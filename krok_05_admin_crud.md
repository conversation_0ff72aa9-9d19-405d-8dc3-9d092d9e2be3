# Krok 5: Administra<PERSON><PERSON><PERSON> r<PERSON>hraní - základní CRUD

## C<PERSON><PERSON> kroku
Implementace administračního rozhraní pro správu katalogů s formuláři a základními CRUD operacemi.

## Co se bude realizovat

### 5.1 AdminCatalogController.php

#### Základ<PERSON><PERSON> akce
- `indexAction()` - listing katalogů
- `newAction()` - formulář pro nový katalog
- `createAction()` - zpracování vytvoření
- `editAction(int $id)` - formulář pro editaci
- `updateAction(int $id)` - zpracování aktualizace
- `deleteAction(int $id)` - s<PERSON><PERSON><PERSON><PERSON> katalogu

#### Pokročilé akce
- `toggleActiveAction(int $id)` - přepnutí aktivní/neaktivní
- `duplicateAction(int $id)` - duplikace katalogu
- `bulkAction()` - hromadné operace
- `configurationAction()` - konfigurace modulu

### 5.2 Symfony formuláře

#### CatalogType.php
```php
class CatalogType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Název katalogu',
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Název je povinný']),
                    new Length(['max' => 255])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Popis',
                'required' => false,
                'attr' => ['rows' => 4]
            ])
            ->add('image', FileType::class, [
                'label' => 'Náhledový obrázek',
                'required' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '2M',
                        'mimeTypes' => ['image/jpeg', 'image/png', 'image/webp'],
                        'mimeTypesMessage' => 'Povolené formáty: JPG, PNG, WebP'
                    ])
                ]
            ])
            ->add('catalog_file', FileType::class, [
                'label' => 'Soubor katalogu',
                'required' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '50M',
                        'mimeTypes' => ['application/pdf', 'application/zip'],
                        'mimeTypesMessage' => 'Povolené formáty: PDF, ZIP'
                    ])
                ]
            ])
            ->add('catalog_url', UrlType::class, [
                'label' => 'Externí URL',
                'required' => false
            ])
            ->add('is_new', CheckboxType::class, [
                'label' => 'Označit jako nový',
                'required' => false
            ])
            ->add('active', CheckboxType::class, [
                'label' => 'Aktivní',
                'required' => false,
                'data' => true
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Uložit'
            ]);
    }
}
```

#### ConfigurationType.php
```php
class ConfigurationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('email_recipients', TextType::class, [
                'label' => 'Email příjemci (oddělené čárkami)',
                'required' => true,
                'help' => 'Zadejte email adresy oddělené čárkami'
            ])
            ->add('email_subject', TextType::class, [
                'label' => 'Předmět emailu',
                'required' => true,
                'data' => 'Nová objednávka katalogu'
            ])
            ->add('page_title', TextType::class, [
                'label' => 'Název stránky',
                'required' => true,
                'data' => 'Naše katalogy'
            ])
            ->add('page_description', TextareaType::class, [
                'label' => 'Popis stránky',
                'required' => false,
                'attr' => ['rows' => 3]
            ])
            ->add('catalogs_per_page', IntegerType::class, [
                'label' => 'Katalogů na stránku',
                'required' => true,
                'data' => 12,
                'constraints' => [
                    new Range(['min' => 1, 'max' => 50])
                ]
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Uložit konfiguraci'
            ]);
    }
}
```

### 5.3 Admin šablony

#### index.html.twig (listing)
```twig
{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="material-icons">folder</i>
            Správa katalogů
        </h3>
        <div class="card-header-toolbar">
            <a href="{{ path('admin_catalog_new') }}" class="btn btn-primary">
                <i class="material-icons">add</i>
                Přidat katalog
            </a>
        </div>
    </div>
    
    <div class="card-body">
        {% if catalogs|length > 0 %}
            <div class="table-responsive">
                <table class="table table-striped" id="catalog-table">
                    <thead>
                        <tr>
                            <th>Pozice</th>
                            <th>Obrázek</th>
                            <th>Název</th>
                            <th>Stav</th>
                            <th>Nový</th>
                            <th>Datum vytvoření</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody id="sortable-catalogs">
                        {% for catalog in catalogs %}
                            <tr data-id="{{ catalog.id_catalog }}">
                                <td class="drag-handle">
                                    <i class="material-icons">drag_indicator</i>
                                    {{ catalog.position }}
                                </td>
                                <td>
                                    {% if catalog.image_path %}
                                        <img src="{{ catalog.image_path }}" alt="{{ catalog.title }}" 
                                             class="img-thumbnail" style="max-width: 50px;">
                                    {% else %}
                                        <span class="text-muted">Bez obrázku</span>
                                    {% endif %}
                                </td>
                                <td>{{ catalog.title }}</td>
                                <td>
                                    <span class="badge badge-{{ catalog.active ? 'success' : 'danger' }}">
                                        {{ catalog.active ? 'Aktivní' : 'Neaktivní' }}
                                    </span>
                                </td>
                                <td>
                                    {% if catalog.is_new %}
                                        <span class="badge badge-info">Nový</span>
                                    {% endif %}
                                </td>
                                <td>{{ catalog.date_add|date('d.m.Y H:i') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ path('admin_catalog_edit', {id: catalog.id_catalog}) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="material-icons">edit</i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-secondary toggle-active" 
                                                data-id="{{ catalog.id_catalog }}">
                                            <i class="material-icons">
                                                {{ catalog.active ? 'visibility_off' : 'visibility' }}
                                            </i>
                                        </button>
                                        <a href="{{ path('admin_catalog_duplicate', {id: catalog.id_catalog}) }}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="material-icons">content_copy</i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger delete-catalog" 
                                                data-id="{{ catalog.id_catalog }}">
                                            <i class="material-icons">delete</i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                <p>Zatím nebyly vytvořeny žádné katalogy.</p>
                <a href="{{ path('admin_catalog_new') }}" class="btn btn-primary">
                    Vytvořit první katalog
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
```

#### form.html.twig (formulář)
```twig
{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="material-icons">{{ catalog.id_catalog ? 'edit' : 'add' }}</i>
            {{ catalog.id_catalog ? 'Upravit katalog' : 'Nový katalog' }}
        </h3>
    </div>
    
    <div class="card-body">
        {{ form_start(form, {'attr': {'enctype': 'multipart/form-data'}}) }}
        
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    {{ form_label(form.title) }}
                    {{ form_widget(form.title, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.title) }}
                </div>
                
                <div class="form-group">
                    {{ form_label(form.description) }}
                    {{ form_widget(form.description, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.description) }}
                </div>
                
                <div class="form-group">
                    {{ form_label(form.catalog_url) }}
                    {{ form_widget(form.catalog_url, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.catalog_url) }}
                    <small class="form-text text-muted">
                        Pokud je vyplněno, bude použito místo nahraného souboru
                    </small>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="form-group">
                    {{ form_label(form.image) }}
                    {{ form_widget(form.image, {'attr': {'class': 'form-control-file'}}) }}
                    {{ form_errors(form.image) }}
                    {% if catalog.image_path %}
                        <div class="mt-2">
                            <img src="{{ catalog.image_path }}" alt="Aktuální obrázek" 
                                 class="img-thumbnail" style="max-width: 200px;">
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form_label(form.catalog_file) }}
                    {{ form_widget(form.catalog_file, {'attr': {'class': 'form-control-file'}}) }}
                    {{ form_errors(form.catalog_file) }}
                    {% if catalog.catalog_file %}
                        <div class="mt-2">
                            <a href="{{ catalog.catalog_file }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="material-icons">download</i>
                                Aktuální soubor
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-check">
                    {{ form_widget(form.is_new, {'attr': {'class': 'form-check-input'}}) }}
                    {{ form_label(form.is_new, null, {'label_attr': {'class': 'form-check-label'}}) }}
                </div>
                
                <div class="form-check">
                    {{ form_widget(form.active, {'attr': {'class': 'form-check-input'}}) }}
                    {{ form_label(form.active, null, {'label_attr': {'class': 'form-check-label'}}) }}
                </div>
            </div>
        </div>
        
        <div class="card-footer">
            {{ form_widget(form.submit, {'attr': {'class': 'btn btn-primary'}}) }}
            <a href="{{ path('admin_catalog_index') }}" class="btn btn-secondary">Zrušit</a>
        </div>
        
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}
```

## Technické detaily

### Validace
- Server-side validace přes Symfony
- Client-side validace přes JavaScript
- File upload validace
- CSRF protection

### UX/UI
- Bootstrap 4 kompatibilita
- Material Design ikony
- Responsive design
- Loading states

### Performance
- Pagination pro velké seznamy
- Lazy loading obrázků
- Optimalizované dotazy

## Výstupy kroku
1. Funkční admin controller
2. Symfony formuláře
3. Admin šablony
4. Základní CRUD operace
5. File upload handling

## Závislosti
- Krok 1-4: Předchozí kroky

## Následující krok
Krok 6: Pokročilé admin funkce - drag&drop a upload
