# Krok 4: Service vrstva a business logika

## <PERSON><PERSON><PERSON> kroku
Vytvoření service vrstvy pro business logiku, správu souborů a email komunikaci.

## Co se bude realizovat

### 4.1 CatalogManager.php

#### Hlavní business logika
- `createCatalog(array $data, array $files): int`
- `updateCatalog(int $id, array $data, array $files): bool`
- `deleteCatalog(int $id): bool`
- `toggleActive(int $id): bool`
- `reorderCatalogs(array $positions): bool`

#### Pokročilé operace
- `duplicateCatalog(int $id): int`
- `bulkDelete(array $ids): bool`
- `bulkToggleActive(array $ids, bool $active): bool`
- `getStatistics(): array`

#### Validace a business rules
- Kontrola unikátnosti názvů
- Validace file formátů
- Pozice management
- Dependency kontroly

### 4.2 FileUploadService.php

#### Upload management
- `uploadImage(UploadedFile $file, string $type = 'catalog'): string`
- `uploadCatalogFile(UploadedFile $file): string`
- `deleteFile(string $path): bool`
- `validateFile(UploadedFile $file, array $allowedTypes): bool`

#### Image optimalizace
```php
class ImageOptimizer
{
    public function optimizeAndResize(string $imagePath): array
    {
        $sizes = [
            'thumbnail' => [300, 200],
            'medium' => [600, 400], 
            'large' => [1200, 800]
        ];
        
        $optimizedImages = [];
        
        foreach ($sizes as $sizeName => $dimensions) {
            $optimizedPath = $this->createResizedImage(
                $imagePath, 
                $dimensions[0], 
                $dimensions[1],
                $sizeName
            );
            $optimizedImages[$sizeName] = $optimizedPath;
        }
        
        return $optimizedImages;
    }
    
    private function createResizedImage(string $source, int $width, int $height, string $suffix): string
    {
        $imageInfo = getimagesize($source);
        $mime = $imageInfo['mime'];
        
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($source);
                break;
            default:
                throw new \InvalidArgumentException('Nepodporovaný formát obrázku');
        }
        
        $resized = imagecreatetruecolor($width, $height);
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $width, $height, imagesx($image), imagesy($image));
        
        $pathInfo = pathinfo($source);
        $newPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $suffix . '.webp';
        
        imagewebp($resized, $newPath, 85);
        
        imagedestroy($image);
        imagedestroy($resized);
        
        return $newPath;
    }
}
```

#### File security
- MIME type validace
- File size limity
- Malware scanning
- Safe filename generation

### 4.3 EmailService.php

#### Email templates
- `sendOrderNotification(array $orderData, array $catalogData): bool`
- `sendOrderConfirmation(array $orderData, array $catalogData): bool`
- `sendAdminNotification(string $type, array $data): bool`

#### Template management
```php
class EmailTemplateManager
{
    public function renderTemplate(string $templateName, array $variables): string
    {
        $templatePath = $this->getTemplatePath($templateName);
        
        if (!file_exists($templatePath)) {
            throw new \InvalidArgumentException("Template {$templateName} not found");
        }
        
        extract($variables);
        ob_start();
        include $templatePath;
        return ob_get_clean();
    }
    
    public function getAvailableTemplates(): array
    {
        return [
            'order_notification' => 'Notifikace o nové objednávce',
            'order_confirmation' => 'Potvrzení objednávky zákazníkovi',
            'admin_alert' => 'Admin upozornění'
        ];
    }
}
```

#### Email konfigurace
- SMTP nastavení
- Template customizace
- Multi-recipient podpora
- Email queue systém

### 4.4 CatalogOrderService.php

#### Objednávky management
- `createOrder(array $orderData): int`
- `validateOrderData(array $data): array`
- `processOrder(int $orderId): bool`
- `getOrderStatistics(): array`

#### Business validace
- IČO validace (český formát)
- Email duplicity kontrola
- Rate limiting
- Spam protection

### 4.5 CacheService.php

#### Cache management
- `get(string $key): mixed`
- `set(string $key, mixed $value, int $ttl = 3600): bool`
- `delete(string $key): bool`
- `clear(string $pattern = '*'): bool`

#### Cache strategie
- Catalog listing cache (1 hodina)
- Configuration cache (24 hodin)
- Image metadata cache (1 týden)
- Statistics cache (30 minut)

## Technické detaily

### Dependency Injection
- Service container integrace
- Interface-based design
- Lazy loading služeb
- Circular dependency prevence

### Error handling
- Structured exceptions
- Logging integrace
- Graceful degradation
- User-friendly error messages

### Performance
- Batch operace
- Memory optimalizace
- Database connection pooling
- Async processing připravenost

### Security
- Input sanitizace
- File upload security
- CSRF protection
- Rate limiting

## Výstupy kroku
1. Kompletní service třídy
2. File upload systém
3. Email template systém
4. Cache mechanismus
5. Business validace

## Závislosti
- Krok 1: Základní struktura
- Krok 2: Databázová struktura a entity
- Krok 3: Repository pattern

## Následující krok
Krok 5: Administrační rozhraní - základní CRUD
