# Krok 7: Frontend stránka katalogů

## <PERSON><PERSON><PERSON> frontend stránky pro zobrazení katalogů s responzivním designem a uživatelsky přívětivým rozhraním.

## Co se bude realizovat

### 7.1 Frontend Controller

#### CatalogController.php
```php
<?php

namespace CigCatalog\Controller\Front;

use PrestaShopBundle\Controller\Front\FrontController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use CigCatalog\Service\CatalogManager;
use CigCatalog\Repository\CatalogRepository;

class CatalogController extends FrontController
{
    private CatalogManager $catalogManager;
    private CatalogRepository $catalogRepository;
    
    public function __construct(
        CatalogManager $catalogManager,
        CatalogRepository $catalogRepository
    ) {
        $this->catalogManager = $catalogManager;
        $this->catalogRepository = $catalogRepository;
    }
    
    public function indexAction(Request $request): Response
    {
        $page = max(1, $request->query->getInt('page', 1));
        $perPage = $this->getConfiguration('CATALOG_PER_PAGE', 12);
        $langId = $this->context->language->id;
        
        // Získání katalogů s paginací
        $catalogs = $this->catalogRepository->findActiveWithPagination($page, $perPage, $langId);
        $totalCatalogs = $this->catalogRepository->countActive($langId);
        $totalPages = ceil($totalCatalogs / $perPage);
        
        // Konfigurace stránky
        $pageTitle = $this->getConfiguration('CATALOG_PAGE_TITLE', 'Naše katalogy');
        $pageDescription = $this->getConfiguration('CATALOG_PAGE_DESCRIPTION', '');
        
        return $this->render('@Modules/cig_catalog/views/templates/front/catalog/index.html.twig', [
            'catalogs' => $catalogs,
            'page_title' => $pageTitle,
            'page_description' => $pageDescription,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $totalCatalogs,
                'per_page' => $perPage
            ]
        ]);
    }
    
    public function downloadAction(Request $request, int $id): Response
    {
        $langId = $this->context->language->id;
        $catalog = $this->catalogRepository->findById($id, $langId);
        
        if (!$catalog || !$catalog['active']) {
            throw $this->createNotFoundException('Katalog nebyl nalezen');
        }
        
        // Log stažení pro statistiky
        $this->catalogManager->logDownload($id, $request->getClientIp());
        
        // Pokud je nastavena externí URL, přesměruj
        if (!empty($catalog['catalog_url'])) {
            return $this->redirect($catalog['catalog_url']);
        }
        
        // Jinak stáhni lokální soubor
        if (!empty($catalog['catalog_file']) && file_exists($catalog['catalog_file'])) {
            return $this->file($catalog['catalog_file']);
        }
        
        throw $this->createNotFoundException('Soubor katalogu nebyl nalezen');
    }
}
```

### 7.2 Frontend šablony

#### index.html.twig (hlavní stránka)
```twig
{# views/templates/front/catalog/index.html.twig #}
{% extends 'page.tpl' %}

{% block content %}
<div class="catalog-page">
    <div class="container">
        <div class="catalog-header">
            <h1 class="page-title">{{ page_title }}</h1>
            {% if page_description %}
                <div class="catalog-intro">
                    <p>{{ page_description }}</p>
                </div>
            {% endif %}
        </div>
        
        {% if catalogs|length > 0 %}
            <div class="catalog-grid">
                <div class="row">
                    {% for catalog in catalogs %}
                        <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
                            <div class="catalog-item {{ catalog.is_new ? 'catalog-new' : '' }}">
                                {% if catalog.is_new %}
                                    <div class="catalog-badge">
                                        <span class="badge badge-primary">NOVINKA</span>
                                    </div>
                                {% endif %}
                                
                                <div class="catalog-image">
                                    {% if catalog.image_path %}
                                        <img src="{{ catalog.image_path }}" 
                                             alt="{{ catalog.title }}" 
                                             class="img-fluid catalog-thumbnail"
                                             loading="lazy">
                                    {% else %}
                                        <div class="catalog-placeholder">
                                            <i class="material-icons">folder</i>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="catalog-content">
                                    <h3 class="catalog-title">{{ catalog.title }}</h3>
                                    {% if catalog.description %}
                                        <p class="catalog-description">{{ catalog.description|truncate(150) }}</p>
                                    {% endif %}
                                </div>
                                
                                <div class="catalog-actions">
                                    <a href="{{ path('catalog_download', {id: catalog.id_catalog}) }}" 
                                       class="btn btn-primary btn-download"
                                       target="_blank">
                                        <i class="material-icons">download</i>
                                        Stáhnout
                                    </a>
                                    <button class="btn btn-outline-primary btn-order" 
                                            data-catalog-id="{{ catalog.id_catalog }}"
                                            data-catalog-title="{{ catalog.title }}">
                                        <i class="material-icons">shopping_cart</i>
                                        Objednat
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            {# Paginace #}
            {% if pagination.total_pages > 1 %}
                <nav aria-label="Stránkování katalogů" class="catalog-pagination">
                    <ul class="pagination justify-content-center">
                        {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ path('catalog_index', {page: pagination.current_page - 1}) }}">
                                    <i class="material-icons">chevron_left</i>
                                    Předchozí
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page in range(max(1, pagination.current_page - 2), min(pagination.total_pages, pagination.current_page + 2)) %}
                            <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                <a class="page-link" href="{{ path('catalog_index', {page: page}) }}">{{ page }}</a>
                            </li>
                        {% endfor %}
                        
                        {% if pagination.current_page < pagination.total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ path('catalog_index', {page: pagination.current_page + 1}) }}">
                                    Další
                                    <i class="material-icons">chevron_right</i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="catalog-empty">
                <div class="text-center">
                    <i class="material-icons catalog-empty-icon">folder_open</i>
                    <h3>Zatím nejsou k dispozici žádné katalogy</h3>
                    <p>Katalogy budou brzy dostupné. Zkuste to prosím později.</p>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block javascript %}
    {{ parent() }}
    <script src="{{ asset('modules/cig_catalog/views/js/catalog-frontend.js') }}"></script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('modules/cig_catalog/views/css/catalog-frontend.css') }}">
{% endblock %}
```

### 7.3 CSS styly

#### catalog-frontend.css
```css
/* Hlavní kontejner */
.catalog-page {
    padding: 2rem 0;
}

.catalog-header {
    text-align: center;
    margin-bottom: 3rem;
}

.catalog-header .page-title {
    font-size: 2.5rem;
    font-weight: 300;
    color: #333;
    margin-bottom: 1rem;
}

.catalog-intro {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Grid katalogů */
.catalog-grid {
    margin-bottom: 3rem;
}

.catalog-item {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.catalog-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.catalog-item.catalog-new {
    border: 2px solid #007bff;
}

/* Badge pro nové katalogy */
.catalog-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.catalog-badge .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* Obrázek katalogu */
.catalog-image {
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.catalog-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.catalog-item:hover .catalog-thumbnail {
    transform: scale(1.05);
}

.catalog-placeholder {
    color: #dee2e6;
    font-size: 3rem;
}

/* Obsah katalogu */
.catalog-content {
    padding: 1.5rem;
    flex-grow: 1;
}

.catalog-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.catalog-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0;
}

/* Akce */
.catalog-actions {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    display: flex;
    gap: 0.5rem;
    margin-top: auto;
}

.catalog-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.btn-download {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-download:hover {
    background: #218838;
    border-color: #1e7e34;
    color: white;
    text-decoration: none;
}

.btn-order {
    background: transparent;
    border-color: #007bff;
    color: #007bff;
}

.btn-order:hover {
    background: #007bff;
    color: white;
}

/* Prázdný stav */
.catalog-empty {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.catalog-empty-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

/* Paginace */
.catalog-pagination {
    margin-top: 3rem;
}

.catalog-pagination .page-link {
    color: #007bff;
    border-color: #dee2e6;
    padding: 0.75rem 1rem;
}

.catalog-pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.catalog-pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Responzivní design */
@media (max-width: 768px) {
    .catalog-header .page-title {
        font-size: 2rem;
    }
    
    .catalog-actions {
        flex-direction: column;
    }
    
    .catalog-actions .btn {
        margin-bottom: 0.5rem;
    }
    
    .catalog-actions .btn:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 576px) {
    .catalog-page {
        padding: 1rem 0;
    }
    
    .catalog-header {
        margin-bottom: 2rem;
    }
    
    .catalog-image {
        height: 150px;
    }
    
    .catalog-content {
        padding: 1rem;
    }
    
    .catalog-actions {
        padding: 1rem;
    }
}

/* Loading states */
.catalog-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

.catalog-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

### 7.4 JavaScript funkcionalita

#### catalog-frontend.js
```javascript
// Frontend funkcionalita pro katalogy
class CatalogFrontend {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupOrderButtons();
        this.setupLazyLoading();
        this.setupDownloadTracking();
    }
    
    setupOrderButtons() {
        const orderButtons = document.querySelectorAll('.btn-order');
        orderButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const catalogId = button.dataset.catalogId;
                const catalogTitle = button.dataset.catalogTitle;
                this.openOrderModal(catalogId, catalogTitle);
            });
        });
    }
    
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });
            
            const lazyImages = document.querySelectorAll('img[data-src]');
            lazyImages.forEach(img => imageObserver.observe(img));
        }
    }
    
    setupDownloadTracking() {
        const downloadButtons = document.querySelectorAll('.btn-download');
        downloadButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Analytics tracking
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'download', {
                        'event_category': 'catalog',
                        'event_label': button.closest('.catalog-item').querySelector('.catalog-title').textContent
                    });
                }
            });
        });
    }
    
    openOrderModal(catalogId, catalogTitle) {
        // Tato funkce bude implementována v kroku 8
        console.log('Opening order modal for catalog:', catalogId, catalogTitle);
    }
}

// Inicializace po načtení DOM
document.addEventListener('DOMContentLoaded', () => {
    new CatalogFrontend();
});
```

## Technické detaily

### SEO optimalizace
- Semantic HTML struktura
- Meta tags pro katalogy
- Structured data markup
- Optimalizované URL

### Performance
- Lazy loading obrázků
- CSS/JS minifikace
- Image optimization
- Caching headers

### Accessibility
- ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance

### Responzivní design
- Mobile-first přístup
- Flexbox/Grid layout
- Touch-friendly interface
- Optimalizace pro různé velikosti

## Výstupy kroku
1. Frontend controller
2. Responzivní šablony
3. CSS styly
4. JavaScript funkcionalita
5. SEO optimalizace

## Závislosti
- Krok 1-6: Předchozí kroky

## Následující krok
Krok 8: Objednávkový systém a modal formulář
