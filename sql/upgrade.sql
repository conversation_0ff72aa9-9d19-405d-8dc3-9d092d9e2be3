-- CIG Katalogy Module - Upgrade Script
-- PrestaShop 8.2.0 Compatible
-- Created: 2025-06-18

-- This file contains upgrade scripts for different versions
-- Each upgrade should be wrapped in version checks

-- Upgrade from version 1.0.0 to 1.1.0
-- Example upgrade script structure

-- Check if upgrade is needed
SET @current_version = (SELECT `value` FROM `PREFIX_cig_catalog_config` WHERE `name` = 'CIG_CATALOG_VERSION' LIMIT 1);

-- Version 1.1.0 upgrades
-- Add new configuration options if they don't exist
INSERT IGNORE INTO `PREFIX_cig_catalog_config` (`name`, `value`, `date_add`, `date_upd`) VALUES
('CIG_CATALOG_CACHE_ENABLED', '1', NOW(), NOW()),
('CIG_CATALOG_CACHE_LIFETIME', '3600', NOW(), NOW()),
('CIG_CATALOG_SEO_FRIENDLY_URLS', '1', NOW(), NOW());

-- Add new indexes for better performance (if they don't exist)
-- Check if index exists before creating
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE table_schema = DATABASE() 
    AND table_name = CONCAT((SELECT REPLACE(@@sql_mode, 'TRADITIONAL', '') FROM DUAL), 'cig_catalog') 
    AND index_name = 'idx_title_active');

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `PREFIX_cig_catalog` ADD INDEX `idx_title_active` (`title`, `active`)', 
    'SELECT "Index idx_title_active already exists"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add new column for catalog categories (example future upgrade)
-- SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
--     WHERE table_schema = DATABASE() 
--     AND table_name = 'PREFIX_cig_catalog' 
--     AND column_name = 'id_category');
-- 
-- SET @sql = IF(@column_exists = 0, 
--     'ALTER TABLE `PREFIX_cig_catalog` ADD COLUMN `id_category` int(11) DEFAULT NULL AFTER `id_catalog`', 
--     'SELECT "Column id_category already exists"');
-- 
-- PREPARE stmt FROM @sql;
-- EXECUTE stmt;
-- DEALLOCATE PREPARE stmt;

-- Update version number
INSERT INTO `PREFIX_cig_catalog_config` (`name`, `value`, `date_add`, `date_upd`) VALUES
('CIG_CATALOG_VERSION', '1.1.0', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value` = '1.1.0', `date_upd` = NOW();

-- Data migration example
-- Update any existing data if needed
-- UPDATE `PREFIX_cig_catalog` SET `position` = `id_catalog` WHERE `position` = 0;

-- Clean up old configuration values (example)
-- DELETE FROM `PREFIX_cig_catalog_config` WHERE `name` = 'OLD_CONFIG_NAME';

-- Integrity checks
-- Ensure all catalogs have at least one language entry
INSERT INTO `PREFIX_cig_catalog_lang` (`id_catalog`, `id_lang`, `title`, `description`)
SELECT 
    c.id_catalog,
    (SELECT id_lang FROM `PREFIX_lang` WHERE active = 1 ORDER BY id_lang LIMIT 1) as id_lang,
    c.title,
    c.description
FROM `PREFIX_cig_catalog` c
WHERE NOT EXISTS (
    SELECT 1 FROM `PREFIX_cig_catalog_lang` cl 
    WHERE cl.id_catalog = c.id_catalog
);

-- Log upgrade completion
INSERT INTO `PREFIX_cig_catalog_config` (`name`, `value`, `date_add`, `date_upd`) VALUES
(CONCAT('CIG_CATALOG_UPGRADE_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s')), 'completed', NOW(), NOW());
