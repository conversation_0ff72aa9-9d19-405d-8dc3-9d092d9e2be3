-- CIG Katalogy Module - <PERSON><PERSON>ript
-- PrestaShop 8.2.0 Compatible
-- Created: 2025-06-18

-- This script restores data from backup tables
-- IMPORTANT: Replace 'BACKUP_SUFFIX' with actual backup suffix (e.g., '_backup_20250618_143022')

-- WARNING: This will overwrite current data!
-- Make sure you have the correct backup suffix before running this script.

-- Example usage:
-- Replace all occurrences of 'BACKUP_SUFFIX' with your actual backup suffix
-- SET @backup_suffix = '_backup_20250618_143022';

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear current data
TRUNCATE TABLE `PREFIX_cig_catalog_order`;
TRUNCATE TABLE `PREFIX_cig_catalog_lang`;
DELETE FROM `PREFIX_cig_catalog_config` WHERE `name` NOT LIKE '%BACKUP_%';
TRUNCATE TABLE `PREFIX_cig_catalog`;

-- Restore data from backup tables
-- Note: Replace BACKUP_SUFFIX with actual backup suffix

-- <PERSON>ore catalogs
-- INSERT INTO `PREFIX_cig_catalog` SELECT * FROM `PREFIX_cig_catalogBACKUP_SUFFIX`;

-- Restore multilingual data
-- INSERT INTO `PREFIX_cig_catalog_lang` SELECT * FROM `PREFIX_cig_catalog_langBACKUP_SUFFIX`;

-- Restore orders
-- INSERT INTO `PREFIX_cig_catalog_order` SELECT * FROM `PREFIX_cig_catalog_orderBACKUP_SUFFIX`;

-- Restore configuration (excluding backup logs)
-- INSERT INTO `PREFIX_cig_catalog_config` 
-- SELECT * FROM `PREFIX_cig_catalog_configBACKUP_SUFFIX` 
-- WHERE `name` NOT LIKE '%BACKUP_%';

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Log restore completion
INSERT INTO `PREFIX_cig_catalog_config` (`name`, `value`, `date_add`, `date_upd`) VALUES
(CONCAT('CIG_CATALOG_RESTORE_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s')), 'completed', NOW(), NOW());

-- Instructions for manual execution:
-- 1. Find your backup suffix by running:
--    SELECT `name` FROM `PREFIX_cig_catalog_config` WHERE `name` LIKE '%BACKUP_%' ORDER BY `date_add` DESC;
-- 
-- 2. Replace BACKUP_SUFFIX in the commented INSERT statements above with your actual suffix
-- 
-- 3. Uncomment and execute the INSERT statements
-- 
-- 4. Verify data integrity after restore
