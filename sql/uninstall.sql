-- CIG Katalogy Module - Uninstallation Script
-- PrestaShop 8.2.0 Compatible
-- Created: 2025-06-18

-- WARNING: This script will permanently delete all catalog data!
-- Make sure to backup your data before running this script.

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Drop foreign key constraints first
ALTER TABLE `PREFIX_cig_catalog_lang` DROP FOREIGN KEY IF EXISTS `fk_cig_catalog_lang_catalog`;
ALTER TABLE `PREFIX_cig_catalog_lang` DROP FOREIGN KEY IF EXISTS `fk_cig_catalog_lang_lang`;
ALTER TABLE `PREFIX_cig_catalog_order` DROP FOREIGN KEY IF EXISTS `fk_cig_catalog_order_catalog`;

-- Drop tables in correct order (child tables first)
DROP TABLE IF EXISTS `PREFIX_cig_catalog_order`;
DROP TABLE IF EXISTS `PREFIX_cig_catalog_lang`;
DROP TABLE IF EXISTS `PREFIX_cig_catalog_config`;
DROP TABLE IF EXISTS `PREFIX_cig_catalog`;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Clean up any remaining configuration values in PrestaShop configuration table
DELETE FROM `PREFIX_configuration` WHERE `name` LIKE 'CIG_CATALOG_%';

-- Clean up any hooks that might have been registered
DELETE FROM `PREFIX_hook_module` WHERE `id_module` IN (
    SELECT `id_module` FROM `PREFIX_module` WHERE `name` = 'cigkatalogy'
);

-- Note: Module files and uploaded catalog files need to be removed manually
-- from the filesystem. This includes:
-- - /modules/cigkatalogy/ directory
-- - /upload/cigkatalogy/ directory (if exists)
-- - Any cached files related to the module
