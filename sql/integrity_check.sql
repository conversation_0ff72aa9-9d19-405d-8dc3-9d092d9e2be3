-- CIG Katalogy Module - Data Integrity Check Script
-- PrestaShop 8.2.0 Compatible
-- Created: 2025-06-18

-- This script checks data integrity and reports any issues

-- Check 1: Catalogs without multilingual data
SELECT 
    'Catalogs without multilingual data' as check_name,
    COUNT(*) as issue_count,
    GROUP_CONCAT(c.id_catalog) as affected_ids
FROM `PREFIX_cig_catalog` c
LEFT JOIN `PREFIX_cig_catalog_lang` cl ON c.id_catalog = cl.id_catalog
WHERE cl.id_catalog IS NULL;

-- Check 2: Multilingual data without parent catalog
SELECT 
    'Orphaned multilingual data' as check_name,
    COUNT(*) as issue_count,
    GROUP_CONCAT(CONCAT(cl.id_catalog, ':', cl.id_lang)) as affected_ids
FROM `PREFIX_cig_catalog_lang` cl
LEFT JOIN `PREFIX_cig_catalog` c ON cl.id_catalog = c.id_catalog
WHERE c.id_catalog IS NULL;

-- Check 3: Orders without valid catalog reference
SELECT 
    'Orders with invalid catalog reference' as check_name,
    COUNT(*) as issue_count,
    GROUP_CONCAT(co.id_order) as affected_ids
FROM `PREFIX_cig_catalog_order` co
LEFT JOIN `PREFIX_cig_catalog` c ON co.id_catalog = c.id_catalog
WHERE c.id_catalog IS NULL;

-- Check 4: Multilingual data with invalid language reference
SELECT 
    'Multilingual data with invalid language' as check_name,
    COUNT(*) as issue_count,
    GROUP_CONCAT(CONCAT(cl.id_catalog, ':', cl.id_lang)) as affected_ids
FROM `PREFIX_cig_catalog_lang` cl
LEFT JOIN `PREFIX_lang` l ON cl.id_lang = l.id_lang
WHERE l.id_lang IS NULL;

-- Check 5: Duplicate positions
SELECT 
    'Catalogs with duplicate positions' as check_name,
    COUNT(*) - COUNT(DISTINCT position) as issue_count,
    GROUP_CONCAT(position) as affected_positions
FROM `PREFIX_cig_catalog`
WHERE active = 1
HAVING issue_count > 0;

-- Check 6: Missing required configuration
SELECT 
    'Missing required configuration' as check_name,
    (20 - COUNT(*)) as issue_count,
    GROUP_CONCAT(required_configs.config_name) as missing_configs
FROM (
    SELECT 'CIG_CATALOG_ENABLED' as config_name
    UNION SELECT 'CIG_CATALOG_ITEMS_PER_PAGE'
    UNION SELECT 'CIG_CATALOG_ALLOW_ORDERS'
    UNION SELECT 'CIG_CATALOG_REQUIRE_COMPANY'
    UNION SELECT 'CIG_CATALOG_ADMIN_EMAIL'
    UNION SELECT 'CIG_CATALOG_EMAIL_TEMPLATE'
    UNION SELECT 'CIG_CATALOG_UPLOAD_MAX_SIZE'
    UNION SELECT 'CIG_CATALOG_ALLOWED_EXTENSIONS'
    UNION SELECT 'CIG_CATALOG_IMAGE_WIDTH'
    UNION SELECT 'CIG_CATALOG_IMAGE_HEIGHT'
    UNION SELECT 'CIG_CATALOG_THUMBNAIL_WIDTH'
    UNION SELECT 'CIG_CATALOG_THUMBNAIL_HEIGHT'
    UNION SELECT 'CIG_CATALOG_SHOW_NEW_BADGE'
    UNION SELECT 'CIG_CATALOG_NEW_DAYS'
    UNION SELECT 'CIG_CATALOG_META_TITLE'
    UNION SELECT 'CIG_CATALOG_META_DESCRIPTION'
    UNION SELECT 'CIG_CATALOG_BREADCRUMB_ENABLED'
    UNION SELECT 'CIG_CATALOG_PAGINATION_ENABLED'
    UNION SELECT 'CIG_CATALOG_SEARCH_ENABLED'
    UNION SELECT 'CIG_CATALOG_SORT_DEFAULT'
) required_configs
LEFT JOIN `PREFIX_cig_catalog_config` cc ON required_configs.config_name = cc.name
WHERE cc.name IS NULL;

-- Check 7: Invalid email addresses in orders
SELECT 
    'Orders with invalid email addresses' as check_name,
    COUNT(*) as issue_count,
    GROUP_CONCAT(id_order) as affected_ids
FROM `PREFIX_cig_catalog_order`
WHERE email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';

-- Check 8: Catalogs with invalid file paths
SELECT 
    'Catalogs with potentially invalid file paths' as check_name,
    COUNT(*) as issue_count,
    GROUP_CONCAT(id_catalog) as affected_ids
FROM `PREFIX_cig_catalog`
WHERE (catalog_file IS NOT NULL AND catalog_file != '' AND catalog_file NOT REGEXP '\.(pdf|doc|docx)$')
   OR (image_path IS NOT NULL AND image_path != '' AND image_path NOT REGEXP '\.(jpg|jpeg|png|gif)$');

-- Summary report
SELECT 
    'INTEGRITY CHECK SUMMARY' as report_title,
    (SELECT COUNT(*) FROM `PREFIX_cig_catalog`) as total_catalogs,
    (SELECT COUNT(*) FROM `PREFIX_cig_catalog_lang`) as total_translations,
    (SELECT COUNT(*) FROM `PREFIX_cig_catalog_order`) as total_orders,
    (SELECT COUNT(*) FROM `PREFIX_cig_catalog_config`) as total_configs,
    NOW() as check_date;
