-- CIG Katalogy Module - Backup Script
-- PrestaShop 8.2.0 Compatible
-- Created: 2025-06-18

-- This script creates backup tables for all CIG Katalogy data
-- Use before major upgrades or before uninstalling the module

-- Create backup tables with timestamp
SET @backup_suffix = DATE_FORMAT(NOW(), '_backup_%Y%m%d_%H%i%s');

-- Backup main catalog table
SET @sql = CONCAT('CREATE TABLE `PREFIX_cig_catalog', @backup_suffix, '` AS SELECT * FROM `PREFIX_cig_catalog`');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Backup multilingual table
SET @sql = CONCAT('CREATE TABLE `PREFIX_cig_catalog_lang', @backup_suffix, '` AS SELECT * FROM `PREFIX_cig_catalog_lang`');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Backup orders table
SET @sql = CONCAT('CREATE TABLE `PREFIX_cig_catalog_order', @backup_suffix, '` AS SELECT * FROM `PREFIX_cig_catalog_order`');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Backup configuration table
SET @sql = CONCAT('CREATE TABLE `PREFIX_cig_catalog_config', @backup_suffix, '` AS SELECT * FROM `PREFIX_cig_catalog_config`');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Log backup creation
INSERT INTO `PREFIX_cig_catalog_config` (`name`, `value`, `date_add`, `date_upd`) VALUES
(CONCAT('CIG_CATALOG_BACKUP_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s')), 'created', NOW(), NOW());

-- Show backup tables created
SELECT CONCAT('Backup completed. Tables created with suffix: ', @backup_suffix) as backup_info;
