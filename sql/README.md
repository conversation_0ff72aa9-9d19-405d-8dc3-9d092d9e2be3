# CIG Katalogy - SQL Scripts

Tento adresář obsahuje všechny SQL skripty pro modul CIG Katalogy.

## Přehled souborů

### Základn<PERSON> skripty

#### `install.sql`
- **Účel**: Instalace modulu - vytvoření všech databázových tabulek
- **Kdy pou<PERSON>**: Při první instalaci modulu
- **Obsahuje**:
  - Vytvoření všech tabulek s správnými indexy
  - Nastavení foreign key constraints
  - Vložení výchozích konfiguračních hodnot
  - Vytvoření ukázkového katalogu pro testování

#### `uninstall.sql`
- **Účel**: Kompletní odstranění modulu z databáze
- **Kdy použít**: Při odinstalaci modulu
- **Varování**: ⚠️ Permanentně smaže všechna data!
- **Obsahuje**:
  - Odstranění všech tabulek modulu
  - Vyčištění konfiguračních hodnot
  - Odstranění hook registrací

### Migrace a aktualizace

#### `upgrade.sql`
- **Účel**: Aktualizace mezi verzemi modulu
- **Kdy použít**: Při upgrade na novější verzi
- **Obsahuje**:
  - Verze kontroly
  - Postupné migrace databázové struktury
  - Aktualizace konfiguračních hodnot
  - Data integrity kontroly

### Backup a obnovení

#### `backup.sql`
- **Účel**: Vytvoření zálohy všech dat modulu
- **Kdy použít**: Před upgrade nebo před rizikovými operacemi
- **Vytváří**: Backup tabulky s časovým razítkem
- **Formát**: `PREFIX_cig_catalog_backup_YYYYMMDD_HHMMSS`

#### `restore.sql`
- **Účel**: Obnovení dat ze zálohy
- **Kdy použít**: Po neúspěšném upgrade nebo při potřebě rollback
- **Varování**: ⚠️ Přepíše současná data!
- **Poznámka**: Vyžaduje manuální úpravu backup suffixu

### Diagnostika

#### `integrity_check.sql`
- **Účel**: Kontrola integrity a konzistence dat
- **Kdy použít**: Pravidelně nebo při podezření na problémy
- **Kontroluje**:
  - Orphaned záznamy
  - Chybějící multilingual data
  - Neplatné reference
  - Duplicitní pozice
  - Chybějící konfigurace
  - Neplatné email adresy
  - Neplatné cesty k souborům

## Použití

### Instalace modulu
```sql
-- Spusťte install.sql
-- PREFIX_ bude automaticky nahrazeno PrestaShop prefixem
```

### Před upgrade
```sql
-- 1. Vytvořte backup
SOURCE backup.sql;

-- 2. Spusťte upgrade
SOURCE upgrade.sql;

-- 3. Zkontrolujte integritu
SOURCE integrity_check.sql;
```

### V případě problémů
```sql
-- 1. Najděte backup suffix
SELECT `name` FROM `PREFIX_cig_catalog_config` 
WHERE `name` LIKE '%BACKUP_%' 
ORDER BY `date_add` DESC;

-- 2. Upravte restore.sql s nalezený suffixem
-- 3. Spusťte restore.sql
```

### Pravidelná údržba
```sql
-- Kontrola integrity (doporučeno měsíčně)
SOURCE integrity_check.sql;
```

## Databázová struktura

### Hlavní tabulky

1. **`cig_catalog`** - Hlavní katalogy
   - `id_catalog` - Primární klíč
   - `title`, `description` - Základní informace
   - `image_path`, `catalog_url`, `catalog_file` - Soubory
   - `is_new`, `position`, `active` - Stav a řazení
   - `date_add`, `date_upd` - Časová razítka

2. **`cig_catalog_lang`** - Multilingual data
   - `id_catalog`, `id_lang` - Kompozitní klíč
   - `title`, `description` - Přeložené texty

3. **`cig_catalog_order`** - Objednávky katalogů
   - `id_order` - Primární klíč
   - `id_catalog` - Reference na katalog
   - Kontaktní údaje zákazníka
   - `date_add` - Datum objednávky

4. **`cig_catalog_config`** - Konfigurace modulu
   - `name`, `value` - Konfigurační páry
   - Všechny nastavení modulu

### Indexy a optimalizace

- Primární klíče na všech tabulkách
- Foreign key constraints pro referenční integritu
- Indexy na často používané sloupce (position, active, date_add)
- UTF8MB4 charset pro emoji podporu
- InnoDB engine pro transakce

## Bezpečnostní poznámky

1. **Vždy vytvořte backup** před jakýmkoliv upgrade
2. **Testujte na staging prostředí** před nasazením na produkci
3. **Kontrolujte integrity** po každé významné změně
4. **Používejte transakce** pro kritické operace
5. **Monitorujte velikost** backup souborů

## Troubleshooting

### Časté problémy

1. **Foreign key chyby**
   - Zkontrolujte pořadí spouštění skriptů
   - Ujistěte se, že parent tabulky existují

2. **Duplicate key chyby**
   - Zkontrolujte existující data před upgrade
   - Použijte `INSERT IGNORE` nebo `ON DUPLICATE KEY UPDATE`

3. **Missing table chyby**
   - Ověřte, že install.sql byl úspěšně spuštěn
   - Zkontrolujte databázové oprávnění

4. **Encoding problémy**
   - Ujistěte se, že používáte UTF8MB4
   - Zkontrolujte collation nastavení

### Kontakt
Pro technickou podporu kontaktujte vývojáře modulu.
