-- CIG Katalogy Module - Installation Script
-- PrestaShop 8.2.0 Compatible
-- Created: 2025-06-18

-- Create main catalog table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog` (
    `id_catalog` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `description` text,
    `image_path` varchar(500),
    `catalog_url` varchar(500),
    `catalog_file` varchar(500),
    `is_new` tinyint(1) DEFAULT 0,
    `position` int(11) DEFAULT 0,
    `active` tinyint(1) DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_catalog`),
    KEY `position` (`position`),
    KEY `active` (`active`),
    KEY `is_new` (`is_new`),
    KEY `date_add` (`date_add`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create multilingual catalog table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog_lang` (
    `id_catalog` int(11) NOT NULL,
    `id_lang` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `description` text,
    PRIMARY KEY (`id_catalog`, `id_lang`),
    KEY `id_lang` (`id_lang`),
    KEY `id_catalog` (`id_catalog`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create catalog orders table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog_order` (
    `id_order` int(11) NOT NULL AUTO_INCREMENT,
    `id_catalog` int(11) NOT NULL,
    `company_name` varchar(255),
    `company_id` varchar(50),
    `first_name` varchar(100) NOT NULL,
    `last_name` varchar(100) NOT NULL,
    `email` varchar(255) NOT NULL,
    `phone` varchar(50),
    `address` text NOT NULL,
    `note` text,
    `date_add` datetime NOT NULL,
    PRIMARY KEY (`id_order`),
    KEY `id_catalog` (`id_catalog`),
    KEY `email` (`email`),
    KEY `date_add` (`date_add`),
    KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create configuration table
CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog_config` (
    `id_config` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `value` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_config`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraints
ALTER TABLE `PREFIX_cig_catalog_lang` 
ADD CONSTRAINT `fk_cig_catalog_lang_catalog` 
FOREIGN KEY (`id_catalog`) REFERENCES `PREFIX_cig_catalog` (`id_catalog`) ON DELETE CASCADE;

ALTER TABLE `PREFIX_cig_catalog_lang` 
ADD CONSTRAINT `fk_cig_catalog_lang_lang` 
FOREIGN KEY (`id_lang`) REFERENCES `PREFIX_lang` (`id_lang`) ON DELETE CASCADE;

ALTER TABLE `PREFIX_cig_catalog_order` 
ADD CONSTRAINT `fk_cig_catalog_order_catalog` 
FOREIGN KEY (`id_catalog`) REFERENCES `PREFIX_cig_catalog` (`id_catalog`) ON DELETE CASCADE;

-- Insert default configuration values
INSERT INTO `PREFIX_cig_catalog_config` (`name`, `value`, `date_add`, `date_upd`) VALUES
('CIG_CATALOG_ENABLED', '1', NOW(), NOW()),
('CIG_CATALOG_ITEMS_PER_PAGE', '12', NOW(), NOW()),
('CIG_CATALOG_ALLOW_ORDERS', '1', NOW(), NOW()),
('CIG_CATALOG_REQUIRE_COMPANY', '0', NOW(), NOW()),
('CIG_CATALOG_ADMIN_EMAIL', '', NOW(), NOW()),
('CIG_CATALOG_EMAIL_TEMPLATE', 'catalog_order', NOW(), NOW()),
('CIG_CATALOG_UPLOAD_MAX_SIZE', '10485760', NOW(), NOW()),
('CIG_CATALOG_ALLOWED_EXTENSIONS', 'pdf,doc,docx,jpg,jpeg,png', NOW(), NOW()),
('CIG_CATALOG_IMAGE_WIDTH', '300', NOW(), NOW()),
('CIG_CATALOG_IMAGE_HEIGHT', '200', NOW(), NOW()),
('CIG_CATALOG_THUMBNAIL_WIDTH', '150', NOW(), NOW()),
('CIG_CATALOG_THUMBNAIL_HEIGHT', '100', NOW(), NOW()),
('CIG_CATALOG_SHOW_NEW_BADGE', '1', NOW(), NOW()),
('CIG_CATALOG_NEW_DAYS', '30', NOW(), NOW()),
('CIG_CATALOG_META_TITLE', 'Katalogy', NOW(), NOW()),
('CIG_CATALOG_META_DESCRIPTION', 'Prohlížejte a objednávejte naše katalogy', NOW(), NOW()),
('CIG_CATALOG_BREADCRUMB_ENABLED', '1', NOW(), NOW()),
('CIG_CATALOG_PAGINATION_ENABLED', '1', NOW(), NOW()),
('CIG_CATALOG_SEARCH_ENABLED', '1', NOW(), NOW()),
('CIG_CATALOG_SORT_DEFAULT', 'position', NOW(), NOW());

-- Insert sample catalog for testing (only if no catalogs exist)
INSERT INTO `PREFIX_cig_catalog` (`title`, `description`, `is_new`, `position`, `active`, `date_add`, `date_upd`)
SELECT 'Ukázkový katalog', 'Toto je ukázkový katalog pro testování modulu', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `PREFIX_cig_catalog` LIMIT 1);

-- Insert multilingual data for sample catalog
INSERT INTO `PREFIX_cig_catalog_lang` (`id_catalog`, `id_lang`, `title`, `description`)
SELECT 
    c.id_catalog,
    l.id_lang,
    CASE 
        WHEN l.iso_code = 'cs' THEN 'Ukázkový katalog'
        WHEN l.iso_code = 'en' THEN 'Sample Catalog'
        WHEN l.iso_code = 'sk' THEN 'Ukážkový katalóg'
        ELSE 'Ukázkový katalog'
    END as title,
    CASE 
        WHEN l.iso_code = 'cs' THEN 'Toto je ukázkový katalog pro testování modulu CIG Katalogy'
        WHEN l.iso_code = 'en' THEN 'This is a sample catalog for testing the CIG Catalogs module'
        WHEN l.iso_code = 'sk' THEN 'Toto je ukážkový katalóg pre testovanie modulu CIG Katalógy'
        ELSE 'Toto je ukázkový katalog pro testování modulu CIG Katalogy'
    END as description
FROM `PREFIX_cig_catalog` c
CROSS JOIN `PREFIX_lang` l
WHERE c.title = 'Ukázkový katalog'
AND NOT EXISTS (
    SELECT 1 FROM `PREFIX_cig_catalog_lang` cl 
    WHERE cl.id_catalog = c.id_catalog AND cl.id_lang = l.id_lang
);
