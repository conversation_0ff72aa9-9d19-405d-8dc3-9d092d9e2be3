<?php
/**
 * CIG Katalogy Module - Migration Tests
 * 
 * Tests for database migration system
 * 
 * <AUTHOR> Team
 * @copyright 2025 CIG
 * @license   Commercial
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/../classes/migrations/MigrationManager.php';
require_once dirname(__FILE__) . '/../classes/migrations/AbstractMigration.php';
require_once dirname(__FILE__) . '/../classes/migrations/Migration_1_0_0_to_1_1_0.php';
require_once dirname(__FILE__) . '/../classes/migrations/Migration_1_1_0_to_1_2_0.php';
require_once dirname(__FILE__) . '/../classes/migrations/Migration_1_2_0_to_1_3_0.php';

class MigrationTest
{
    /** @var array Test results */
    private $results = [];
    
    /** @var int Test counter */
    private $testCount = 0;
    
    /** @var int Passed tests */
    private $passedTests = 0;

    /**
     * Run all migration tests
     * 
     * @return array Test results
     */
    public function runAllTests()
    {
        echo "Starting Migration Tests...\n";
        echo "========================\n\n";

        // Test MigrationManager
        $this->testMigrationManager();
        
        // Test individual migrations
        $this->testMigration_1_0_0_to_1_1_0();
        $this->testMigration_1_1_0_to_1_2_0();
        $this->testMigration_1_2_0_to_1_3_0();
        
        // Test migration chain
        $this->testMigrationChain();
        
        // Test rollback functionality
        $this->testRollbackFunctionality();

        $this->printSummary();
        return $this->results;
    }

    /**
     * Test MigrationManager functionality
     */
    private function testMigrationManager()
    {
        echo "Testing MigrationManager...\n";

        // Test version detection
        $this->test('MigrationManager can detect current version', function() {
            $manager = new MigrationManager();
            $version = $manager->getCurrentVersion();
            return !empty($version) && preg_match('/^\d+\.\d+\.\d+$/', $version);
        });

        // Test migration needed detection
        $this->test('MigrationManager can detect if migration is needed', function() {
            $manager = new MigrationManager('2.0.0');
            return $manager->isMigrationNeeded();
        });

        // Test latest version detection
        $this->test('MigrationManager can get latest version', function() {
            $manager = new MigrationManager();
            $version = $manager->getLatestVersion();
            return !empty($version) && preg_match('/^\d+\.\d+\.\d+$/', $version);
        });

        // Test backup creation
        $this->test('MigrationManager can create backup', function() {
            $manager = new MigrationManager();
            $reflection = new ReflectionClass($manager);
            $method = $reflection->getMethod('createBackup');
            $method->setAccessible(true);
            
            try {
                $result = $method->invoke($manager);
                return $result === true;
            } catch (Exception $e) {
                return false;
            }
        });

        echo "\n";
    }

    /**
     * Test Migration 1.0.0 to 1.1.0
     */
    private function testMigration_1_0_0_to_1_1_0()
    {
        echo "Testing Migration 1.0.0 to 1.1.0...\n";

        $migration = new Migration_1_0_0_to_1_1_0();

        // Test migration validation
        $this->test('Migration 1.0.0 to 1.1.0 validates correctly', function() use ($migration) {
            try {
                return $migration->validate();
            } catch (Exception $e) {
                return false;
            }
        });

        // Test version properties
        $this->test('Migration 1.0.0 to 1.1.0 has correct versions', function() use ($migration) {
            return $migration->getFromVersion() === '1.0.0' && 
                   $migration->getToVersion() === '1.1.0';
        });

        // Test description
        $this->test('Migration 1.0.0 to 1.1.0 has description', function() use ($migration) {
            $description = $migration->getDescription();
            return !empty($description) && is_string($description);
        });

        // Test helper methods
        $this->test('Migration 1.0.0 to 1.1.0 helper methods work', function() use ($migration) {
            $reflection = new ReflectionClass($migration);
            
            // Test tableExists method
            $method = $reflection->getMethod('tableExists');
            $method->setAccessible(true);
            $exists = $method->invoke($migration, 'cig_catalog');
            
            return is_bool($exists);
        });

        echo "\n";
    }

    /**
     * Test Migration 1.1.0 to 1.2.0
     */
    private function testMigration_1_1_0_to_1_2_0()
    {
        echo "Testing Migration 1.1.0 to 1.2.0...\n";

        $migration = new Migration_1_1_0_to_1_2_0();

        // Test migration validation
        $this->test('Migration 1.1.0 to 1.2.0 validates correctly', function() use ($migration) {
            try {
                return $migration->validate();
            } catch (Exception $e) {
                return false;
            }
        });

        // Test version properties
        $this->test('Migration 1.1.0 to 1.2.0 has correct versions', function() use ($migration) {
            return $migration->getFromVersion() === '1.1.0' && 
                   $migration->getToVersion() === '1.2.0';
        });

        // Test description
        $this->test('Migration 1.1.0 to 1.2.0 has description', function() use ($migration) {
            $description = $migration->getDescription();
            return !empty($description) && is_string($description);
        });

        echo "\n";
    }

    /**
     * Test Migration 1.2.0 to 1.3.0
     */
    private function testMigration_1_2_0_to_1_3_0()
    {
        echo "Testing Migration 1.2.0 to 1.3.0...\n";

        $migration = new Migration_1_2_0_to_1_3_0();

        // Test migration validation
        $this->test('Migration 1.2.0 to 1.3.0 validates correctly', function() use ($migration) {
            try {
                return $migration->validate();
            } catch (Exception $e) {
                return false;
            }
        });

        // Test version properties
        $this->test('Migration 1.2.0 to 1.3.0 has correct versions', function() use ($migration) {
            return $migration->getFromVersion() === '1.2.0' && 
                   $migration->getToVersion() === '1.3.0';
        });

        // Test description
        $this->test('Migration 1.2.0 to 1.3.0 has description', function() use ($migration) {
            $description = $migration->getDescription();
            return !empty($description) && is_string($description);
        });

        echo "\n";
    }

    /**
     * Test migration chain functionality
     */
    private function testMigrationChain()
    {
        echo "Testing Migration Chain...\n";

        // Test migration path calculation
        $this->test('MigrationManager can calculate migration path', function() {
            $manager = new MigrationManager('1.3.0');
            $reflection = new ReflectionClass($manager);
            
            // Set current version to 1.0.0 for testing
            $property = $reflection->getProperty('currentVersion');
            $property->setAccessible(true);
            $property->setValue($manager, '1.0.0');
            
            $method = $reflection->getMethod('getMigrationPath');
            $method->setAccessible(true);
            $path = $method->invoke($manager);
            
            return is_array($path) && count($path) > 0;
        });

        // Test next migration detection
        $this->test('MigrationManager can find next migration', function() {
            $manager = new MigrationManager();
            $reflection = new ReflectionClass($manager);
            
            $method = $reflection->getMethod('getNextMigration');
            $method->setAccessible(true);
            $next = $method->invoke($manager, '1.0.0');
            
            return is_array($next) || is_null($next);
        });

        echo "\n";
    }

    /**
     * Test rollback functionality
     */
    private function testRollbackFunctionality()
    {
        echo "Testing Rollback Functionality...\n";

        // Test rollback method exists
        $this->test('All migrations have rollback method', function() {
            $migrations = [
                new Migration_1_0_0_to_1_1_0(),
                new Migration_1_1_0_to_1_2_0(),
                new Migration_1_2_0_to_1_3_0()
            ];
            
            foreach ($migrations as $migration) {
                if (!method_exists($migration, 'down')) {
                    return false;
                }
            }
            
            return true;
        });

        // Test backup restoration
        $this->test('MigrationManager can restore backup', function() {
            $manager = new MigrationManager();
            $reflection = new ReflectionClass($manager);
            
            $method = $reflection->getMethod('restoreBackup');
            $method->setAccessible(true);
            
            try {
                // This will fail because no backup exists, but method should exist
                $method->invoke($manager);
                return false; // Should not reach here
            } catch (Exception $e) {
                // Expected to fail, but method exists
                return strpos($e->getMessage(), 'No backup found') !== false;
            }
        });

        echo "\n";
    }

    /**
     * Run a single test
     * 
     * @param string $name Test name
     * @param callable $test Test function
     */
    private function test($name, $test)
    {
        $this->testCount++;
        
        try {
            $result = $test();
            
            if ($result) {
                echo "✅ " . $name . "\n";
                $this->passedTests++;
                $this->results[] = ['name' => $name, 'status' => 'PASS'];
            } else {
                echo "❌ " . $name . "\n";
                $this->results[] = ['name' => $name, 'status' => 'FAIL', 'error' => 'Test returned false'];
            }
        } catch (Exception $e) {
            echo "❌ " . $name . " - Error: " . $e->getMessage() . "\n";
            $this->results[] = ['name' => $name, 'status' => 'ERROR', 'error' => $e->getMessage()];
        }
    }

    /**
     * Print test summary
     */
    private function printSummary()
    {
        echo "\n========================\n";
        echo "Migration Test Summary\n";
        echo "========================\n";
        echo "Total tests: " . $this->testCount . "\n";
        echo "Passed: " . $this->passedTests . "\n";
        echo "Failed: " . ($this->testCount - $this->passedTests) . "\n";
        
        if ($this->passedTests === $this->testCount) {
            echo "🎉 All tests passed!\n";
        } else {
            echo "⚠️  Some tests failed. Check the output above.\n";
        }
        
        echo "\n";
    }

    /**
     * Get test results
     * 
     * @return array Test results
     */
    public function getResults()
    {
        return $this->results;
    }

    /**
     * Check if all tests passed
     * 
     * @return bool True if all tests passed
     */
    public function allTestsPassed()
    {
        return $this->passedTests === $this->testCount;
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new MigrationTest();
    $test->runAllTests();
}
