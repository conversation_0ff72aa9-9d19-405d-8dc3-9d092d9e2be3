# Krok 8: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> systém a modal formulář

## Cíl kroku
Implementace kompletního objedn<PERSON>v<PERSON><PERSON>ho systému s modal formulářem, AJAX zpracováním a validací.

## Co se bude realizovat

### 8.1 Modal formulář pro objednávky

#### order-modal.html.twig
```twig
{# views/templates/front/catalog/order-modal.html.twig #}
<div class="modal fade" id="orderModal" tabindex="-1" role="dialog" aria-labelledby="orderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderModalLabel">
                    <i class="material-icons">shopping_cart</i>
                    Objednat katalog: <span id="modal-catalog-title"></span>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Zavřít">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            
            <form id="catalog-order-form" novalidate>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="material-icons">info</i>
                        Vyplňte prosím kontaktní údaje. Katalog vám zašleme poštou zdarma.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company_name">Název firmy *</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company_id">IČO</label>
                                <input type="text" class="form-control" id="company_id" name="company_id" 
                                       pattern="[0-9]{8}" maxlength="8">
                                <small class="form-text text-muted">8 číslic bez mezer</small>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name">Jméno *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name">Příjmení *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">Telefon</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="address">Adresa pro doručení *</label>
                        <textarea class="form-control" id="address" name="address" rows="3" required 
                                  placeholder="Ulice a číslo popisné&#10;PSČ Město&#10;Země"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="note">Poznámka</label>
                        <textarea class="form-control" id="note" name="note" rows="2" 
                                  placeholder="Další informace k objednávce..."></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="gdpr_consent" name="gdpr_consent" required>
                        <label class="form-check-label" for="gdpr_consent">
                            Souhlasím se zpracováním osobních údajů pro účely vyřízení objednávky *
                        </label>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="material-icons">close</i>
                        Zrušit
                    </button>
                    <button type="submit" class="btn btn-primary" id="submit-order">
                        <i class="material-icons">send</i>
                        Odeslat objednávku
                    </button>
                </div>
                
                <input type="hidden" id="catalog_id" name="catalog_id">
                <input type="hidden" name="_token" value="{{ csrf_token('catalog_order') }}">
            </form>
        </div>
    </div>
</div>
```

### 8.2 JavaScript pro modal a validaci

#### order-modal.js
```javascript
class CatalogOrderModal {
    constructor() {
        this.modal = null;
        this.form = null;
        this.isSubmitting = false;
        this.init();
    }
    
    init() {
        this.createModal();
        this.setupEventListeners();
        this.setupValidation();
    }
    
    createModal() {
        // Načtení modal HTML přes AJAX nebo vložení do stránky
        if (!document.getElementById('orderModal')) {
            this.loadModalHTML();
        } else {
            this.modal = document.getElementById('orderModal');
            this.form = document.getElementById('catalog-order-form');
        }
    }
    
    async loadModalHTML() {
        try {
            const response = await fetch('/module/cig_catalog/modal');
            const html = await response.text();
            document.body.insertAdjacentHTML('beforeend', html);
            this.modal = document.getElementById('orderModal');
            this.form = document.getElementById('catalog-order-form');
        } catch (error) {
            console.error('Chyba při načítání modal formuláře:', error);
        }
    }
    
    setupEventListeners() {
        // Otevření modalu
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-order') || e.target.closest('.btn-order')) {
                e.preventDefault();
                const button = e.target.classList.contains('btn-order') ? e.target : e.target.closest('.btn-order');
                this.openModal(button.dataset.catalogId, button.dataset.catalogTitle);
            }
        });
        
        // Odeslání formuláře
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitOrder();
            });
        }
        
        // Reset formuláře při zavření
        $(this.modal).on('hidden.bs.modal', () => {
            this.resetForm();
        });
    }
    
    setupValidation() {
        if (!this.form) return;
        
        const inputs = this.form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
        
        // Speciální validace pro IČO
        const companyIdInput = this.form.querySelector('#company_id');
        if (companyIdInput) {
            companyIdInput.addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/\D/g, '').substring(0, 8);
            });
        }
        
        // Formátování telefonu
        const phoneInput = this.form.querySelector('#phone');
        if (phoneInput) {
            phoneInput.addEventListener('input', (e) => {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 9) {
                    value = value.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');
                }
                e.target.value = value;
            });
        }
    }
    
    openModal(catalogId, catalogTitle) {
        if (!this.modal) {
            console.error('Modal není k dispozici');
            return;
        }
        
        document.getElementById('modal-catalog-title').textContent = catalogTitle;
        document.getElementById('catalog_id').value = catalogId;
        
        $(this.modal).modal('show');
    }
    
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';
        
        // Povinná pole
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'Toto pole je povinné';
        }
        
        // Email validace
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Zadejte platnou emailovou adresu';
            }
        }
        
        // IČO validace
        if (field.name === 'company_id' && value) {
            if (!/^\d{8}$/.test(value)) {
                isValid = false;
                errorMessage = 'IČO musí obsahovat přesně 8 číslic';
            }
        }
        
        this.setFieldValidation(field, isValid, errorMessage);
        return isValid;
    }
    
    setFieldValidation(field, isValid, errorMessage) {
        const feedbackElement = field.parentNode.querySelector('.invalid-feedback');
        
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            if (feedbackElement) {
                feedbackElement.textContent = '';
            }
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            if (feedbackElement) {
                feedbackElement.textContent = errorMessage;
            }
        }
    }
    
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const feedbackElement = field.parentNode.querySelector('.invalid-feedback');
        if (feedbackElement) {
            feedbackElement.textContent = '';
        }
    }
    
    validateForm() {
        const requiredFields = this.form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    async submitOrder() {
        if (this.isSubmitting) return;
        
        if (!this.validateForm()) {
            this.showAlert('Prosím opravte chyby ve formuláři', 'danger');
            return;
        }
        
        this.isSubmitting = true;
        const submitButton = this.form.querySelector('#submit-order');
        const originalText = submitButton.innerHTML;
        
        submitButton.innerHTML = '<i class="material-icons">hourglass_empty</i> Odesílám...';
        submitButton.disabled = true;
        
        try {
            const formData = new FormData(this.form);
            
            const response = await fetch('/module/cig_catalog/order', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage();
                $(this.modal).modal('hide');
                
                // Analytics tracking
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'order_submitted', {
                        'event_category': 'catalog',
                        'event_label': document.getElementById('modal-catalog-title').textContent
                    });
                }
            } else {
                this.showAlert(result.message || 'Chyba při odesílání objednávky', 'danger');
            }
        } catch (error) {
            console.error('Chyba při odesílání objednávky:', error);
            this.showAlert('Chyba při komunikaci se serverem', 'danger');
        } finally {
            this.isSubmitting = false;
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        }
    }
    
    showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-hide po 5 sekundách
        setTimeout(() => {
            const alert = modalBody.querySelector('.alert');
            if (alert) {
                $(alert).alert('close');
            }
        }, 5000);
    }
    
    showSuccessMessage() {
        const successHtml = `
            <div class="alert alert-success text-center">
                <i class="material-icons" style="font-size: 3rem; color: #28a745;">check_circle</i>
                <h4>Objednávka byla úspěšně odeslána!</h4>
                <p>Děkujeme za Váš zájem. Katalog vám zašleme na uvedenou adresu.</p>
            </div>
        `;
        
        // Zobrazení na stránce
        const container = document.querySelector('.catalog-page .container');
        container.insertAdjacentHTML('afterbegin', successHtml);
        
        // Scroll na začátek stránky
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    
    resetForm() {
        if (!this.form) return;
        
        this.form.reset();
        
        // Odstranění validačních tříd
        const fields = this.form.querySelectorAll('.form-control');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        // Odstranění alert zpráv
        const alerts = this.modal.querySelectorAll('.alert');
        alerts.forEach(alert => alert.remove());
    }
}

// Inicializace
document.addEventListener('DOMContentLoaded', () => {
    window.catalogOrderModal = new CatalogOrderModal();
});
```

### 8.3 Backend zpracování objednávek

#### OrderController.php
```php
<?php

namespace CigCatalog\Controller\Front;

use PrestaShopBundle\Controller\Front\FrontController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use CigCatalog\Service\CatalogOrderService;
use CigCatalog\Service\EmailService;

class OrderController extends FrontController
{
    private CatalogOrderService $orderService;
    private EmailService $emailService;
    
    public function __construct(
        CatalogOrderService $orderService,
        EmailService $emailService
    ) {
        $this->orderService = $orderService;
        $this->emailService = $emailService;
    }
    
    public function createAction(Request $request): JsonResponse
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný požadavek'], 400);
        }
        
        // CSRF validace
        if (!$this->isCsrfTokenValid('catalog_order', $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný token'], 403);
        }
        
        // Rate limiting kontrola
        if (!$this->checkRateLimit($request)) {
            return new JsonResponse(['success' => false, 'message' => 'Příliš mnoho požadavků'], 429);
        }
        
        $orderData = [
            'id_catalog' => $request->request->getInt('catalog_id'),
            'company_name' => $request->request->get('company_name'),
            'company_id' => $request->request->get('company_id'),
            'first_name' => $request->request->get('first_name'),
            'last_name' => $request->request->get('last_name'),
            'email' => $request->request->get('email'),
            'phone' => $request->request->get('phone'),
            'address' => $request->request->get('address'),
            'note' => $request->request->get('note'),
            'gdpr_consent' => $request->request->getBoolean('gdpr_consent'),
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent')
        ];
        
        try {
            // Validace dat
            $validationErrors = $this->orderService->validateOrderData($orderData);
            if (!empty($validationErrors)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Chyby ve formuláři',
                    'errors' => $validationErrors
                ], 422);
            }
            
            // Vytvoření objednávky
            $orderId = $this->orderService->createOrder($orderData);
            
            // Odeslání emailů
            $this->emailService->sendOrderNotification($orderData);
            $this->emailService->sendOrderConfirmation($orderData);
            
            // Log pro statistiky
            $this->logger->info('Catalog order created', [
                'order_id' => $orderId,
                'catalog_id' => $orderData['id_catalog'],
                'email' => $orderData['email']
            ]);
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Objednávka byla úspěšně odeslána',
                'order_id' => $orderId
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('Order creation failed', [
                'error' => $e->getMessage(),
                'data' => $orderData
            ]);
            
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při zpracování objednávky'
            ], 500);
        }
    }
    
    public function modalAction(): Response
    {
        return $this->render('@Modules/cig_catalog/views/templates/front/catalog/order-modal.html.twig');
    }
    
    private function checkRateLimit(Request $request): bool
    {
        $ip = $request->getClientIp();
        $cacheKey = 'catalog_order_rate_limit_' . md5($ip);
        
        $attempts = $this->cache->get($cacheKey, 0);
        
        if ($attempts >= 5) { // Max 5 objednávek za hodinu
            return false;
        }
        
        $this->cache->set($cacheKey, $attempts + 1, 3600); // 1 hodina
        return true;
    }
}
```

## Technické detaily

### Validace
- Client-side i server-side validace
- Real-time validace při psaní
- CSRF protection
- Rate limiting

### UX/UI
- Responzivní modal design
- Loading states
- Error handling
- Success feedback

### Bezpečnost
- Input sanitizace
- XSS prevence
- CSRF tokens
- Rate limiting

### Performance
- Lazy loading modalu
- Optimalizované AJAX
- Caching mechanismy

## Výstupy kroku
1. Modal formulář pro objednávky
2. JavaScript validace a zpracování
3. Backend API pro objednávky
4. Rate limiting systém
5. Error handling

## Závislosti
- Krok 1-7: Předchozí kroky

## Následující krok
Krok 9: Email systém a notifikace
