<?php
/**
 * PHPUnit Bootstrap for CIG Catalog Module
 * 
 * Sets up testing environment for the module
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Define module constants
define('_CIG_CATALOG_TEST_', true);

// Try to find PrestaShop installation
$prestashopPaths = [
    __DIR__ . '/../../../../config/config.inc.php',
    __DIR__ . '/../../../config/config.inc.php',
    __DIR__ . '/../../config/config.inc.php',
    __DIR__ . '/../config/config.inc.php',
];

$prestashopFound = false;
foreach ($prestashopPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $prestashopFound = true;
        break;
    }
}

if (!$prestashopFound) {
    echo "PrestaShop installation not found. Please run tests from within PrestaShop directory.\n";
    exit(1);
}

// Ensure we're in test mode
if (!defined('_PS_VERSION_')) {
    echo "PrestaShop not properly loaded.\n";
    exit(1);
}

// Set test environment
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/';
$_SERVER['REQUEST_METHOD'] = 'GET';

// Initialize Context if not already done
if (!Context::getContext()) {
    Context::getContext();
}

// Load module autoloader
$moduleAutoloader = __DIR__ . '/../vendor/autoload.php';
if (file_exists($moduleAutoloader)) {
    require_once $moduleAutoloader;
}

// Load module classes
require_once __DIR__ . '/../classes/Catalog.php';
require_once __DIR__ . '/../classes/CatalogOrder.php';
require_once __DIR__ . '/../classes/CatalogConfig.php';

// Load module
$module = Module::getInstanceByName('cig_catalog');
if (!$module) {
    echo "CIG Catalog module not found or not installed.\n";
    exit(1);
}

// Set up test database if needed
setupTestDatabase();

/**
 * Set up test database tables
 */
function setupTestDatabase()
{
    $db = Db::getInstance();
    
    // Check if test tables exist, create if not
    $tables = [
        'catalog',
        'catalog_lang',
        'catalog_order',
        'catalog_config',
    ];
    
    foreach ($tables as $table) {
        $tableName = _DB_PREFIX_ . $table;
        $exists = $db->executeS("SHOW TABLES LIKE '{$tableName}'");
        
        if (empty($exists)) {
            echo "Creating test table: {$tableName}\n";
            createTestTable($table);
        }
    }
}

/**
 * Create test table
 */
function createTestTable($table)
{
    $db = Db::getInstance();
    $sqlFile = __DIR__ . '/../sql/install.sql';
    
    if (!file_exists($sqlFile)) {
        echo "SQL install file not found: {$sqlFile}\n";
        return;
    }
    
    $sql = file_get_contents($sqlFile);
    $sql = str_replace(['PREFIX_', 'ENGINE=InnoDB'], [_DB_PREFIX_, 'ENGINE=InnoDB'], $sql);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $db->execute($statement);
            } catch (Exception $e) {
                echo "Error creating table: " . $e->getMessage() . "\n";
            }
        }
    }
}

/**
 * Clean up test data after tests
 */
function cleanupTestData()
{
    $db = Db::getInstance();
    
    // Clean up test records
    $testTables = [
        _DB_PREFIX_ . 'catalog_order',
        _DB_PREFIX_ . 'catalog_lang',
        _DB_PREFIX_ . 'catalog',
        _DB_PREFIX_ . 'catalog_config',
    ];
    
    foreach ($testTables as $table) {
        try {
            $db->execute("DELETE FROM {$table} WHERE 1");
        } catch (Exception $e) {
            // Table might not exist, ignore
        }
    }
}

// Register shutdown function to clean up
register_shutdown_function('cleanupTestData');

// Set error reporting for tests
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "CIG Catalog test environment initialized.\n";
echo "PrestaShop version: " . _PS_VERSION_ . "\n";
echo "PHP version: " . PHP_VERSION . "\n";
echo "Test database: " . _DB_NAME_ . "\n";
echo "Module path: " . __DIR__ . "/../\n";
echo "----------------------------------------\n";
