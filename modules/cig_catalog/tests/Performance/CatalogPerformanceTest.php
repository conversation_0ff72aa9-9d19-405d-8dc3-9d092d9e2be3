<?php
/**
 * CIG Catalog Performance Tests
 * 
 * Performance and load testing for the catalog module
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

declare(strict_types=1);

namespace CigCatalog\Tests\Performance;

use PHPUnit\Framework\TestCase;

class CatalogPerformanceTest extends TestCase
{
    private static $moduleInstance;
    private static $testDataIds = [];

    public static function setUpBeforeClass(): void
    {
        // Initialize PrestaShop environment
        self::initializePrestaShop();
        
        // Get module instance
        self::$moduleInstance = \Module::getInstanceByName('cig_catalog');
        
        if (!self::$moduleInstance) {
            self::markTestSkipped('CIG Catalog module is not installed');
        }

        // Create test data
        self::createTestData();
    }

    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        self::cleanupTestData();
    }

    /**
     * Test catalog listing performance
     */
    public function testCatalogListingPerformance(): void
    {
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
        
        // Test with different page sizes
        $pageSizes = [10, 25, 50, 100];
        
        foreach ($pageSizes as $pageSize) {
            $startTime = microtime(true);
            
            $catalogs = $repository->findAllActive(1, $pageSize, 0);
            
            $endTime = microtime(true);
            $queryTime = $endTime - $startTime;
            
            // Query should complete within reasonable time
            $this->assertLessThan(0.5, $queryTime, "Catalog listing with {$pageSize} items took too long: {$queryTime}s");
            $this->assertLessThanOrEqual($pageSize, count($catalogs));
        }
    }

    /**
     * Test search performance
     */
    public function testSearchPerformance(): void
    {
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
        
        $searchTerms = ['test', 'katalog', 'product', 'service'];
        
        foreach ($searchTerms as $term) {
            $startTime = microtime(true);
            
            $results = $repository->searchCatalogs($term, 1, 20, 0);
            
            $endTime = microtime(true);
            $queryTime = $endTime - $startTime;
            
            // Search should be fast
            $this->assertLessThan(1.0, $queryTime, "Search for '{$term}' took too long: {$queryTime}s");
            $this->assertIsArray($results);
        }
    }

    /**
     * Test cache performance
     */
    public function testCachePerformance(): void
    {
        $cacheService = self::$moduleInstance->get('cig_catalog.service.cache');
        
        $testData = [
            'small' => str_repeat('a', 100),
            'medium' => str_repeat('b', 10000),
            'large' => str_repeat('c', 100000),
        ];
        
        foreach ($testData as $size => $data) {
            // Test cache write performance
            $startTime = microtime(true);
            $cacheService->set("test_{$size}", $data, 3600);
            $writeTime = microtime(true) - $startTime;
            
            // Test cache read performance
            $startTime = microtime(true);
            $retrievedData = $cacheService->get("test_{$size}");
            $readTime = microtime(true) - $startTime;
            
            // Cache operations should be very fast
            $this->assertLessThan(0.1, $writeTime, "Cache write for {$size} data took too long: {$writeTime}s");
            $this->assertLessThan(0.05, $readTime, "Cache read for {$size} data took too long: {$readTime}s");
            $this->assertEquals($data, $retrievedData);
            
            // Cleanup
            $cacheService->delete("test_{$size}");
        }
    }

    /**
     * Test concurrent order processing
     */
    public function testConcurrentOrderProcessing(): void
    {
        $orderService = self::$moduleInstance->get('cig_catalog.service.catalog_order');
        
        $orderData = [
            'catalog_id' => self::$testDataIds[0] ?? 1,
            'company_name' => 'Performance Test s.r.o.',
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+420123456789',
            'address' => 'Test Address 123, Praha',
            'gdpr_consent' => true,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Performance Test',
            'language_id' => 1,
            'shop_id' => 1,
        ];
        
        $orderCount = 10;
        $orderIds = [];
        
        $startTime = microtime(true);
        
        // Create multiple orders
        for ($i = 0; $i < $orderCount; $i++) {
            $orderData['email'] = "performance{$i}@test.com";
            $orderId = $orderService->createOrder($orderData);
            $orderIds[] = $orderId;
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        $averageTime = $totalTime / $orderCount;
        
        // Each order should process quickly
        $this->assertLessThan(2.0, $totalTime, "Processing {$orderCount} orders took too long: {$totalTime}s");
        $this->assertLessThan(0.5, $averageTime, "Average order processing time too slow: {$averageTime}s");
        
        // Cleanup orders
        foreach ($orderIds as $orderId) {
            $order = new \CigCatalog\Entity\CatalogOrder($orderId);
            if (\Validate::isLoadedObject($order)) {
                $order->delete();
            }
        }
    }

    /**
     * Test memory usage
     */
    public function testMemoryUsage(): void
    {
        $initialMemory = memory_get_usage(true);
        
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
        
        // Load large dataset
        $catalogs = $repository->findAllActive(1, 100, 0);
        
        $peakMemory = memory_get_peak_usage(true);
        $memoryIncrease = $peakMemory - $initialMemory;
        
        // Memory usage should be reasonable (less than 10MB for 100 catalogs)
        $this->assertLessThan(10 * 1024 * 1024, $memoryIncrease, "Memory usage too high: " . ($memoryIncrease / 1024 / 1024) . "MB");
        
        // Cleanup
        unset($catalogs);
        
        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    /**
     * Test database query optimization
     */
    public function testDatabaseQueryOptimization(): void
    {
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
        
        // Enable query logging if available
        $queryCount = 0;
        
        if (class_exists('\Db') && method_exists('\Db', 'getInstance')) {
            $db = \Db::getInstance();
            
            // Test single catalog fetch
            $startQueries = $this->getQueryCount();
            $catalog = $repository->findById(self::$testDataIds[0] ?? 1, 1);
            $endQueries = $this->getQueryCount();
            
            $queriesUsed = $endQueries - $startQueries;
            
            // Should use minimal queries (ideally 1-2)
            $this->assertLessThanOrEqual(3, $queriesUsed, "Too many queries for single catalog fetch: {$queriesUsed}");
            
            // Test catalog listing
            $startQueries = $this->getQueryCount();
            $catalogs = $repository->findAllActive(1, 20, 0);
            $endQueries = $this->getQueryCount();
            
            $queriesUsed = $endQueries - $startQueries;
            
            // Should use minimal queries for listing
            $this->assertLessThanOrEqual(5, $queriesUsed, "Too many queries for catalog listing: {$queriesUsed}");
        }
    }

    /**
     * Test file upload performance
     */
    public function testFileUploadPerformance(): void
    {
        $fileUploadService = self::$moduleInstance->get('cig_catalog.service.file_upload');
        
        // Create test file data
        $testSizes = [
            'small' => 1024,      // 1KB
            'medium' => 102400,   // 100KB
            'large' => 1048576,   // 1MB
        ];
        
        foreach ($testSizes as $sizeName => $size) {
            // Create temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'perf_test_');
            file_put_contents($tempFile, str_repeat('x', $size));
            
            $fileData = [
                'name' => "test_{$sizeName}.jpg",
                'tmp_name' => $tempFile,
                'size' => $size,
                'type' => 'image/jpeg',
                'error' => UPLOAD_ERR_OK,
            ];
            
            $startTime = microtime(true);
            
            try {
                // Test file validation performance
                $isValid = $fileUploadService->validateUploadedFile($fileData);
                
                $endTime = microtime(true);
                $validationTime = $endTime - $startTime;
                
                // Validation should be fast
                $this->assertLessThan(0.1, $validationTime, "File validation for {$sizeName} file took too long: {$validationTime}s");
                
            } catch (\Exception $e) {
                // Expected for test files
            } finally {
                // Cleanup
                if (file_exists($tempFile)) {
                    unlink($tempFile);
                }
            }
        }
    }

    /**
     * Test email sending performance
     */
    public function testEmailPerformance(): void
    {
        $emailService = self::$moduleInstance->get('cig_catalog.service.email');
        
        $startTime = microtime(true);
        
        // Test email template rendering
        $templateData = [
            'shop_name' => 'Performance Test Shop',
            'order' => [
                'id_order' => 123,
                'first_name' => 'Test',
                'last_name' => 'User',
                'email' => '<EMAIL>',
            ],
            'catalog' => [
                'title' => 'Test Catalog',
            ],
        ];
        
        $templateManager = self::$moduleInstance->get('cig_catalog.service.email_template_manager');
        $renderedTemplate = $templateManager->renderTemplate('order_confirmation', $templateData);
        
        $endTime = microtime(true);
        $renderTime = $endTime - $startTime;
        
        // Template rendering should be fast
        $this->assertLessThan(0.5, $renderTime, "Email template rendering took too long: {$renderTime}s");
        $this->assertNotEmpty($renderedTemplate);
    }

    /**
     * Create test data for performance testing
     */
    private static function createTestData(): void
    {
        $catalogManager = self::$moduleInstance->get('cig_catalog.service.catalog_manager');
        
        // Create test catalogs
        for ($i = 1; $i <= 50; $i++) {
            $catalogData = [
                'name' => ['cs' => "Performance Test Katalog {$i}", 'en' => "Performance Test Catalog {$i}"],
                'description' => ['cs' => "Testovací popis pro katalog {$i}", 'en' => "Test description for catalog {$i}"],
                'short_description' => ['cs' => "Krátký popis {$i}", 'en' => "Short description {$i}"],
                'active' => true,
                'is_new' => $i <= 10,
                'position' => $i,
            ];
            
            $catalogId = $catalogManager->createCatalog($catalogData);
            self::$testDataIds[] = $catalogId;
        }
    }

    /**
     * Clean up test data
     */
    private static function cleanupTestData(): void
    {
        foreach (self::$testDataIds as $catalogId) {
            $catalog = new \CigCatalog\Entity\Catalog($catalogId);
            if (\Validate::isLoadedObject($catalog)) {
                $catalog->delete();
            }
        }
    }

    /**
     * Get current query count (if available)
     */
    private function getQueryCount(): int
    {
        // This would need to be implemented based on PrestaShop's query logging
        // For now, return 0 as placeholder
        return 0;
    }

    /**
     * Initialize PrestaShop environment
     */
    private static function initializePrestaShop(): void
    {
        if (!defined('_PS_VERSION_')) {
            $configPaths = [
                __DIR__ . '/../../../../config/config.inc.php',
                __DIR__ . '/../../../config/config.inc.php',
                __DIR__ . '/../../config/config.inc.php',
            ];

            foreach ($configPaths as $configPath) {
                if (file_exists($configPath)) {
                    require_once $configPath;
                    break;
                }
            }

            if (!defined('_PS_VERSION_')) {
                self::markTestSkipped('PrestaShop environment not found');
            }
        }
    }
}
