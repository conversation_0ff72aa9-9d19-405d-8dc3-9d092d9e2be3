<?php
/**
 * CIG Catalog Module - Entity Tests
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

// Mock PrestaShop classes for testing
if (!defined('_PS_VERSION_')) {
    define('_PS_VERSION_', '8.2.0');
}

// Mock ObjectModel class
if (!class_exists('ObjectModel')) {
    class ObjectModel {
        const TYPE_STRING = 1;
        const TYPE_HTML = 2;
        const TYPE_INT = 3;
        const TYPE_BOOL = 4;
        const TYPE_DATE = 5;

        public static $definition = [];
        public $id;

        public function __construct($id = null) {
            $this->id = $id;
        }

        public function add($auto_date = true, $null_values = false) {
            return true;
        }

        public function update($null_values = false) {
            return true;
        }

        public function delete() {
            return true;
        }
    }
}

// Mock other PrestaShop classes
if (!class_exists('Validate')) {
    class Validate {
        public static function isGenericName($name) { return true; }
        public static function isName($name) { return true; }
        public static function isEmail($email) { return filter_var($email, FILTER_VALIDATE_EMAIL) !== false; }
        public static function isPhoneNumber($phone) { return true; }
        public static function isAddress($address) { return true; }
        public static function isCleanHtml($html) { return true; }
        public static function isUrl($url) { return true; }
        public static function isFileName($filename) { return true; }
        public static function isBool($bool) { return is_bool($bool); }
        public static function isUnsignedInt($int) { return is_int($int) && $int >= 0; }
        public static function isDate($date) { return true; }
        public static function isAnything($value) { return true; }
    }
}

if (!class_exists('DbQuery')) {
    class DbQuery {
        public function select($fields) { return $this; }
        public function from($table, $alias = null) { return $this; }
        public function leftJoin($table, $alias, $on) { return $this; }
        public function where($condition) { return $this; }
        public function orderBy($order) { return $this; }
        public function limit($limit, $offset = 0) { return $this; }
        public function groupBy($group) { return $this; }
    }
}

if (!class_exists('Db')) {
    class Db {
        public static function getInstance($slave = false) {
            return new self();
        }
        public function executeS($sql) { return []; }
        public function getValue($sql) { return 0; }
        public function execute($sql) { return true; }
    }
}

if (!class_exists('Context')) {
    class Context {
        public static function getContext() {
            $context = new self();
            $context->language = (object)['id' => 1];
            $context->shop = (object)['id' => 1];
            return $context;
        }
    }
}

if (!function_exists('pSQL')) {
    function pSQL($string) {
        return addslashes($string);
    }
}

if (!defined('_DB_PREFIX_')) {
    define('_DB_PREFIX_', 'ps_');
}

if (!defined('_PS_MODULE_DIR_')) {
    define('_PS_MODULE_DIR_', __DIR__ . '/../');
}

if (!defined('_PS_USE_SQL_SLAVE_')) {
    define('_PS_USE_SQL_SLAVE_', false);
}

// Include module entities
require_once dirname(__FILE__) . '/../classes/Catalog.php';
require_once dirname(__FILE__) . '/../classes/CatalogOrder.php';
require_once dirname(__FILE__) . '/../classes/CatalogConfig.php';

/**
 * Simple test class for entity functionality
 */
class EntityTest
{
    private $errors = [];
    private $successes = [];

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "=== CIG Catalog Entity Tests ===\n\n";

        $this->testCatalogEntity();
        $this->testCatalogOrderEntity();
        $this->testCatalogConfigEntity();

        $this->printResults();
    }

    /**
     * Test Catalog entity
     */
    private function testCatalogEntity()
    {
        echo "Testing Catalog entity...\n";

        try {
            // Test entity creation
            $catalog = new Catalog();
            $this->assert($catalog instanceof Catalog, "Catalog entity creation");

            // Test definition
            $this->assert(isset(Catalog::$definition), "Catalog definition exists");
            $this->assert(Catalog::$definition['table'] === 'cig_catalog', "Catalog table name");
            $this->assert(Catalog::$definition['primary'] === 'id_catalog', "Catalog primary key");
            $this->assert(Catalog::$definition['multilang'] === true, "Catalog multilang support");

            // Test field definitions
            $fields = Catalog::$definition['fields'];
            $this->assert(isset($fields['active']), "Active field defined");
            $this->assert(isset($fields['position']), "Position field defined");
            $this->assert(isset($fields['title']), "Title field defined");
            $this->assert($fields['title']['lang'] === true, "Title field is multilingual");

            // Test methods
            $this->assert(method_exists($catalog, 'getHighestPosition'), "getHighestPosition method exists");
            $this->assert(method_exists($catalog, 'getImageUrl'), "getImageUrl method exists");
            $this->assert(method_exists('Catalog', 'getActiveCatalogs'), "getActiveCatalogs static method exists");

            echo "✓ Catalog entity tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "Catalog entity test failed: " . $e->getMessage();
            echo "✗ Catalog entity tests failed\n";
        }
    }

    /**
     * Test CatalogOrder entity
     */
    private function testCatalogOrderEntity()
    {
        echo "Testing CatalogOrder entity...\n";

        try {
            // Test entity creation
            $order = new CatalogOrder();
            $this->assert($order instanceof CatalogOrder, "CatalogOrder entity creation");

            // Test definition
            $this->assert(isset(CatalogOrder::$definition), "CatalogOrder definition exists");
            $this->assert(CatalogOrder::$definition['table'] === 'cig_catalog_order', "CatalogOrder table name");
            $this->assert(CatalogOrder::$definition['primary'] === 'id_order', "CatalogOrder primary key");

            // Test field definitions
            $fields = CatalogOrder::$definition['fields'];
            $this->assert(isset($fields['email']), "Email field defined");
            $this->assert(isset($fields['first_name']), "First name field defined");
            $this->assert(isset($fields['last_name']), "Last name field defined");

            // Test methods
            $this->assert(method_exists('CatalogOrder', 'validateCompanyId'), "validateCompanyId static method exists");
            $this->assert(method_exists('CatalogOrder', 'getAllOrders'), "getAllOrders static method exists");
            $this->assert(method_exists($order, 'getFullName'), "getFullName method exists");

            // Test ICO validation - basic tests
            $this->assert(CatalogOrder::validateCompanyId(''), "Empty ICO validation");
            $this->assert(!CatalogOrder::validateCompanyId('1234567'), "Short ICO validation"); // Too short

            echo "✓ CatalogOrder entity tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "CatalogOrder entity test failed: " . $e->getMessage();
            echo "✗ CatalogOrder entity tests failed\n";
        }
    }

    /**
     * Test CatalogConfig entity
     */
    private function testCatalogConfigEntity()
    {
        echo "Testing CatalogConfig entity...\n";

        try {
            // Test entity creation
            $config = new CatalogConfig();
            $this->assert($config instanceof CatalogConfig, "CatalogConfig entity creation");

            // Test definition
            $this->assert(isset(CatalogConfig::$definition), "CatalogConfig definition exists");
            $this->assert(CatalogConfig::$definition['table'] === 'cig_catalog_config', "CatalogConfig table name");

            // Test static methods
            $this->assert(method_exists('CatalogConfig', 'get'), "get static method exists");
            $this->assert(method_exists('CatalogConfig', 'set'), "set static method exists");
            $this->assert(method_exists('CatalogConfig', 'initializeDefaults'), "initializeDefaults static method exists");

            // Test configuration groups
            $groups = CatalogConfig::getConfigGroups();
            $this->assert(is_array($groups), "Config groups is array");
            $this->assert(isset($groups['general']), "general group exists");
            $this->assert(isset($groups['display']), "display group exists");

            // Test helper methods
            $this->assert(method_exists('CatalogConfig', 'getBool'), "getBool method exists");
            $this->assert(method_exists('CatalogConfig', 'getInt'), "getInt method exists");
            $this->assert(method_exists('CatalogConfig', 'getArray'), "getArray method exists");

            echo "✓ CatalogConfig entity tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "CatalogConfig entity test failed: " . $e->getMessage();
            echo "✗ CatalogConfig entity tests failed\n";
        }
    }



    /**
     * Assert helper
     */
    private function assert($condition, $message)
    {
        if ($condition) {
            $this->successes[] = $message;
        } else {
            $this->errors[] = $message;
            throw new Exception("Assertion failed: " . $message);
        }
    }

    /**
     * Print test results
     */
    private function printResults()
    {
        echo "\n=== Test Results ===\n";
        echo "Successes: " . count($this->successes) . "\n";
        echo "Errors: " . count($this->errors) . "\n";

        if (!empty($this->errors)) {
            echo "\nErrors:\n";
            foreach ($this->errors as $error) {
                echo "- " . $error . "\n";
            }
        }

        echo "\nTest completed.\n";
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new EntityTest();
    $test->runAllTests();
}
