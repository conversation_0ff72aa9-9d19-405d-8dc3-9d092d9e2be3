<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Tests\Repository;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use CigCatalog\Repository\CatalogRepository;
use Db;
use DbQuery;
use Exception;

/**
 * Test class for CatalogRepository
 */
class CatalogRepositoryTest extends TestCase
{
    /** @var CatalogRepository */
    private $repository;

    /** @var Db|MockObject */
    private $mockDb;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock global constants and functions if not defined
        if (!defined('_DB_PREFIX_')) {
            define('_DB_PREFIX_', 'ps_');
        }

        if (!function_exists('pSQL')) {
            function pSQL($string) {
                return addslashes($string);
            }
        }

        $this->mockDb = $this->createMock(Db::class);
        $this->repository = new CatalogRepository();

        // Use reflection to inject mock database
        $reflection = new \ReflectionClass($this->repository);
        $connectionProperty = $reflection->getProperty('connection');
        $connectionProperty->setAccessible(true);
        $connectionProperty->setValue($this->repository, $this->mockDb);
    }

    public function testFindByIdSuccess(): void
    {
        $catalogId = 1;
        $langId = 1;
        $expectedResult = [
            [
                'id_catalog' => 1,
                'name' => 'Test Catalog',
                'description' => 'Test Description',
                'active' => 1
            ]
        ];

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn($expectedResult);

        $result = $this->repository->findById($catalogId, $langId);
        $this->assertEquals($expectedResult[0], $result);
    }

    public function testFindByIdNotFound(): void
    {
        $catalogId = 999;
        $langId = 1;

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn([]);

        $result = $this->repository->findById($catalogId, $langId);
        $this->assertNull($result);
    }

    public function testFindAllActive(): void
    {
        $langId = 1;
        $expectedResult = [
            [
                'id_catalog' => 1,
                'name' => 'Catalog 1',
                'active' => 1,
                'position' => 1
            ],
            [
                'id_catalog' => 2,
                'name' => 'Catalog 2',
                'active' => 1,
                'position' => 2
            ]
        ];

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn($expectedResult);

        $result = $this->repository->findAllActive($langId);
        $this->assertEquals($expectedResult, $result);
        $this->assertCount(2, $result);
    }

    public function testCreateSuccess(): void
    {
        $catalogData = [
            'active' => 1,
            'position' => 1,
            'is_new' => 1,
            'image' => 'test.jpg',
            'catalog_file' => 'test.pdf',
            'lang' => [
                1 => [
                    'name' => 'Test Catalog',
                    'description' => 'Test Description',
                    'slug' => 'test-catalog'
                ]
            ]
        ];

        $expectedCatalogId = 123;

        // Mock transaction methods
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('START TRANSACTION')
            ->willReturn(true);

        $this->mockDb->expects($this->exactly(2))
            ->method('executeS')
            ->willReturn(true);

        $this->mockDb->expects($this->once())
            ->method('Insert_ID')
            ->willReturn($expectedCatalogId);

        // Mock getMaxPosition call
        $this->mockDb->expects($this->once())
            ->method('getValue')
            ->willReturn(0);

        $result = $this->repository->create($catalogData);
        $this->assertEquals($expectedCatalogId, $result);
    }

    public function testCreateTransactionFailure(): void
    {
        $catalogData = [
            'active' => 1,
            'lang' => [
                1 => ['name' => 'Test']
            ]
        ];

        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('START TRANSACTION')
            ->willReturn(false);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failed to start transaction');

        $this->repository->create($catalogData);
    }

    public function testUpdateSuccess(): void
    {
        $catalogId = 1;
        $updateData = [
            'active' => 0,
            'position' => 5,
            'lang' => [
                1 => [
                    'name' => 'Updated Catalog',
                    'description' => 'Updated Description'
                ]
            ]
        ];

        // Mock transaction methods
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('START TRANSACTION')
            ->willReturn(true);

        $this->mockDb->expects($this->exactly(2))
            ->method('executeS')
            ->willReturn(true);

        $result = $this->repository->update($catalogId, $updateData);
        $this->assertTrue($result);
    }

    public function testDeleteSuccess(): void
    {
        $catalogId = 1;

        // Mock transaction methods
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('START TRANSACTION')
            ->willReturn(true);

        // Mock position retrieval
        $this->mockDb->expects($this->once())
            ->method('getValue')
            ->willReturn(2);

        // Mock delete operations
        $this->mockDb->expects($this->exactly(3))
            ->method('executeS')
            ->willReturn(true);

        $result = $this->repository->delete($catalogId);
        $this->assertTrue($result);
    }

    public function testFindNewCatalogs(): void
    {
        $langId = 1;
        $expectedResult = [
            [
                'id_catalog' => 1,
                'name' => 'New Catalog',
                'is_new' => 1,
                'date_add' => '2024-01-01 10:00:00'
            ]
        ];

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn($expectedResult);

        $result = $this->repository->findNewCatalogs($langId);
        $this->assertEquals($expectedResult, $result);
    }

    public function testFindWithPagination(): void
    {
        $page = 1;
        $limit = 10;
        $langId = 1;

        $expectedData = [
            ['id_catalog' => 1, 'name' => 'Catalog 1'],
            ['id_catalog' => 2, 'name' => 'Catalog 2']
        ];

        // Mock count query
        $this->mockDb->expects($this->at(0))
            ->method('getValue')
            ->willReturn(25);

        // Mock data query
        $this->mockDb->expects($this->at(1))
            ->method('executeS')
            ->willReturn($expectedData);

        $result = $this->repository->findWithPagination($page, $limit, $langId);

        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('limit', $result);
        $this->assertArrayHasKey('pages', $result);

        $this->assertEquals($expectedData, $result['data']);
        $this->assertEquals(25, $result['total']);
        $this->assertEquals(1, $result['page']);
        $this->assertEquals(10, $result['limit']);
        $this->assertEquals(3, $result['pages']);
    }

    public function testSearchCatalogs(): void
    {
        $query = 'test search';
        $langId = 1;
        $expectedResult = [
            [
                'id_catalog' => 1,
                'name' => 'Test Catalog',
                'description' => 'Contains test search term'
            ]
        ];

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn($expectedResult);

        $result = $this->repository->search($query, $langId);
        $this->assertEquals($expectedResult, $result);
    }

    public function testSearchEmptyQuery(): void
    {
        $result = $this->repository->search('', 1);
        $this->assertEquals([], $result);
    }

    public function testUpdatePositions(): void
    {
        $positions = [
            1 => 3,
            2 => 1,
            3 => 2
        ];

        // Mock transaction methods
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('START TRANSACTION')
            ->willReturn(true);

        $this->mockDb->expects($this->exactly(3))
            ->method('executeS')
            ->willReturn(true);

        $result = $this->repository->updatePositions($positions);
        $this->assertTrue($result);
    }

    public function testGetMaxPosition(): void
    {
        $expectedMaxPosition = 5;

        $this->mockDb->expects($this->once())
            ->method('getValue')
            ->willReturn($expectedMaxPosition);

        $result = $this->repository->getMaxPosition();
        $this->assertEquals($expectedMaxPosition, $result);
    }

    public function testIncrementDownloadCount(): void
    {
        $catalogId = 1;

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn(true);

        $result = $this->repository->incrementDownloadCount($catalogId);
        $this->assertTrue($result);
    }

    public function testGetStatistics(): void
    {
        $expectedStats = [
            'total' => 10,
            'active' => 8,
            'new' => 3,
            'total_downloads' => 150
        ];

        $this->mockDb->expects($this->exactly(4))
            ->method('getValue')
            ->willReturnOnConsecutiveCalls(
                $expectedStats['total'],
                $expectedStats['active'],
                $expectedStats['new'],
                $expectedStats['total_downloads']
            );

        $result = $this->repository->getStatistics();
        $this->assertEquals($expectedStats, $result);
    }

    public function testFindActiveWithFilters(): void
    {
        $filters = [
            'is_new' => 1,
            'search' => 'test',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31',
            'order_by' => 'name',
            'order_way' => 'DESC'
        ];
        $langId = 1;

        $expectedResult = [
            ['id_catalog' => 1, 'name' => 'Test Catalog', 'is_new' => 1]
        ];

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->willReturn($expectedResult);

        $result = $this->repository->findActiveWithFilters($filters, $langId);
        $this->assertEquals($expectedResult, $result);
    }
}
