# CIG Catalog Module

Pokročilý modul pro správu a zobrazování katalogů v PrestaShop 8.2.0 s objednávkovým systémem a email notifikacemi.

## 📋 Obsah

- [<PERSON>ce](#funkce)
- [<PERSON><PERSON><PERSON><PERSON><PERSON>](#po<PERSON><PERSON><PERSON><PERSON>)
- [Instalace](#instalace)
- [Konfigurace](#konfigurace)
- [Použit<PERSON>](#použití)
- [API](#api)
- [Testování](#testování)
- [Bezpečnost](#bezpečnost)
- [Výkon](#výkon)
- [Podpora](#podpora)

## ✨ Funkce

### Základn<PERSON> funkce
- ✅ Správa katalogů s vícejazyčnou podporou
- ✅ Upload obrázků a souborů katalogů
- ✅ Responzivní frontend zobrazení
- ✅ Pokročilé vyhledávání a filtrování
- ✅ Stránkování a řazení

### Objednávkový systém
- ✅ Modal formulář pro objednávky
- ✅ Validace formulářů (včetně českého IČO)
- ✅ GDPR compliance
- ✅ Rate limiting pro zabezpečení
- ✅ Admin správa objednávek

### Email systém
- ✅ HTML email šablony
- ✅ SMTP konfigurace
- ✅ Automatické notifikace
- ✅ Email testing nástroje
- ✅ Statistiky emailů

### Bezpečnost
- ✅ CSRF ochrana
- ✅ XSS prevence
- ✅ SQL injection ochrana
- ✅ Validace souborů
- ✅ Rate limiting

### Výkon
- ✅ Pokročilé cachování
- ✅ Optimalizované databázové dotazy
- ✅ Lazy loading obrázků
- ✅ Komprese a optimalizace

## 🔧 Požadavky

### Systémové požadavky
- **PrestaShop**: 8.2.0 nebo vyšší
- **PHP**: 7.4 nebo vyšší (doporučeno 8.1+)
- **MySQL**: 5.7 nebo vyšší
- **Apache/Nginx**: s mod_rewrite
- **Memory limit**: minimálně 256MB

### PHP rozšíření
- `gd` nebo `imagick` (pro zpracování obrázků)
- `curl` (pro HTTP požadavky)
- `mbstring` (pro Unicode podporu)
- `zip` (pro export/import)
- `openssl` (pro SMTP SSL/TLS)

## 📦 Instalace

### 1. Stažení modulu
```bash
# Stáhněte modul do složky modules
cd /path/to/prestashop/modules/
git clone https://github.com/your-repo/cig_catalog.git
```

### 2. Instalace přes admin
1. Přihlaste se do PrestaShop administrace
2. Jděte na **Moduly** → **Správce modulů**
3. Najděte "CIG Catalog" a klikněte na **Instalovat**
4. Postupujte podle průvodce instalací

### 3. Manuální instalace
```bash
# Nastavte správná oprávnění
chmod -R 755 modules/cig_catalog/
chmod -R 777 modules/cig_catalog/uploads/
```

### 4. Ověření instalace
- Zkontrolujte, že se vytvořily databázové tabulky
- Ověřte, že jsou dostupné admin stránky
- Otestujte frontend zobrazení

## ⚙️ Konfigurace

### Základní nastavení

#### 1. Obecná konfigurace
```
Admin → Moduly → CIG Catalog → Konfigurace

- Počet katalogů na stránku: 12
- Povolit objednávky: Ano
- Povolit stahování: Ano
- Zobrazit počet stažení: Ano
- Dny pro "Nový" badge: 30
```

#### 2. Email konfigurace
```
Admin → CIG Catalog → Email konfigurace

SMTP nastavení:
- SMTP server: smtp.gmail.com
- Port: 587
- Šifrování: TLS
- Uživatelské jméno: <EMAIL>
- Heslo: your-app-password
```

#### 3. Bezpečnostní nastavení
```
Admin → CIG Catalog → Bezpečnost

- Max objednávek za hodinu: 5
- Povolit pouze registrované uživatele: Ne
- CSRF ochrana: Ano
- Rate limiting: Ano
```

### Pokročilá konfigurace

#### Cache nastavení
```php
// config/cig_catalog_config.php
return [
    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // 1 hodina
        'driver' => 'file', // file, redis, memcached
    ],
    'performance' => [
        'lazy_loading' => true,
        'image_optimization' => true,
        'gzip_compression' => true,
    ],
];
```

## 🎯 Použití

### Admin správa

#### Správa katalogů
1. **Vytvoření katalogu**
   ```
   Admin → CIG Catalog → Katalogy → Přidat nový
   
   - Název (CS/EN)
   - Popis
   - Krátký popis
   - Obrázek (JPG, PNG, max 2MB)
   - Soubor katalogu (PDF, ZIP, max 10MB)
   - Aktivní: Ano/Ne
   - Nový: Ano/Ne
   ```

2. **Úprava katalogu**
   - Klikněte na katalog v seznamu
   - Upravte požadované údaje
   - Uložte změny

3. **Hromadné operace**
   - Označte katalogy
   - Vyberte akci (Aktivovat/Deaktivovat/Smazat)
   - Potvrďte operaci

#### Správa objednávek
1. **Zobrazení objednávek**
   ```
   Admin → CIG Catalog → Objednávky
   
   Filtry:
   - Datum od/do
   - Status
   - Katalog
   - Email zákazníka
   ```

2. **Zpracování objednávky**
   - Otevřete objednávku
   - Změňte status
   - Přidejte poznámku
   - Odešlete email zákazníkovi

### Frontend použití

#### Zobrazení katalogů
```
URL: /module/cig_catalog/catalog

Funkce:
- Vyhledávání katalogů
- Filtrování podle kategorií
- Stránkování
- Řazení (datum, název, popularita)
```

#### Objednání katalogu
1. Klikněte na "Objednat" u katalogu
2. Vyplňte kontaktní formulář
3. Souhlaste s GDPR
4. Odešlete objednávku
5. Obdržíte potvrzovací email

### API použití

#### REST API endpoints
```php
// Získání katalogů
GET /api/catalogs
GET /api/catalogs/{id}

// Vyhledávání
GET /api/catalogs/search?q=keyword

// Objednávky (POST only)
POST /api/orders
```

#### Příklad API volání
```javascript
// Získání katalogů
fetch('/api/catalogs')
  .then(response => response.json())
  .then(data => console.log(data));

// Vytvoření objednávky
fetch('/api/orders', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': token
  },
  body: JSON.stringify(orderData)
});
```

## 🧪 Testování

### Spuštění testů
```bash
# Všechny testy
./vendor/bin/phpunit modules/cig_catalog/tests/

# Unit testy
./vendor/bin/phpunit modules/cig_catalog/tests/Unit/

# Integrační testy
./vendor/bin/phpunit modules/cig_catalog/tests/Integration/

# Performance testy
./vendor/bin/phpunit modules/cig_catalog/tests/Performance/

# Bezpečnostní testy
./vendor/bin/phpunit modules/cig_catalog/tests/Security/
```

### Test coverage
```bash
# Generování coverage reportu
./vendor/bin/phpunit --coverage-html coverage/ modules/cig_catalog/tests/
```

### Manuální testování
1. **Funkční testy**
   - Vytvoření/úprava/smazání katalogu
   - Upload souborů
   - Objednávkový proces
   - Email notifikace

2. **UI/UX testy**
   - Responzivní design
   - Přístupnost (WCAG 2.1)
   - Cross-browser kompatibilita
   - Mobile-first přístup

## 🔒 Bezpečnost

### Implementované bezpečnostní opatření

#### Input validace
```php
// Všechny vstupy jsou validovány
$email = Tools::getValue('email');
if (!Validate::isEmail($email)) {
    throw new DomainException('Invalid email');
}
```

#### CSRF ochrana
```php
// Všechny formuláře obsahují CSRF token
$token = Tools::getToken(false);
if ($token !== Tools::getValue('token')) {
    throw new SecurityException('Invalid CSRF token');
}
```

#### Rate limiting
```php
// Omezení počtu požadavků
$rateLimiter->checkLimit($ip, 'order_submission', 5, 3600);
```

#### File upload security
```php
// Validace typů souborů
$allowedTypes = ['jpg', 'png', 'pdf', 'zip'];
if (!in_array($extension, $allowedTypes)) {
    throw new SecurityException('Invalid file type');
}
```

### Bezpečnostní doporučení
1. **Pravidelné aktualizace**
   - Aktualizujte PrestaShop
   - Aktualizujte modul
   - Monitorujte bezpečnostní bulletiny

2. **Server konfigurace**
   - Používejte HTTPS
   - Nastavte správná oprávnění souborů
   - Zakažte directory listing
   - Používejte firewall

3. **Monitoring**
   - Sledujte logy
   - Nastavte alerting
   - Pravidelné bezpečnostní audity

## ⚡ Výkon

### Optimalizace

#### Database optimalizace
```sql
-- Indexy pro rychlé vyhledávání
CREATE INDEX idx_catalog_active ON ps_catalog (active);
CREATE INDEX idx_catalog_position ON ps_catalog (position);
CREATE INDEX idx_catalog_lang_name ON ps_catalog_lang (name);
```

#### Cache strategie
```php
// Multi-level caching
$cacheKey = 'catalogs_' . $langId . '_' . $page;
$data = $cache->get($cacheKey);
if ($data === null) {
    $data = $repository->findAllActive($langId, $limit, $offset);
    $cache->set($cacheKey, $data, 3600);
}
```

#### Image optimalizace
```php
// Automatická komprese obrázků
$image->resize(800, 600, 'jpg', 85); // 85% kvalita
$image->generateThumbnails([
    'small' => [200, 150],
    'medium' => [400, 300],
    'large' => [800, 600]
]);
```

### Performance monitoring
```bash
# Sledování výkonu
tail -f var/logs/performance.log

# Database query analysis
EXPLAIN SELECT * FROM ps_catalog WHERE active = 1;

# Memory usage monitoring
memory_get_peak_usage(true);
```

## 📞 Podpora

### Dokumentace
- **Uživatelská příručka**: `/docs/user-guide.md`
- **Vývojářská dokumentace**: `/docs/developer-guide.md`
- **API dokumentace**: `/docs/api.md`
- **FAQ**: `/docs/faq.md`

### Kontakt
- **Email**: <EMAIL>
- **GitHub Issues**: https://github.com/your-repo/cig_catalog/issues
- **Dokumentace**: https://docs.cigcatalog.com

### Changelog
Viz [CHANGELOG.md](CHANGELOG.md) pro kompletní historii změn.

### Licence
Tento modul je licencován pod [MIT License](LICENSE).

---

**Verze**: 1.0.0  
**Autor**: CIG Development Team  
**Datum**: 2024-12-19
