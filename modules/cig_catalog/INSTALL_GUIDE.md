# 🚀 CIG Catalog Module - Instalační příručka

## 📦 Stažení modulu

**ZIP balíček je připraven zde:**
```
modules/cig_catalog/dist/cig_catalog-1.0.0.zip
```

## ⚠️ Důležité opravy

V této verzi byly opraveny následující problémy:
- ✅ **SQL syntax error** - Opravena chyba s MariaDB kompatibilitou
- ✅ **clearCache() konflikt** - Vyřešen konflikt s PrestaShop core metodami
- ✅ **Database installation** - SQL příkazy se nyní spouštějí jednotlivě

## 🎯 Instalace modulu

### Možnost 1: Přes PrestaShop Admin (DOPORUČENO)

1. **Přihlaste se** do PrestaShop administrace
2. Jděte na **Moduly** → **Správce modulů** 
3. Klikně<PERSON> na **"Nahrát modul"** (tlačítko vpravo nahoře)
4. **<PERSON><PERSON><PERSON><PERSON> soubor** `cig_catalog-1.0.0.zip`
5. **Nahrajte** a počkejte na instalaci
6. **Aktivujte** modul po úspěšné instalaci

### Možnost 2: Přes FTP

1. **Stáhněte** `cig_catalog-1.0.0.zip`
2. **Rozbalte** ZIP soubor
3. **Nahrajte** složku `cig_catalog` do `modules/` na serveru
4. **Nastavte oprávnění:**
   ```bash
   chmod 755 modules/cig_catalog -R
   chmod 777 modules/cig_catalog/uploads -R
   ```
5. V **PrestaShop admin** → **Moduly** → najděte "CIG Catalog" → **Instalovat**

## ✅ Ověření instalace

Po úspěšné instalaci byste měli vidět:

1. **V admin menu:**
   - Nový tab "Katalogy" v sekci "Vylepšení"

2. **Nové stránky v admin:**
   - `Katalogy` - Správa katalogů
   - `Objednávky katalogů` - Správa objednávek  
   - `Email konfigurace` - Nastavení emailů

3. **Frontend stránka:**
   - `/module/cig_catalog/catalog` - Zobrazení katalogů

## 🔧 Základní konfigurace

### 1. Email nastavení
```
Admin → CIG Catalog → Email konfigurace

Nastavte:
- Admin email pro notifikace
- SMTP server (volitelné)
- Test email funkcionalitu
```

### 2. Vytvoření prvního katalogu
```
Admin → CIG Catalog → Katalogy → Přidat nový

Vyplňte:
- Název (CS/EN)
- Popis
- Nahrajte obrázek
- Nahrajte soubor katalogu (PDF)
- Aktivujte katalog
```

### 3. Test frontend
```
Navštivte: /module/cig_catalog/catalog
Otestujte:
- Zobrazení katalogů
- Vyhledávání
- Objednávkový formulář
```

## 🐛 Řešení problémů

### Problém: "SQL syntax error"
**Řešení:** Použijte nový ZIP balíček - problém byl opraven

### Problém: "clearCache() error"  
**Řešení:** Použijte nový ZIP balíček - konflikt byl vyřešen

### Problém: "Permission denied"
**Řešení:** 
```bash
chmod 755 modules/cig_catalog -R
chmod 777 modules/cig_catalog/uploads -R
```

### Problém: "Module not found"
**Řešení:** 
1. Zkontrolujte, že složka je v `modules/cig_catalog/`
2. Zkontrolujte, že existuje soubor `cig_catalog.php`
3. Obnovte cache: Admin → Pokročilé parametry → Výkon → Vymazat cache

### Problém: "Database tables not created"
**Řešení:**
1. Odinstalujte modul
2. Znovu nainstalujte s novým ZIP balíčkem
3. Zkontrolujte databázi - měly by existovat tabulky:
   - `ps_cig_catalog`
   - `ps_cig_catalog_lang` 
   - `ps_cig_catalog_order`
   - `ps_cig_catalog_config`

## 📞 Podpora

Pokud máte problémy s instalací:

1. **Zkontrolujte logy:**
   - PrestaShop: `var/logs/`
   - Server: Apache/Nginx error log

2. **Systémové požadavky:**
   - PrestaShop 8.2.0+
   - PHP 7.4+ (doporučeno 8.1+)
   - MySQL 5.7+ nebo MariaDB 10.3+
   - PHP extensions: GD, cURL, ZIP, JSON

3. **Debug mode:**
   ```php
   // V config/defines.inc.php
   define('_PS_MODE_DEV_', true);
   ```

## 🎉 Po úspěšné instalaci

Modul je připraven k použití! Můžete:

- ✅ Vytvářet a spravovat katalogy
- ✅ Přijímat objednávky katalogů
- ✅ Odesílat email notifikace
- ✅ Sledovat statistiky stahování
- ✅ Konfigurovat vzhled a chování

**Užijte si nový katalogový systém!** 🚀

---

**Verze:** 1.0.0  
**Datum:** 2024-12-19  
**Autor:** CIG Development Team
