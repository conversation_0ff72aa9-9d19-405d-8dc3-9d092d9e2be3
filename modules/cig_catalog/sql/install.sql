CREATE TABLE IF NOT EXISTS `PREFIX_cig_catalog` (
    `id_catalog` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `active` tinyint(1) unsigned NOT NULL DEFAULT '1',
    `position` int(10) unsigned NOT NULL DEFAULT '0',
    `is_new` tinyint(1) unsigned NOT NULL DEFAULT '0',
    `image` varchar(255) DEFAULT NULL,
    `catalog_file` varchar(255) DEFAULT NULL,
    `catalog_url` varchar(500) DEFAULT NULL,
    `download_count` int(10) unsigned NOT NULL DEFAULT '0',
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_catalog`),
    KEY `active` (`active`),
    KEY `position` (`position`),
    KEY `is_new` (`is_new`),
    KEY `date_add` (`date_add`)
) ENGINE=ENGINE_TYPE DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
