{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
<div class="content-div">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="card-title">
                                <i class="material-icons">email</i>
                                {{ pageTitle }}
                            </h3>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_catalog_index') }}" class="btn btn-outline-secondary">
                                <i class="material-icons">arrow_back</i>
                                Zpět na katalogy
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    {% if form %}
                        {{ form_start(form, {'attr': {'class': 'email-config-form', 'novalidate': 'novalidate'}}) }}
                        
                        <div class="row">
                            <div class="col-lg-8">
                                <!-- <PERSON>áklad<PERSON><PERSON> nastavení -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="material-icons">settings</i>
                                            Základní nastavení
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            {{ form_label(form.email_recipients) }}
                                            {{ form_widget(form.email_recipients) }}
                                            {{ form_help(form.email_recipients) }}
                                            {{ form_errors(form.email_recipients) }}
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    {{ form_label(form.email_from_name) }}
                                                    {{ form_widget(form.email_from_name) }}
                                                    {{ form_help(form.email_from_name) }}
                                                    {{ form_errors(form.email_from_name) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    {{ form_label(form.email_from_address) }}
                                                    {{ form_widget(form.email_from_address) }}
                                                    {{ form_help(form.email_from_address) }}
                                                    {{ form_errors(form.email_from_address) }}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-check">
                                            {{ form_widget(form.send_confirmation_to_customer) }}
                                            {{ form_label(form.send_confirmation_to_customer) }}
                                            {{ form_help(form.send_confirmation_to_customer) }}
                                            {{ form_errors(form.send_confirmation_to_customer) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Email předměty -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="material-icons">subject</i>
                                            Předměty emailů
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            {{ form_label(form.email_subject_order) }}
                                            {{ form_widget(form.email_subject_order) }}
                                            {{ form_help(form.email_subject_order) }}
                                            {{ form_errors(form.email_subject_order) }}
                                        </div>

                                        <div class="form-group">
                                            {{ form_label(form.email_subject_confirmation) }}
                                            {{ form_widget(form.email_subject_confirmation) }}
                                            {{ form_help(form.email_subject_confirmation) }}
                                            {{ form_errors(form.email_subject_confirmation) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Email šablony -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="material-icons">description</i>
                                            Email šablony
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            {{ form_label(form.email_template_order) }}
                                            {{ form_widget(form.email_template_order) }}
                                            {{ form_help(form.email_template_order) }}
                                            {{ form_errors(form.email_template_order) }}
                                        </div>

                                        <div class="form-group">
                                            {{ form_label(form.email_template_confirmation) }}
                                            {{ form_widget(form.email_template_confirmation) }}
                                            {{ form_help(form.email_template_confirmation) }}
                                            {{ form_errors(form.email_template_confirmation) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- SMTP nastavení -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="material-icons">mail_outline</i>
                                            SMTP nastavení
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check mb-3">
                                            {{ form_widget(form.smtp_enabled) }}
                                            {{ form_label(form.smtp_enabled) }}
                                            {{ form_help(form.smtp_enabled) }}
                                            {{ form_errors(form.smtp_enabled) }}
                                        </div>

                                        <div id="smtp-settings" style="display: none;">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group">
                                                        {{ form_label(form.smtp_host) }}
                                                        {{ form_widget(form.smtp_host) }}
                                                        {{ form_help(form.smtp_host) }}
                                                        {{ form_errors(form.smtp_host) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        {{ form_label(form.smtp_port) }}
                                                        {{ form_widget(form.smtp_port) }}
                                                        {{ form_help(form.smtp_port) }}
                                                        {{ form_errors(form.smtp_port) }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        {{ form_label(form.smtp_encryption) }}
                                                        {{ form_widget(form.smtp_encryption) }}
                                                        {{ form_errors(form.smtp_encryption) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        {{ form_label(form.smtp_username) }}
                                                        {{ form_widget(form.smtp_username) }}
                                                        {{ form_help(form.smtp_username) }}
                                                        {{ form_errors(form.smtp_username) }}
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        {{ form_label(form.smtp_password) }}
                                                        {{ form_widget(form.smtp_password) }}
                                                        {{ form_help(form.smtp_password) }}
                                                        {{ form_errors(form.smtp_password) }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tlačítka -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="btn-group">
                                            {{ form_widget(form.submit) }}
                                            {{ form_widget(form.test_email) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sidebar s nápovědou -->
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="material-icons">help</i>
                                            Nápověda
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <h6>Dostupné proměnné v šablonách:</h6>
                                        <ul class="list-unstyled">
                                            <li><code>{{catalog_title}}</code> - Název katalogu</li>
                                            <li><code>{{order_id}}</code> - Číslo objednávky</li>
                                            <li><code>{{first_name}}</code> - Jméno zákazníka</li>
                                            <li><code>{{last_name}}</code> - Příjmení zákazníka</li>
                                            <li><code>{{company_name}}</code> - Název firmy</li>
                                            <li><code>{{email}}</code> - Email zákazníka</li>
                                            <li><code>{{phone}}</code> - Telefon zákazníka</li>
                                            <li><code>{{address}}</code> - Adresa zákazníka</li>
                                            <li><code>{{note}}</code> - Poznámka k objednávce</li>
                                            <li><code>{{date_add}}</code> - Datum objednávky</li>
                                        </ul>

                                        <hr>

                                        <h6>SMTP nastavení:</h6>
                                        <p class="small text-muted">
                                            Pro Gmail použijte:<br>
                                            <strong>Server:</strong> smtp.gmail.com<br>
                                            <strong>Port:</strong> 587<br>
                                            <strong>Šifrování:</strong> TLS
                                        </p>

                                        <hr>

                                        <h6>Test emailu:</h6>
                                        <div class="form-group">
                                            <label for="test-email-address">Email pro test:</label>
                                            <input type="email" id="test-email-address" class="form-control form-control-sm" 
                                                   placeholder="<EMAIL>">
                                            <button type="button" id="send-test-email" class="btn btn-sm btn-outline-info mt-2">
                                                <i class="material-icons">send</i>
                                                Odeslat test
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{ form_end(form) }}
                    {% else %}
                        <div class="alert alert-danger">
                            <h4>Chyba při načítání konfigurace</h4>
                            <p>Nepodařilo se načíst konfiguraci emailů. Zkuste stránku obnovit.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle SMTP settings
        const smtpEnabled = document.getElementById('email_configuration_smtp_enabled');
        const smtpSettings = document.getElementById('smtp-settings');
        
        function toggleSmtpSettings() {
            if (smtpEnabled.checked) {
                smtpSettings.style.display = 'block';
            } else {
                smtpSettings.style.display = 'none';
            }
        }
        
        if (smtpEnabled) {
            toggleSmtpSettings(); // Initial state
            smtpEnabled.addEventListener('change', toggleSmtpSettings);
        }
        
        // Test email functionality
        const testEmailBtn = document.getElementById('send-test-email');
        const testEmailInput = document.getElementById('test-email-address');
        
        if (testEmailBtn && testEmailInput) {
            testEmailBtn.addEventListener('click', function() {
                const email = testEmailInput.value.trim();
                if (!email) {
                    alert('Zadejte email adresu pro test.');
                    return;
                }
                
                // Collect form data
                const formData = new FormData();
                const form = document.querySelector('.email-config-form');
                const formElements = form.querySelectorAll('input, textarea, select');
                
                formElements.forEach(element => {
                    if (element.type === 'checkbox') {
                        formData.append(element.name, element.checked ? '1' : '0');
                    } else {
                        formData.append(element.name, element.value);
                    }
                });
                
                formData.append('test_email_address', email);
                formData.append('_token', '{{ csrf_token('test_email') }}');
                
                // Send test email
                testEmailBtn.disabled = true;
                testEmailBtn.innerHTML = '<i class="material-icons">hourglass_empty</i> Odesílání...';
                
                fetch('{{ path('admin_catalog_email_test') }}', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✓ ' + data.message);
                    } else {
                        alert('✗ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Chyba při odesílání testovacího emailu.');
                })
                .finally(() => {
                    testEmailBtn.disabled = false;
                    testEmailBtn.innerHTML = '<i class="material-icons">send</i> Odeslat test';
                });
            });
        }
    });
    </script>
{% endblock %}
