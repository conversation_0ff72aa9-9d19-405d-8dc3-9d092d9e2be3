<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Repository;

use DbQuery;
use Exception;
use DateTime;

/**
 * Catalog order repository class
 * 
 * Handles all database operations for catalog order entities
 */
class CatalogOrderRepository extends BaseRepository
{
    /**
     * Create new catalog order
     *
     * @param array $orderData Order data
     * @return int New order ID
     * @throws Exception
     */
    public function create(array $orderData): int
    {
        $data = [
            'id_catalog' => $this->validateInt($orderData['id_catalog']),
            'customer_name' => $this->validateString($orderData['customer_name']),
            'customer_email' => $this->validateString($orderData['customer_email']),
            'customer_phone' => $this->validateString($orderData['customer_phone'] ?? ''),
            'company_name' => $this->validateString($orderData['company_name'] ?? ''),
            'company_ico' => $this->validateString($orderData['company_ico'] ?? ''),
            'address' => $this->validateString($orderData['address'] ?? ''),
            'city' => $this->validateString($orderData['city'] ?? ''),
            'postal_code' => $this->validateString($orderData['postal_code'] ?? ''),
            'country' => $this->validateString($orderData['country'] ?? ''),
            'note' => $this->validateString($orderData['note'] ?? ''),
            'ip_address' => $this->validateString($orderData['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? ''),
            'user_agent' => $this->validateString($orderData['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? ''),
            'status' => $this->validateString($orderData['status'] ?? 'pending'),
            'admin_note' => $this->validateString($orderData['admin_note'] ?? ''),
            'date_add' => date('Y-m-d H:i:s'),
            'date_upd' => date('Y-m-d H:i:s')
        ];

        $sql = 'INSERT INTO ' . $this->getTable('cig_catalog_order') . ' (' . 
               implode(', ', array_keys($data)) . ') VALUES (' . 
               implode(', ', array_fill(0, count($data), '?')) . ')';

        if (!$this->executeUpdate($sql, array_values($data))) {
            throw new Exception('Failed to create catalog order');
        }

        return $this->getLastInsertId();
    }

    /**
     * Find orders by catalog ID
     *
     * @param int $catalogId Catalog ID
     * @return array Array of orders
     * @throws Exception
     */
    public function findByCatalogId(int $catalogId): array
    {
        $sql = $this->createQuery();
        $sql->select('co.*, c.name as catalog_name');
        $sql->from($this->getTable('cig_catalog_order'), 'co');
        $sql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');
        $sql->where('co.id_catalog = ' . $this->validateInt($catalogId));
        $sql->orderBy('co.date_add DESC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Find orders by email
     *
     * @param string $email Customer email
     * @return array Array of orders
     * @throws Exception
     */
    public function findByEmail(string $email): array
    {
        $email = $this->validateString($email);
        if (empty($email)) {
            return [];
        }

        $sql = $this->createQuery();
        $sql->select('co.*, cl.name as catalog_name');
        $sql->from($this->getTable('cig_catalog_order'), 'co');
        $sql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');
        $sql->where('co.customer_email = "' . $this->escape($email) . '"');
        $sql->orderBy('co.date_add DESC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Find recent orders
     *
     * @param int $days Number of days to look back (default: 30)
     * @return array Array of recent orders
     * @throws Exception
     */
    public function findRecent(int $days = 30): array
    {
        $days = max(1, $days);
        $dateLimit = date('Y-m-d H:i:s', strtotime('-' . $days . ' days'));

        $sql = $this->createQuery();
        $sql->select('co.*, cl.name as catalog_name');
        $sql->from($this->getTable('cig_catalog_order'), 'co');
        $sql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');
        $sql->where('co.date_add >= "' . $this->escape($dateLimit) . '"');
        $sql->orderBy('co.date_add DESC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Get order statistics
     *
     * @return array Statistics data
     * @throws Exception
     */
    public function getStatistics(): array
    {
        $stats = [];

        // Total orders
        $stats['total'] = (int) $this->executeValue('SELECT COUNT(*) FROM ' . $this->getTable('cig_catalog_order'));

        // Orders by status
        $statusSql = $this->createQuery();
        $statusSql->select('status, COUNT(*) as count');
        $statusSql->from($this->getTable('cig_catalog_order'));
        $statusSql->groupBy('status');

        $statusResults = $this->executeQuery($statusSql->build());
        $stats['by_status'] = [
            'pending' => 0,
            'processed' => 0,
            'sent' => 0,
            'cancelled' => 0
        ];

        foreach ($statusResults as $result) {
            $stats['by_status'][$result['status']] = (int) $result['count'];
        }

        // Recent orders (last 30 days)
        $recentDate = date('Y-m-d H:i:s', strtotime('-30 days'));
        $stats['recent'] = (int) $this->executeValue(
            'SELECT COUNT(*) FROM ' . $this->getTable('cig_catalog_order') . ' WHERE date_add >= ?',
            [$recentDate]
        );

        // Most requested catalogs
        $popularSql = $this->createQuery();
        $popularSql->select('co.id_catalog, cl.name, COUNT(*) as order_count');
        $popularSql->from($this->getTable('cig_catalog_order'), 'co');
        $popularSql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $popularSql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');
        $popularSql->groupBy('co.id_catalog');
        $popularSql->orderBy('order_count DESC');
        $popularSql->limit(5);

        $stats['popular_catalogs'] = $this->executeQuery($popularSql->build());

        return $stats;
    }

    /**
     * Export orders to CSV format
     *
     * @param array $filters Export filters
     * @return string CSV content
     * @throws Exception
     */
    public function exportToCSV(array $filters = []): string
    {
        $sql = $this->createQuery();
        $sql->select('co.*, cl.name as catalog_name');
        $sql->from($this->getTable('cig_catalog_order'), 'co');
        $sql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');

        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $sql->where('co.status = "' . $this->escape($filters['status']) . '"');
        }

        if (isset($filters['date_from']) && !empty($filters['date_from'])) {
            $sql->where('co.date_add >= "' . $this->escape($filters['date_from']) . '"');
        }

        if (isset($filters['date_to']) && !empty($filters['date_to'])) {
            $sql->where('co.date_add <= "' . $this->escape($filters['date_to']) . ' 23:59:59"');
        }

        if (isset($filters['catalog_id']) && !empty($filters['catalog_id'])) {
            $sql->where('co.id_catalog = ' . $this->validateInt($filters['catalog_id']));
        }

        $sql->orderBy('co.date_add DESC');

        $orders = $this->executeQuery($sql->build());

        // Generate CSV content
        $csv = [];
        $csv[] = [
            'ID',
            'Catalog',
            'Customer Name',
            'Email',
            'Phone',
            'Company',
            'ICO',
            'Address',
            'City',
            'Postal Code',
            'Country',
            'Status',
            'Note',
            'Admin Note',
            'Date Added',
            'Date Updated'
        ];

        foreach ($orders as $order) {
            $csv[] = [
                $order['id_order'],
                $order['catalog_name'] ?? '',
                $order['customer_name'],
                $order['customer_email'],
                $order['customer_phone'] ?? '',
                $order['company_name'] ?? '',
                $order['company_ico'] ?? '',
                $order['address'] ?? '',
                $order['city'] ?? '',
                $order['postal_code'] ?? '',
                $order['country'] ?? '',
                $order['status'],
                $order['note'] ?? '',
                $order['admin_note'] ?? '',
                $order['date_add'],
                $order['date_upd']
            ];
        }

        // Convert to CSV string
        $output = '';
        foreach ($csv as $row) {
            $output .= '"' . implode('","', array_map('str_replace', ['""', '"'], ['"', '""'], $row)) . '"' . "\n";
        }

        return $output;
    }

    /**
     * Get orders by date range
     *
     * @param DateTime $from Start date
     * @param DateTime $to End date
     * @return array Array of orders in date range
     * @throws Exception
     */
    public function getOrdersByDateRange(DateTime $from, DateTime $to): array
    {
        $sql = $this->createQuery();
        $sql->select('co.*, cl.name as catalog_name');
        $sql->from($this->getTable('cig_catalog_order'), 'co');
        $sql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');
        $sql->where('co.date_add >= "' . $from->format('Y-m-d H:i:s') . '"');
        $sql->where('co.date_add <= "' . $to->format('Y-m-d H:i:s') . '"');
        $sql->orderBy('co.date_add DESC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Update order status
     *
     * @param int $orderId Order ID
     * @param string $status New status
     * @param string $adminNote Optional admin note
     * @return bool Success status
     * @throws Exception
     */
    public function updateStatus(int $orderId, string $status, string $adminNote = ''): bool
    {
        $validStatuses = ['pending', 'processed', 'sent', 'cancelled'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('Invalid order status: ' . $status);
        }

        $data = [
            'status' => $status,
            'date_upd' => date('Y-m-d H:i:s')
        ];

        if (!empty($adminNote)) {
            $data['admin_note'] = $this->validateString($adminNote);
        }

        $setParts = [];
        $values = [];
        foreach ($data as $field => $value) {
            $setParts[] = $field . ' = ?';
            $values[] = $value;
        }
        $values[] = $this->validateInt($orderId);

        $sql = 'UPDATE ' . $this->getTable('cig_catalog_order') . ' SET ' . 
               implode(', ', $setParts) . ' WHERE id_order = ?';

        return $this->executeUpdate($sql, $values);
    }

    /**
     * Delete order
     *
     * @param int $orderId Order ID
     * @return bool Success status
     * @throws Exception
     */
    public function delete(int $orderId): bool
    {
        $sql = 'DELETE FROM ' . $this->getTable('cig_catalog_order') . ' WHERE id_order = ?';
        return $this->executeUpdate($sql, [$this->validateInt($orderId)]);
    }

    /**
     * Find order by ID
     *
     * @param int $orderId Order ID
     * @return array|null Order data or null if not found
     * @throws Exception
     */
    public function findById(int $orderId): ?array
    {
        $sql = $this->createQuery();
        $sql->select('co.*, cl.name as catalog_name');
        $sql->from($this->getTable('cig_catalog_order'), 'co');
        $sql->leftJoin($this->getTable('cig_catalog'), 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = 1');
        $sql->where('co.id_order = ' . $this->validateInt($orderId));

        $result = $this->executeQuery($sql->build());
        return !empty($result) ? $result[0] : null;
    }
}
