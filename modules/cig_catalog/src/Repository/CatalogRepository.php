<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Repository;

use DbQuery;
use Exception;
use Context;

/**
 * Catalog repository class
 * 
 * Handles all database operations for catalog entities with multilingual support
 */
class CatalogRepository extends BaseRepository
{
    /**
     * Find catalog by ID with language support
     *
     * @param int $id Catalog ID
     * @param int|null $langId Language ID (optional, uses context language if null)
     * @return object|null Catalog object or null if not found
     * @throws Exception
     */
    public function findById(int $id, ?int $langId = null)
    {
        if ($langId === null) {
            $langId = \Context::getContext()->language->id;
        }

        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl',
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('c.id_catalog = ' . $this->validateInt($id));

        $result = $this->executeQuery($sql->build());
        if (!empty($result)) {
            // Convert array to object for compatibility
            return (object) $result[0];
        }
        return null;
    }

    /**
     * Find all catalogs with language support
     *
     * @param int $langId Language ID
     * @return array Array of catalog data
     * @throws Exception
     */
    public function findAll(int $langId): array
    {
        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->orderBy('c.position ASC, c.id_catalog ASC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Find all active catalogs with language support
     *
     * @param int $langId Language ID
     * @return array Array of active catalog data
     * @throws Exception
     */
    public function findAllActive(int $langId): array
    {
        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('c.active = 1');
        $sql->orderBy('c.position ASC, c.id_catalog ASC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Create new catalog
     *
     * @param array $data Catalog data
     * @return int New catalog ID
     * @throws Exception
     */
    public function create(array $data): int
    {
        if (!$this->beginTransaction()) {
            throw new Exception('Failed to start transaction');
        }

        try {
            // Prepare main catalog data
            $catalogData = [
                'active' => isset($data['active']) ? (int) $data['active'] : 1,
                'position' => isset($data['position']) ? $this->validateInt($data['position']) : $this->getMaxPosition() + 1,
                'is_new' => isset($data['is_new']) ? (int) $data['is_new'] : 0,
                'image' => $this->validateString($data['image'] ?? ''),
                'catalog_file' => $this->validateString($data['catalog_file'] ?? ''),
                'catalog_url' => $this->validateString($data['catalog_url'] ?? ''),
                'download_count' => 0,
                'date_add' => date('Y-m-d H:i:s'),
                'date_upd' => date('Y-m-d H:i:s')
            ];

            // Insert main catalog record
            $sql = 'INSERT INTO ' . $this->getTable('cig_catalog') . ' (' . 
                   implode(', ', array_keys($catalogData)) . ') VALUES (' . 
                   implode(', ', array_fill(0, count($catalogData), '?')) . ')';

            if (!$this->executeUpdate($sql, array_values($catalogData))) {
                throw new Exception('Failed to insert catalog');
            }

            $catalogId = $this->getLastInsertId();

            // Insert multilingual data
            if (isset($data['lang']) && is_array($data['lang'])) {
                foreach ($data['lang'] as $langId => $langData) {
                    $langRecord = [
                        'id_catalog' => $catalogId,
                        'id_lang' => $this->validateInt($langId),
                        'name' => $this->validateString($langData['name'] ?? ''),
                        'description' => $this->validateString($langData['description'] ?? ''),
                        'short_description' => $this->validateString($langData['short_description'] ?? ''),
                        'meta_title' => $this->validateString($langData['meta_title'] ?? ''),
                        'meta_description' => $this->validateString($langData['meta_description'] ?? ''),
                        'slug' => $this->validateString($langData['slug'] ?? '')
                    ];

                    $langSql = 'INSERT INTO ' . $this->getTable('cig_catalog_lang') . ' (' . 
                               implode(', ', array_keys($langRecord)) . ') VALUES (' . 
                               implode(', ', array_fill(0, count($langRecord), '?')) . ')';

                    if (!$this->executeUpdate($langSql, array_values($langRecord))) {
                        throw new Exception('Failed to insert catalog language data');
                    }
                }
            }

            $this->commit();
            return $catalogId;

        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Update existing catalog
     *
     * @param int $id Catalog ID
     * @param array $data Updated catalog data
     * @return bool Success status
     * @throws Exception
     */
    public function update(int $id, array $data): bool
    {
        if (!$this->beginTransaction()) {
            throw new Exception('Failed to start transaction');
        }

        try {
            // Prepare main catalog data
            $catalogData = [];
            $allowedFields = ['active', 'position', 'is_new', 'image', 'catalog_file', 'catalog_url', 'download_count'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $catalogData[$field] = $field === 'active' || $field === 'is_new' ? 
                        (int) $data[$field] : $this->validateString($data[$field]);
                }
            }

            if (!empty($catalogData)) {
                $catalogData['date_upd'] = date('Y-m-d H:i:s');
                
                $setParts = [];
                $values = [];
                foreach ($catalogData as $field => $value) {
                    $setParts[] = $field . ' = ?';
                    $values[] = $value;
                }
                $values[] = $this->validateInt($id);

                $sql = 'UPDATE ' . $this->getTable('cig_catalog') . ' SET ' . 
                       implode(', ', $setParts) . ' WHERE id_catalog = ?';

                if (!$this->executeUpdate($sql, $values)) {
                    throw new Exception('Failed to update catalog');
                }
            }

            // Update multilingual data
            if (isset($data['lang']) && is_array($data['lang'])) {
                foreach ($data['lang'] as $langId => $langData) {
                    $langRecord = [];
                    $langFields = ['name', 'description', 'short_description', 'meta_title', 'meta_description', 'slug'];
                    
                    foreach ($langFields as $field) {
                        if (isset($langData[$field])) {
                            $langRecord[$field] = $this->validateString($langData[$field]);
                        }
                    }

                    if (!empty($langRecord)) {
                        $setParts = [];
                        $values = [];
                        foreach ($langRecord as $field => $value) {
                            $setParts[] = $field . ' = ?';
                            $values[] = $value;
                        }
                        $values[] = $this->validateInt($id);
                        $values[] = $this->validateInt($langId);

                        $langSql = 'UPDATE ' . $this->getTable('cig_catalog_lang') . ' SET ' . 
                                   implode(', ', $setParts) . ' WHERE id_catalog = ? AND id_lang = ?';

                        if (!$this->executeUpdate($langSql, $values)) {
                            throw new Exception('Failed to update catalog language data');
                        }
                    }
                }
            }

            $this->commit();
            return true;

        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Delete catalog
     *
     * @param int $id Catalog ID
     * @return bool Success status
     * @throws Exception
     */
    public function delete(int $id): bool
    {
        if (!$this->beginTransaction()) {
            throw new Exception('Failed to start transaction');
        }

        try {
            // Get catalog position for reordering
            $position = $this->executeValue(
                'SELECT position FROM ' . $this->getTable('cig_catalog') . ' WHERE id_catalog = ?',
                [$this->validateInt($id)]
            );

            // Delete multilingual data first (due to foreign key)
            $this->executeUpdate(
                'DELETE FROM ' . $this->getTable('cig_catalog_lang') . ' WHERE id_catalog = ?',
                [$this->validateInt($id)]
            );

            // Delete main catalog record
            $result = $this->executeUpdate(
                'DELETE FROM ' . $this->getTable('cig_catalog') . ' WHERE id_catalog = ?',
                [$this->validateInt($id)]
            );

            if ($result && $position !== false) {
                // Reorder remaining catalogs
                $this->reorderAfterDelete((int) $position);
            }

            $this->commit();
            return $result;

        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Find catalogs ordered by position
     *
     * @param int $langId Language ID
     * @return array Array of catalogs ordered by position
     * @throws Exception
     */
    public function findByPosition(int $langId): array
    {
        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl', 
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('c.active = 1');
        $sql->orderBy('c.position ASC, c.id_catalog ASC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Find new catalogs
     *
     * @param int $langId Language ID
     * @return array Array of new catalogs
     * @throws Exception
     */
    public function findNewCatalogs(int $langId): array
    {
        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl',
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('c.active = 1 AND c.is_new = 1');
        $sql->orderBy('c.date_add DESC, c.position ASC');

        return $this->executeQuery($sql->build());
    }

    /**
     * Find catalogs with pagination
     *
     * @param int $page Page number (1-based)
     * @param int $limit Items per page
     * @param int $langId Language ID
     * @return array Array of catalog data
     * @throws Exception
     */
    public function findWithPagination(int $page, int $limit, int $langId): array
    {
        $page = max(1, $page);
        $limit = max(1, $limit);
        $offset = ($page - 1) * $limit;

        // Get paginated data
        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl',
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->orderBy('c.position ASC, c.id_catalog ASC');
        $sql->limit($limit, $offset);

        return $this->executeQuery($sql->build());
    }

    /**
     * Search catalogs by query with pagination
     *
     * @param string $query Search query
     * @param int $langId Language ID
     * @param int $page Page number (optional)
     * @param int $limit Items per page (optional)
     * @return array Array of matching catalogs
     * @throws Exception
     */
    public function search(string $query, int $langId, int $page = 1, int $limit = 0): array
    {
        $query = $this->validateString($query);
        if (empty($query)) {
            return [];
        }

        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl',
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('(cl.name LIKE "%' . $this->escape($query) . '%" OR ' .
                    'cl.description LIKE "%' . $this->escape($query) . '%" OR ' .
                    'cl.short_description LIKE "%' . $this->escape($query) . '%")');
        $sql->orderBy('c.position ASC, c.id_catalog ASC');

        if ($limit > 0) {
            $page = max(1, $page);
            $offset = ($page - 1) * $limit;
            $sql->limit($limit, $offset);
        }

        return $this->executeQuery($sql->build());
    }

    /**
     * Update catalog positions
     *
     * @param array $positions Array of id => position pairs
     * @return bool Success status
     * @throws Exception
     */
    public function updatePositions(array $positions): bool
    {
        if (empty($positions)) {
            return true;
        }

        if (!$this->beginTransaction()) {
            throw new Exception('Failed to start transaction');
        }

        try {
            foreach ($positions as $id => $position) {
                $sql = 'UPDATE ' . $this->getTable('cig_catalog') . ' SET position = ?, date_upd = ? WHERE id_catalog = ?';
                $this->executeUpdate($sql, [
                    $this->validateInt($position),
                    date('Y-m-d H:i:s'),
                    $this->validateInt($id)
                ]);
            }

            $this->commit();
            return true;

        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Get maximum position value
     *
     * @return int Maximum position
     * @throws Exception
     */
    public function getMaxPosition(): int
    {
        $sql = 'SELECT MAX(position) FROM ' . $this->getTable('cig_catalog');
        $result = $this->executeValue($sql);
        return $result !== false ? (int) $result : 0;
    }

    /**
     * Reorder catalogs after deletion
     *
     * @param int $deletedPosition Position of deleted catalog
     * @return bool Success status
     * @throws Exception
     */
    public function reorderAfterDelete(int $deletedPosition): bool
    {
        $sql = 'UPDATE ' . $this->getTable('cig_catalog') . ' SET position = position - 1, date_upd = ? WHERE position > ?';
        return $this->executeUpdate($sql, [date('Y-m-d H:i:s'), $deletedPosition]);
    }

    /**
     * Find catalogs with advanced filters using Query Builder
     *
     * @param array $filters Filter criteria
     * @param int $langId Language ID
     * @return array Array of filtered catalogs
     * @throws Exception
     */
    public function findActiveWithFilters(array $filters, int $langId): array
    {
        $sql = $this->createQuery();
        $sql->select('c.*, cl.name, cl.description, cl.short_description, cl.meta_title, cl.meta_description, cl.slug');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl',
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('c.active = 1');

        // Apply filters
        if (isset($filters['is_new']) && $filters['is_new'] !== '') {
            $sql->where('c.is_new = ' . (int) $filters['is_new']);
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $searchTerm = $this->escape($filters['search']);
            $sql->where('(cl.name LIKE "%' . $searchTerm . '%" OR ' .
                        'cl.description LIKE "%' . $searchTerm . '%" OR ' .
                        'cl.short_description LIKE "%' . $searchTerm . '%")');
        }

        if (isset($filters['date_from']) && !empty($filters['date_from'])) {
            $sql->where('c.date_add >= "' . $this->escape($filters['date_from']) . '"');
        }

        if (isset($filters['date_to']) && !empty($filters['date_to'])) {
            $sql->where('c.date_add <= "' . $this->escape($filters['date_to']) . ' 23:59:59"');
        }

        // Ordering
        $orderBy = isset($filters['order_by']) ? $this->escape($filters['order_by']) : 'position';
        $orderWay = isset($filters['order_way']) && strtoupper($filters['order_way']) === 'DESC' ? 'DESC' : 'ASC';

        if ($orderBy === 'name') {
            $sql->orderBy('cl.name ' . $orderWay);
        } elseif ($orderBy === 'date_add') {
            $sql->orderBy('c.date_add ' . $orderWay);
        } else {
            $sql->orderBy('c.is_new DESC, c.position ASC');
        }

        return $this->executeQuery($sql->build());
    }

    /**
     * Increment download count for catalog
     *
     * @param int $id Catalog ID
     * @return bool Success status
     * @throws Exception
     */
    public function incrementDownloadCount(int $id): bool
    {
        $sql = 'UPDATE ' . $this->getTable('cig_catalog') . ' SET download_count = download_count + 1, date_upd = ? WHERE id_catalog = ?';
        return $this->executeUpdate($sql, [date('Y-m-d H:i:s'), $this->validateInt($id)]);
    }

    /**
     * Get catalog statistics
     *
     * @return array Statistics data
     * @throws Exception
     */
    public function getStatistics(): array
    {
        $stats = [];

        // Total catalogs
        $stats['total'] = (int) $this->executeValue('SELECT COUNT(*) FROM ' . $this->getTable('cig_catalog'));

        // Active catalogs
        $stats['active'] = (int) $this->executeValue('SELECT COUNT(*) FROM ' . $this->getTable('cig_catalog') . ' WHERE active = 1');

        // New catalogs
        $stats['new'] = (int) $this->executeValue('SELECT COUNT(*) FROM ' . $this->getTable('cig_catalog') . ' WHERE is_new = 1');

        // Total downloads
        $stats['total_downloads'] = (int) $this->executeValue('SELECT SUM(download_count) FROM ' . $this->getTable('cig_catalog'));

        return $stats;
    }



    /**
     * Count search results
     *
     * @param string $search Search query
     * @param int $langId Language ID
     * @return int Number of matching catalogs
     * @throws Exception
     */
    public function countSearch(string $search, int $langId): int
    {
        $search = $this->validateString($search);
        if (empty($search)) {
            return 0;
        }

        $sql = $this->createQuery();
        $sql->select('COUNT(*)');
        $sql->from($this->getTable('cig_catalog'), 'c');
        $sql->leftJoin($this->getTable('cig_catalog_lang'), 'cl',
            'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . $this->validateInt($langId));
        $sql->where('(cl.name LIKE "%' . $this->escape($search) . '%" OR ' .
                    'cl.description LIKE "%' . $this->escape($search) . '%" OR ' .
                    'cl.short_description LIKE "%' . $this->escape($search) . '%")');

        return (int) $this->executeValue($sql->build());
    }

    /**
     * Count all catalogs
     *
     * @return int Total number of catalogs
     * @throws Exception
     */
    public function count(): int
    {
        $sql = 'SELECT COUNT(*) FROM ' . $this->getTable('cig_catalog');
        return (int) $this->executeValue($sql);
    }

    /**
     * Get next position for new catalog
     *
     * @return int Next position
     * @throws Exception
     */
    public function getNextPosition(): int
    {
        return $this->getMaxPosition() + 1;
    }
}
