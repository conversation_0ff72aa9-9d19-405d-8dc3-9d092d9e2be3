<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Repository;

use Db;
use DbQuery;
use PrestaShopLogger;
use Exception;
use PDOException;

/**
 * Base repository class providing common database operations
 * 
 * Provides shared functionality for all repository classes including:
 * - Database connection management
 * - Error handling and logging
 * - Transaction support
 * - Query execution with error handling
 */
abstract class BaseRepository
{
    /** @var Db Database connection instance */
    protected $connection;

    /** @var PrestaShopLogger Logger instance */
    protected $logger;

    /** @var string Database table prefix */
    protected $tablePrefix;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->connection = Db::getInstance();
        $this->logger = PrestaShopLogger::getInstance();
        $this->tablePrefix = _DB_PREFIX_;
    }

    /**
     * Execute a SELECT query with error handling
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return array Query results
     * @throws Exception
     */
    protected function executeQuery(string $sql, array $params = []): array
    {
        try {
            if (!empty($params)) {
                // Use prepared statement for parameterized queries
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception('Failed to prepare statement: ' . $this->connection->getMsgError());
                }

                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }

                $result = $stmt->execute();
                if (!$result) {
                    throw new Exception('Query execution failed: ' . $this->connection->getMsgError());
                }

                return $stmt->fetchAll();
            } else {
                // Simple query without parameters
                $result = $this->connection->executeS($sql);
                if ($result === false) {
                    throw new Exception('Query execution failed: ' . $this->connection->getMsgError());
                }
                return $result ?: [];
            }
        } catch (Exception $e) {
            $this->logError('Database query failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Execute a single value query (getValue equivalent)
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return mixed Single value result
     * @throws Exception
     */
    protected function executeValue(string $sql, array $params = [])
    {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception('Failed to prepare statement: ' . $this->connection->getMsgError());
                }

                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }

                $result = $stmt->execute();
                if (!$result) {
                    throw new Exception('Query execution failed: ' . $this->connection->getMsgError());
                }

                $row = $stmt->fetch();
                return $row ? array_values($row)[0] : false;
            } else {
                return $this->connection->getValue($sql);
            }
        } catch (Exception $e) {
            $this->logError('Database value query failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Execute an INSERT, UPDATE or DELETE query
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return bool Success status
     * @throws Exception
     */
    protected function executeUpdate(string $sql, array $params = []): bool
    {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception('Failed to prepare statement: ' . $this->connection->getMsgError());
                }

                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }

                $result = $stmt->execute();
                if (!$result) {
                    throw new Exception('Update query execution failed: ' . $this->connection->getMsgError());
                }

                return true;
            } else {
                return $this->connection->execute($sql);
            }
        } catch (Exception $e) {
            $this->logError('Database update query failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get the last inserted ID
     *
     * @return int Last insert ID
     */
    protected function getLastInsertId(): int
    {
        return (int) $this->connection->Insert_ID();
    }

    /**
     * Start a database transaction
     *
     * @return bool Success status
     */
    protected function beginTransaction(): bool
    {
        try {
            return $this->connection->execute('START TRANSACTION');
        } catch (Exception $e) {
            $this->logError('Failed to start transaction', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Commit a database transaction
     *
     * @return bool Success status
     */
    protected function commit(): bool
    {
        try {
            return $this->connection->execute('COMMIT');
        } catch (Exception $e) {
            $this->logError('Failed to commit transaction', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Rollback a database transaction
     *
     * @return bool Success status
     */
    protected function rollback(): bool
    {
        try {
            return $this->connection->execute('ROLLBACK');
        } catch (Exception $e) {
            $this->logError('Failed to rollback transaction', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Log error message with context
     *
     * @param string $message Error message
     * @param array $context Error context
     */
    protected function logError(string $message, array $context = []): void
    {
        $this->logger->addLog($message . ' - Context: ' . json_encode($context), 3);
    }

    /**
     * Escape string for SQL query
     *
     * @param string $string String to escape
     * @return string Escaped string
     */
    protected function escape(string $string): string
    {
        return pSQL($string);
    }

    /**
     * Create a new DbQuery instance
     *
     * @return DbQuery
     */
    protected function createQuery(): DbQuery
    {
        return new DbQuery();
    }

    /**
     * Get table name with prefix
     *
     * @param string $table Table name without prefix
     * @return string Full table name with prefix
     */
    protected function getTable(string $table): string
    {
        return $this->tablePrefix . $table;
    }

    /**
     * Execute query with retry mechanism
     *
     * @param callable $queryFunction Function that executes the query
     * @param int $maxRetries Maximum number of retries
     * @return mixed Query result
     * @throws Exception
     */
    protected function executeWithRetry(callable $queryFunction, int $maxRetries = 3)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                return $queryFunction();
            } catch (Exception $e) {
                $lastException = $e;
                $attempt++;

                if ($attempt < $maxRetries) {
                    // Wait before retry (exponential backoff)
                    usleep(pow(2, $attempt) * 100000); // 0.2s, 0.4s, 0.8s
                    $this->logError("Query retry attempt {$attempt}", ['error' => $e->getMessage()]);
                }
            }
        }

        throw $lastException;
    }

    /**
     * Validate and sanitize integer value
     *
     * @param mixed $value Value to validate
     * @param int $default Default value if validation fails
     * @return int Validated integer
     */
    protected function validateInt($value, int $default = 0): int
    {
        return is_numeric($value) && $value >= 0 ? (int) $value : $default;
    }

    /**
     * Validate and sanitize string value
     *
     * @param mixed $value Value to validate
     * @param string $default Default value if validation fails
     * @return string Validated string
     */
    protected function validateString($value, string $default = ''): string
    {
        return is_string($value) ? trim($value) : $default;
    }
}
