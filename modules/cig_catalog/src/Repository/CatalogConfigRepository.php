<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Repository;

use DbQuery;
use Exception;

/**
 * Catalog configuration repository class
 * 
 * Handles all database operations for catalog configuration with caching support
 */
class CatalogConfigRepository extends BaseRepository
{
    /** @var array Configuration cache */
    private static $cache = [];

    /** @var bool Cache enabled flag */
    private $cacheEnabled = true;

    /** @var int Cache TTL in seconds */
    private $cacheTtl = 3600;

    /**
     * Get configuration value by name
     *
     * @param string $name Configuration name
     * @param mixed $default Default value if not found
     * @return mixed Configuration value
     * @throws Exception
     */
    public function get(string $name, $default = null)
    {
        $name = $this->validateString($name);
        if (empty($name)) {
            return $default;
        }

        // Check cache first
        if ($this->cacheEnabled && isset(self::$cache[$name])) {
            $cached = self::$cache[$name];
            if ($cached['expires'] > time()) {
                return $cached['value'];
            } else {
                // Cache expired, remove it
                unset(self::$cache[$name]);
            }
        }

        $sql = 'SELECT config_value FROM ' . $this->getTable('cig_catalog_config') . ' WHERE config_key = ?';
        $value = $this->executeValue($sql, [$name]);

        if ($value !== false) {
            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            if ($unserialized !== false || $value === 'b:0;') {
                $value = $unserialized;
            }

            // Cache the value
            if ($this->cacheEnabled) {
                self::$cache[$name] = [
                    'value' => $value,
                    'expires' => time() + $this->cacheTtl
                ];
            }

            return $value;
        }

        return $default;
    }

    /**
     * Set configuration value
     *
     * @param string $name Configuration name
     * @param mixed $value Configuration value
     * @return bool Success status
     * @throws Exception
     */
    public function set(string $name, $value): bool
    {
        $name = $this->validateString($name);
        if (empty($name)) {
            return false;
        }

        // Serialize complex values
        if (is_array($value) || is_object($value)) {
            $value = serialize($value);
        } elseif (is_bool($value)) {
            $value = $value ? '1' : '0';
        } else {
            $value = (string) $value;
        }

        // Check if configuration exists
        $existsSql = 'SELECT id_config FROM ' . $this->getTable('cig_catalog_config') . ' WHERE config_key = ?';
        $configId = $this->executeValue($existsSql, [$name]);

        if ($configId !== false) {
            // Update existing configuration
            $sql = 'UPDATE ' . $this->getTable('cig_catalog_config') . ' SET config_value = ?, date_upd = ? WHERE id_config = ?';
            $result = $this->executeUpdate($sql, [$value, date('Y-m-d H:i:s'), $configId]);
        } else {
            // Insert new configuration
            $sql = 'INSERT INTO ' . $this->getTable('cig_catalog_config') . ' (config_key, config_value, date_add, date_upd) VALUES (?, ?, ?, ?)';
            $now = date('Y-m-d H:i:s');
            $result = $this->executeUpdate($sql, [$name, $value, $now, $now]);
        }

        if ($result) {
            // Update cache
            if ($this->cacheEnabled) {
                $finalValue = $value;
                $unserialized = @unserialize($value);
                if ($unserialized !== false || $value === 'b:0;') {
                    $finalValue = $unserialized;
                }

                self::$cache[$name] = [
                    'value' => $finalValue,
                    'expires' => time() + $this->cacheTtl
                ];
            }
        }

        return $result;
    }

    /**
     * Get all configuration values
     *
     * @return array All configuration values
     * @throws Exception
     */
    public function getAll(): array
    {
        $sql = 'SELECT config_key, config_value FROM ' . $this->getTable('cig_catalog_config') . ' ORDER BY config_key';
        $results = $this->executeQuery($sql);

        $config = [];
        foreach ($results as $row) {
            $value = $row['config_value'];

            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            if ($unserialized !== false || $value === 'b:0;') {
                $value = $unserialized;
            }

            $config[$row['config_key']] = $value;

            // Cache the value
            if ($this->cacheEnabled) {
                self::$cache[$row['config_key']] = [
                    'value' => $value,
                    'expires' => time() + $this->cacheTtl
                ];
            }
        }

        return $config;
    }

    /**
     * Delete configuration value
     *
     * @param string $name Configuration name
     * @return bool Success status
     * @throws Exception
     */
    public function delete(string $name): bool
    {
        $name = $this->validateString($name);
        if (empty($name)) {
            return false;
        }

        $sql = 'DELETE FROM ' . $this->getTable('cig_catalog_config') . ' WHERE config_key = ?';
        $result = $this->executeUpdate($sql, [$name]);

        if ($result) {
            // Remove from cache
            unset(self::$cache[$name]);
        }

        return $result;
    }

    /**
     * Get configuration value with cache
     *
     * @param string $name Configuration name
     * @return mixed Configuration value
     * @throws Exception
     */
    public function getWithCache(string $name)
    {
        return $this->get($name);
    }

    /**
     * Clear configuration cache
     */
    public function clearConfigCache(): void
    {
        self::$cache = [];
    }

    /**
     * Warm up configuration cache
     *
     * @throws Exception
     */
    public function warmupCache(): void
    {
        if (!$this->cacheEnabled) {
            return;
        }

        $this->getAll(); // This will populate the cache
    }

    /**
     * Get configuration values by prefix
     *
     * @param string $prefix Configuration key prefix
     * @return array Configuration values with matching prefix
     * @throws Exception
     */
    public function getByPrefix(string $prefix): array
    {
        $prefix = $this->validateString($prefix);
        if (empty($prefix)) {
            return [];
        }

        $sql = 'SELECT config_key, config_value FROM ' . $this->getTable('cig_catalog_config') . ' WHERE config_key LIKE ? ORDER BY config_key';
        $results = $this->executeQuery($sql, [$prefix . '%']);

        $config = [];
        foreach ($results as $row) {
            $value = $row['config_value'];

            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            if ($unserialized !== false || $value === 'b:0;') {
                $value = $unserialized;
            }

            $config[$row['config_key']] = $value;

            // Cache the value
            if ($this->cacheEnabled) {
                self::$cache[$row['config_key']] = [
                    'value' => $value,
                    'expires' => time() + $this->cacheTtl
                ];
            }
        }

        return $config;
    }

    /**
     * Set multiple configuration values at once
     *
     * @param array $configs Array of key => value pairs
     * @return bool Success status
     * @throws Exception
     */
    public function setMultiple(array $configs): bool
    {
        if (empty($configs)) {
            return true;
        }

        if (!$this->beginTransaction()) {
            throw new Exception('Failed to start transaction');
        }

        try {
            foreach ($configs as $name => $value) {
                if (!$this->set($name, $value)) {
                    throw new Exception('Failed to set configuration: ' . $name);
                }
            }

            $this->commit();
            return true;

        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Initialize default configuration values
     *
     * @return bool Success status
     * @throws Exception
     */
    public function initializeDefaults(): bool
    {
        $defaults = [
            'items_per_page' => 12,
            'enable_ordering' => true,
            'admin_email' => '',
            'from_name' => 'CIG Catalog System',
            'from_email' => '',
            'smtp_enabled' => false,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls',
            'max_file_size' => 10485760, // 10MB
            'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'allowed_file_types' => ['pdf', 'zip', 'doc', 'docx', 'xls', 'xlsx'],
            'enable_download_tracking' => true,
            'enable_new_badge' => true,
            'new_badge_days' => 30,
            'enable_pagination' => true,
            'enable_search' => true,
            'cache_enabled' => true,
            'cache_ttl' => 3600
        ];

        if (!$this->beginTransaction()) {
            throw new Exception('Failed to start transaction');
        }

        try {
            foreach ($defaults as $key => $value) {
                // Only set if not already exists
                if ($this->get($key) === null) {
                    if (!$this->set($key, $value)) {
                        throw new Exception('Failed to initialize default configuration: ' . $key);
                    }
                }
            }

            $this->commit();
            return true;

        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Validate configuration value
     *
     * @param string $key Configuration key
     * @param mixed $value Configuration value
     * @return bool Validation result
     */
    public function validateValue(string $key, $value): bool
    {
        switch ($key) {
            case 'items_per_page':
            case 'max_file_size':
            case 'new_badge_days':
            case 'smtp_port':
            case 'cache_ttl':
                return is_numeric($value) && $value > 0;

            case 'enable_ordering':
            case 'smtp_enabled':
            case 'enable_download_tracking':
            case 'enable_new_badge':
            case 'enable_pagination':
            case 'enable_search':
            case 'cache_enabled':
                return is_bool($value) || in_array($value, ['0', '1', 0, 1, true, false]);

            case 'admin_email':
            case 'from_email':
                return empty($value) || filter_var($value, FILTER_VALIDATE_EMAIL);

            case 'smtp_encryption':
                return in_array($value, ['', 'ssl', 'tls']);

            case 'allowed_image_types':
            case 'allowed_file_types':
                return is_array($value) || is_string($value);

            default:
                return true;
        }
    }

    /**
     * Enable or disable caching
     *
     * @param bool $enabled Cache enabled flag
     */
    public function setCacheEnabled(bool $enabled): void
    {
        $this->cacheEnabled = $enabled;
        if (!$enabled) {
            $this->clearConfigCache();
        }
    }

    /**
     * Set cache TTL
     *
     * @param int $ttl Cache TTL in seconds
     */
    public function setCacheTtl(int $ttl): void
    {
        $this->cacheTtl = max(60, $ttl); // Minimum 1 minute
    }
}
