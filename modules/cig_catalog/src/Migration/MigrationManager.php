<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Migration;

use Db;
use DbQuery;
use CigCatalog\Entity\CatalogConfig;

/**
 * Migration manager for database schema updates
 */
class MigrationManager
{
    /** @var string Current module version */
    private $currentVersion;

    /** @var string Target version */
    private $targetVersion;

    /** @var array Available migrations */
    private $migrations = [];

    /**
     * Constructor
     *
     * @param string $currentVersion
     * @param string $targetVersion
     */
    public function __construct($currentVersion, $targetVersion)
    {
        $this->currentVersion = $currentVersion;
        $this->targetVersion = $targetVersion;
        $this->loadMigrations();
    }

    /**
     * Load available migrations
     */
    private function loadMigrations()
    {
        $this->migrations = [
            '1.0.0' => [
                'description' => 'Initial database structure',
                'up' => [$this, 'migrate_1_0_0_up'],
                'down' => [$this, 'migrate_1_0_0_down']
            ],
            '1.0.1' => [
                'description' => 'Add download tracking',
                'up' => [$this, 'migrate_1_0_1_up'],
                'down' => [$this, 'migrate_1_0_1_down']
            ],
            '1.1.0' => [
                'description' => 'Enhanced order management',
                'up' => [$this, 'migrate_1_1_0_up'],
                'down' => [$this, 'migrate_1_1_0_down']
            ]
        ];
    }

    /**
     * Run migrations
     *
     * @return bool
     */
    public function runMigrations()
    {
        $versionsToMigrate = $this->getVersionsToMigrate();

        foreach ($versionsToMigrate as $version) {
            if (!$this->runMigration($version, 'up')) {
                return false;
            }
        }

        // Update module version
        CatalogConfig::set('module_version', $this->targetVersion);

        return true;
    }

    /**
     * Rollback migrations
     *
     * @param string $targetVersion
     * @return bool
     */
    public function rollbackMigrations($targetVersion)
    {
        $versionsToRollback = $this->getVersionsToRollback($targetVersion);

        foreach ($versionsToRollback as $version) {
            if (!$this->runMigration($version, 'down')) {
                return false;
            }
        }

        // Update module version
        CatalogConfig::set('module_version', $targetVersion);

        return true;
    }

    /**
     * Get versions that need migration
     *
     * @return array
     */
    private function getVersionsToMigrate()
    {
        $versions = [];

        foreach ($this->migrations as $version => $migration) {
            if (version_compare($version, $this->currentVersion, '>') && 
                version_compare($version, $this->targetVersion, '<=')) {
                $versions[] = $version;
            }
        }

        // Sort versions ascending
        usort($versions, 'version_compare');

        return $versions;
    }

    /**
     * Get versions that need rollback
     *
     * @param string $targetVersion
     * @return array
     */
    private function getVersionsToRollback($targetVersion)
    {
        $versions = [];

        foreach ($this->migrations as $version => $migration) {
            if (version_compare($version, $targetVersion, '>') && 
                version_compare($version, $this->currentVersion, '<=')) {
                $versions[] = $version;
            }
        }

        // Sort versions descending for rollback
        usort($versions, function($a, $b) {
            return version_compare($b, $a);
        });

        return $versions;
    }

    /**
     * Run single migration
     *
     * @param string $version
     * @param string $direction
     * @return bool
     */
    private function runMigration($version, $direction)
    {
        if (!isset($this->migrations[$version])) {
            return false;
        }

        $migration = $this->migrations[$version];

        if (!isset($migration[$direction]) || !is_callable($migration[$direction])) {
            return false;
        }

        try {
            return call_user_func($migration[$direction]);
        } catch (Exception $e) {
            error_log("Migration {$version} {$direction} failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if table exists
     *
     * @param string $tableName
     * @return bool
     */
    private function tableExists($tableName)
    {
        $sql = "SHOW TABLES LIKE '" . _DB_PREFIX_ . pSQL($tableName) . "'";
        return (bool) Db::getInstance()->getValue($sql);
    }

    /**
     * Check if column exists
     *
     * @param string $tableName
     * @param string $columnName
     * @return bool
     */
    private function columnExists($tableName, $columnName)
    {
        $sql = "SHOW COLUMNS FROM `" . _DB_PREFIX_ . pSQL($tableName) . "` LIKE '" . pSQL($columnName) . "'";
        return (bool) Db::getInstance()->getValue($sql);
    }

    /**
     * Migration 1.0.0 - Initial structure
     */
    private function migrate_1_0_0_up()
    {
        // This migration is handled by install.sql
        return true;
    }

    private function migrate_1_0_0_down()
    {
        // This migration is handled by uninstall.sql
        return true;
    }

    /**
     * Migration 1.0.1 - Add download tracking
     */
    private function migrate_1_0_1_up()
    {
        $sql = [];

        // Add download_count column if it doesn't exist
        if (!$this->columnExists('cig_catalog', 'download_count')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog` 
                      ADD COLUMN `download_count` int(10) unsigned NOT NULL DEFAULT '0' 
                      AFTER `catalog_url`";
        }

        // Add index for download_count
        $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog` 
                  ADD INDEX `idx_download_count` (`download_count`)";

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    private function migrate_1_0_1_down()
    {
        $sql = [];

        // Remove download_count index
        $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog` DROP INDEX `idx_download_count`";

        // Remove download_count column
        if ($this->columnExists('cig_catalog', 'download_count')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog` DROP COLUMN `download_count`";
        }

        foreach ($sql as $query) {
            Db::getInstance()->execute($query); // Don't fail on rollback errors
        }

        return true;
    }

    /**
     * Migration 1.1.0 - Enhanced order management
     */
    private function migrate_1_1_0_up()
    {
        $sql = [];

        // Add new columns to order table
        if (!$this->columnExists('cig_catalog_order', 'ip_address')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` 
                      ADD COLUMN `ip_address` varchar(45) DEFAULT NULL 
                      AFTER `note`";
        }

        if (!$this->columnExists('cig_catalog_order', 'user_agent')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` 
                      ADD COLUMN `user_agent` varchar(500) DEFAULT NULL 
                      AFTER `ip_address`";
        }

        if (!$this->columnExists('cig_catalog_order', 'admin_note')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` 
                      ADD COLUMN `admin_note` text 
                      AFTER `status`";
        }

        // Add indexes
        $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` 
                  ADD INDEX `idx_ip_address` (`ip_address`)";

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    private function migrate_1_1_0_down()
    {
        $sql = [];

        // Remove indexes
        $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` DROP INDEX `idx_ip_address`";

        // Remove columns
        if ($this->columnExists('cig_catalog_order', 'admin_note')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` DROP COLUMN `admin_note`";
        }

        if ($this->columnExists('cig_catalog_order', 'user_agent')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` DROP COLUMN `user_agent`";
        }

        if ($this->columnExists('cig_catalog_order', 'ip_address')) {
            $sql[] = "ALTER TABLE `" . _DB_PREFIX_ . "cig_catalog_order` DROP COLUMN `ip_address`";
        }

        foreach ($sql as $query) {
            Db::getInstance()->execute($query); // Don't fail on rollback errors
        }

        return true;
    }

    /**
     * Get migration status
     *
     * @return array
     */
    public function getMigrationStatus()
    {
        $status = [];

        foreach ($this->migrations as $version => $migration) {
            $status[] = [
                'version' => $version,
                'description' => $migration['description'],
                'applied' => version_compare($version, $this->currentVersion, '<='),
                'pending' => version_compare($version, $this->currentVersion, '>') && 
                           version_compare($version, $this->targetVersion, '<=')
            ];
        }

        return $status;
    }
}
