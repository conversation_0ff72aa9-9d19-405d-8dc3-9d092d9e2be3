<?php
/**
 * Catalog Form Type
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;
use CigCatalog\Entity\Catalog;

class CatalogType extends AbstractType
{
    private $context;

    public function __construct($context)
    {
        $this->context = $context;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $languages = $this->context->getLanguages() ?? [];

        $builder
            ->add('active', CheckboxType::class, [
                'label' => 'Aktivní',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ]
            ])
            ->add('is_new', CheckboxType::class, [
                'label' => 'Označit jako nový',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ]
            ])
            ->add('position', IntegerType::class, [
                'label' => 'Pozice',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'min' => 0
                ],
                'constraints' => [
                    new Assert\PositiveOrZero()
                ]
            ]);

        // Multijazyčné pole pro každý jazyk
        foreach ($languages as $language) {
            $langId = $language['id_lang'];
            $langCode = $language['iso_code'];
            
            $builder
                ->add("name_{$langId}", TextType::class, [
                    'label' => "Název ({$langCode})",
                    'required' => $langId == 1, // Povinné pro výchozí jazyk
                    'attr' => [
                        'class' => 'form-control',
                        'maxlength' => 255
                    ],
                    'constraints' => $langId == 1 ? [
                        new Assert\NotBlank(['message' => 'Název je povinný.']),
                        new Assert\Length(['max' => 255])
                    ] : [
                        new Assert\Length(['max' => 255])
                    ]
                ])
                ->add("description_{$langId}", TextareaType::class, [
                    'label' => "Popis ({$langCode})",
                    'required' => false,
                    'attr' => [
                        'class' => 'form-control',
                        'rows' => 4
                    ]
                ]);
        }

        $builder
            ->add('image_file', FileType::class, [
                'label' => 'Obrázek katalogu',
                'required' => false,
                'mapped' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new Assert\File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Nahrajte platný obrázek (JPEG, PNG, GIF, WebP).'
                    ])
                ]
            ])
            ->add('catalog_file', FileType::class, [
                'label' => 'Soubor katalogu (PDF/ZIP)',
                'required' => false,
                'mapped' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => '.pdf,.zip'
                ],
                'constraints' => [
                    new Assert\File([
                        'maxSize' => '50M',
                        'mimeTypes' => [
                            'application/pdf',
                            'application/zip',
                            'application/x-zip-compressed'
                        ],
                        'mimeTypesMessage' => 'Nahrajte platný soubor (PDF nebo ZIP).'
                    ])
                ]
            ])
            ->add('save', SubmitType::class, [
                'label' => 'Uložit',
                'attr' => [
                    'class' => 'btn btn-primary'
                ]
            ])
            ->add('save_and_continue', SubmitType::class, [
                'label' => 'Uložit a pokračovat v editaci',
                'attr' => [
                    'class' => 'btn btn-secondary'
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Catalog::class,
            'csrf_protection' => true,
            'csrf_field_name' => '_token',
            'csrf_token_id' => 'catalog_form'
        ]);
    }

    public function getBlockPrefix(): string
    {
        return 'catalog';
    }
}
