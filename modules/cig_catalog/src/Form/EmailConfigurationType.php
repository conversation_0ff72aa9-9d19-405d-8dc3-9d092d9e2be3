<?php

declare(strict_types=1);

namespace CigCatalog\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

/**
 * Email Configuration Form Type
 */
class EmailConfigurationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            // Základní email nastavení
            ->add('email_recipients', TextType::class, [
                'label' => 'Email příjemci',
                'help' => 'Zadejte email adresy oddělené čárkami pro příjem notifikací o nových objednávkách',
                'required' => true,
                'attr' => [
                    'placeholder' => '<EMAIL>, <EMAIL>',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Email příjemci jsou povinní']),
                    new Callback([
                        'callback' => [$this, 'validateEmails']
                    ])
                ]
            ])
            
            // Email předměty
            ->add('email_subject_order', TextType::class, [
                'label' => 'Předmět - nová objednávka',
                'help' => 'Předmět emailu pro notifikaci o nové objednávce katalogu',
                'data' => 'Nová objednávka katalogu',
                'required' => true,
                'attr' => ['class' => 'form-control'],
                'constraints' => [
                    new NotBlank(['message' => 'Předmět emailu je povinný'])
                ]
            ])
            
            ->add('email_subject_confirmation', TextType::class, [
                'label' => 'Předmět - potvrzení zákazníkovi',
                'help' => 'Předmět emailu pro potvrzení objednávky zákazníkovi',
                'data' => 'Potvrzení objednávky katalogu',
                'required' => true,
                'attr' => ['class' => 'form-control'],
                'constraints' => [
                    new NotBlank(['message' => 'Předmět emailu je povinný'])
                ]
            ])
            
            // Email šablony
            ->add('email_template_order', TextareaType::class, [
                'label' => 'Šablona - notifikace admina',
                'help' => 'Dostupné proměnné: {{catalog_title}}, {{company_name}}, {{first_name}}, {{last_name}}, {{email}}, {{phone}}, {{address}}, {{note}}, {{date_add}}, {{order_id}}',
                'attr' => [
                    'rows' => 12,
                    'class' => 'form-control',
                    'placeholder' => 'Dobrý den,

byla vytvořena nová objednávka katalogu.

Katalog: {{catalog_title}}
Objednávka č.: {{order_id}}
Datum: {{date_add}}

Zákazník:
{{first_name}} {{last_name}}
{{company_name}}
{{email}}
{{phone}}

Adresa:
{{address}}

Poznámka:
{{note}}

S pozdravem,
Systém katalogů'
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Šablona emailu je povinná'])
                ]
            ])
            
            ->add('email_template_confirmation', TextareaType::class, [
                'label' => 'Šablona - potvrzení zákazníkovi',
                'help' => 'Dostupné proměnné: {{catalog_title}}, {{company_name}}, {{first_name}}, {{last_name}}, {{order_id}}, {{date_add}}',
                'attr' => [
                    'rows' => 10,
                    'class' => 'form-control',
                    'placeholder' => 'Dobrý den {{first_name}} {{last_name}},

děkujeme za Vaši objednávku katalogu "{{catalog_title}}".

Číslo objednávky: {{order_id}}
Datum objednávky: {{date_add}}

Katalog Vám bude zaslán na uvedenou adresu v nejbližší době.

S pozdravem,
Váš tým'
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Šablona emailu je povinná'])
                ]
            ])
            
            // SMTP nastavení
            ->add('smtp_enabled', CheckboxType::class, [
                'label' => 'Použít SMTP server',
                'help' => 'Pokud není zaškrtnuto, použije se výchozí PHP mail() funkce',
                'required' => false,
                'attr' => ['class' => 'form-check-input']
            ])
            
            ->add('smtp_host', TextType::class, [
                'label' => 'SMTP server',
                'help' => 'Adresa SMTP serveru (např. smtp.gmail.com)',
                'required' => false,
                'attr' => [
                    'placeholder' => 'smtp.example.com',
                    'class' => 'form-control'
                ]
            ])
            
            ->add('smtp_port', IntegerType::class, [
                'label' => 'SMTP port',
                'help' => 'Port SMTP serveru (obvykle 587 pro TLS, 465 pro SSL, 25 pro nešifrované)',
                'data' => 587,
                'required' => false,
                'attr' => ['class' => 'form-control']
            ])
            
            ->add('smtp_encryption', ChoiceType::class, [
                'label' => 'Šifrování',
                'choices' => [
                    'Žádné' => '',
                    'TLS' => 'tls',
                    'SSL' => 'ssl'
                ],
                'data' => 'tls',
                'required' => false,
                'attr' => ['class' => 'form-control']
            ])
            
            ->add('smtp_username', TextType::class, [
                'label' => 'SMTP uživatelské jméno',
                'help' => 'Uživatelské jméno pro přihlášení k SMTP serveru',
                'required' => false,
                'attr' => [
                    'placeholder' => '<EMAIL>',
                    'class' => 'form-control'
                ]
            ])
            
            ->add('smtp_password', PasswordType::class, [
                'label' => 'SMTP heslo',
                'help' => 'Heslo pro přihlášení k SMTP serveru',
                'required' => false,
                'always_empty' => false,
                'attr' => ['class' => 'form-control']
            ])
            
            // Pokročilé nastavení
            ->add('send_confirmation_to_customer', CheckboxType::class, [
                'label' => 'Poslat potvrzení zákazníkovi',
                'help' => 'Automaticky odeslat potvrzovací email zákazníkovi',
                'data' => true,
                'required' => false,
                'attr' => ['class' => 'form-check-input']
            ])
            
            ->add('email_from_name', TextType::class, [
                'label' => 'Jméno odesílatele',
                'help' => 'Jméno, které se zobrazí jako odesílatel emailu',
                'data' => 'Katalogy',
                'required' => false,
                'attr' => ['class' => 'form-control']
            ])
            
            ->add('email_from_address', TextType::class, [
                'label' => 'Email odesílatele',
                'help' => 'Email adresa odesílatele (pokud není zadána, použije se první z příjemců)',
                'required' => false,
                'attr' => [
                    'placeholder' => '<EMAIL>',
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new Email(['message' => 'Zadejte platnou email adresu'])
                ]
            ])
            
            // Tlačítka
            ->add('test_email', SubmitType::class, [
                'label' => 'Odeslat testovací email',
                'attr' => [
                    'class' => 'btn btn-outline-info',
                    'formnovalidate' => true
                ]
            ])
            
            ->add('submit', SubmitType::class, [
                'label' => 'Uložit konfiguraci',
                'attr' => ['class' => 'btn btn-primary']
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => null,
            'csrf_protection' => true,
            'csrf_field_name' => '_token',
            'csrf_token_id' => 'email_config'
        ]);
    }

    /**
     * Validace email adres oddělených čárkami
     */
    public function validateEmails($value, ExecutionContextInterface $context): void
    {
        if (empty($value)) {
            return;
        }

        $emails = array_map('trim', explode(',', $value));
        
        foreach ($emails as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $context->buildViolation('Email "{{ email }}" není platný')
                    ->setParameter('{{ email }}', $email)
                    ->addViolation();
            }
        }
    }
}
