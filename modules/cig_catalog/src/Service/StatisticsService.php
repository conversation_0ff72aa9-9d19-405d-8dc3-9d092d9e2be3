<?php
/**
 * Statistics Service
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Service;

use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Repository\CatalogOrderRepository;
use Doctrine\DBAL\Connection;

class StatisticsService
{
    private CatalogRepository $catalogRepository;
    private CatalogOrderRepository $orderRepository;
    private Connection $connection;

    public function __construct(
        CatalogRepository $catalogRepository,
        CatalogOrderRepository $orderRepository,
        Connection $connection
    ) {
        $this->catalogRepository = $catalogRepository;
        $this->orderRepository = $orderRepository;
        $this->connection = $connection;
    }

    /**
     * Get overall statistics
     */
    public function getOverallStatistics(): array
    {
        return [
            'total_catalogs' => $this->catalogRepository->count(),
            'active_catalogs' => $this->catalogRepository->countActive(),
            'total_downloads' => $this->getTotalDownloads(),
            'total_orders' => $this->orderRepository->count(),
            'pending_orders' => $this->orderRepository->countByStatus('pending'),
            'this_month_downloads' => $this->getDownloadsThisMonth(),
            'this_month_orders' => $this->getOrdersThisMonth()
        ];
    }

    /**
     * Get download statistics
     */
    public function getDownloadStatistics(int $days = 30): array
    {
        $sql = "
            SELECT 
                DATE(c.date_add) as date,
                SUM(c.download_count) as downloads
            FROM " . _DB_PREFIX_ . "cig_catalog c
            WHERE c.date_add >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(c.date_add)
            ORDER BY date ASC
        ";

        $result = $this->connection->fetchAllAssociative($sql, [$days]);
        
        return $this->fillMissingDates($result, $days);
    }

    /**
     * Get order statistics
     */
    public function getOrderStatistics(int $days = 30): array
    {
        $sql = "
            SELECT 
                DATE(co.date_add) as date,
                COUNT(*) as orders,
                co.status
            FROM " . _DB_PREFIX_ . "cig_catalog_order co
            WHERE co.date_add >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(co.date_add), co.status
            ORDER BY date ASC
        ";

        $result = $this->connection->fetchAllAssociative($sql, [$days]);
        
        return $this->groupOrdersByDate($result, $days);
    }

    /**
     * Get popular catalogs
     */
    public function getPopularCatalogs(int $limit = 10): array
    {
        $sql = "
            SELECT 
                c.id_catalog,
                cl.name,
                c.download_count,
                COUNT(co.id_catalog_order) as order_count
            FROM " . _DB_PREFIX_ . "cig_catalog c
            LEFT JOIN " . _DB_PREFIX_ . "cig_catalog_lang cl ON c.id_catalog = cl.id_catalog AND cl.id_lang = 1
            LEFT JOIN " . _DB_PREFIX_ . "cig_catalog_order co ON c.id_catalog = co.id_catalog
            WHERE c.active = 1
            GROUP BY c.id_catalog
            ORDER BY (c.download_count + COUNT(co.id_catalog_order)) DESC
            LIMIT ?
        ";

        return $this->connection->fetchAllAssociative($sql, [$limit]);
    }

    /**
     * Get chart data
     */
    public function getChartData(): array
    {
        return [
            'downloads' => $this->getDownloadStatistics(30),
            'orders' => $this->getOrderStatistics(30),
            'popular_catalogs' => $this->getPopularCatalogs(5)
        ];
    }

    /**
     * Get total downloads
     */
    private function getTotalDownloads(): int
    {
        $sql = "SELECT SUM(download_count) FROM " . _DB_PREFIX_ . "cig_catalog";
        return (int) $this->connection->fetchOne($sql);
    }

    /**
     * Get downloads this month
     */
    private function getDownloadsThisMonth(): int
    {
        $sql = "
            SELECT SUM(download_count) 
            FROM " . _DB_PREFIX_ . "cig_catalog 
            WHERE MONTH(date_add) = MONTH(NOW()) AND YEAR(date_add) = YEAR(NOW())
        ";
        return (int) $this->connection->fetchOne($sql);
    }

    /**
     * Get orders this month
     */
    private function getOrdersThisMonth(): int
    {
        $sql = "
            SELECT COUNT(*) 
            FROM " . _DB_PREFIX_ . "cig_catalog_order 
            WHERE MONTH(date_add) = MONTH(NOW()) AND YEAR(date_add) = YEAR(NOW())
        ";
        return (int) $this->connection->fetchOne($sql);
    }

    /**
     * Fill missing dates in statistics
     */
    private function fillMissingDates(array $data, int $days): array
    {
        $result = [];
        $dataByDate = [];
        
        foreach ($data as $row) {
            $dataByDate[$row['date']] = $row;
        }

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $result[] = [
                'date' => $date,
                'downloads' => $dataByDate[$date]['downloads'] ?? 0
            ];
        }

        return $result;
    }

    /**
     * Group orders by date and status
     */
    private function groupOrdersByDate(array $data, int $days): array
    {
        $result = [];
        $dataByDate = [];
        
        foreach ($data as $row) {
            if (!isset($dataByDate[$row['date']])) {
                $dataByDate[$row['date']] = [];
            }
            $dataByDate[$row['date']][$row['status']] = $row['orders'];
        }

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $result[] = [
                'date' => $date,
                'pending' => $dataByDate[$date]['pending'] ?? 0,
                'processing' => $dataByDate[$date]['processing'] ?? 0,
                'shipped' => $dataByDate[$date]['shipped'] ?? 0,
                'delivered' => $dataByDate[$date]['delivered'] ?? 0,
                'cancelled' => $dataByDate[$date]['cancelled'] ?? 0
            ];
        }

        return $result;
    }
}
