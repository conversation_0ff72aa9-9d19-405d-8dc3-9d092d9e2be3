<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;
use Context;
use Mail;
use Configuration;
use Tools;

/**
 * EmailService - správa email komunikace
 */
class EmailService
{
    private EmailTemplateManager $templateManager;
    private string $moduleDir;

    public function __construct(string $moduleDir)
    {
        $this->moduleDir = $moduleDir;
        $this->templateManager = new EmailTemplateManager($moduleDir);
    }

    /**
     * Odešle notifikaci o nové objednávce administrátorovi
     */
    public function sendOrderNotification(array $orderData, array $catalogData): bool
    {
        $adminEmail = Configuration::get('PS_SHOP_EMAIL');
        if (empty($adminEmail)) {
            throw new DomainException('Admin email není nastaven');
        }

        $subject = 'Nová objednávka katalogu: ' . $catalogData['title'];
        
        $templateVars = [
            'order' => $orderData,
            'catalog' => $catalogData,
            'shop_name' => Configuration::get('PS_SHOP_NAME'),
            'date' => date('d.m.Y H:i')
        ];

        $htmlContent = $this->templateManager->renderTemplate('order_notification', $templateVars);
        $textContent = $this->templateManager->renderTemplate('order_notification_text', $templateVars);

        return $this->sendEmail(
            $adminEmail,
            Configuration::get('PS_SHOP_NAME'),
            $subject,
            $htmlContent,
            $textContent,
            $orderData['email'], // Reply-to na zákazníka
            $orderData['first_name'] . ' ' . $orderData['last_name']
        );
    }

    /**
     * Odešle potvrzení objednávky zákazníkovi
     */
    public function sendOrderConfirmation(array $orderData, array $catalogData): bool
    {
        $subject = 'Potvrzení objednávky katalogu: ' . $catalogData['title'];
        
        $templateVars = [
            'order' => $orderData,
            'catalog' => $catalogData,
            'shop_name' => Configuration::get('PS_SHOP_NAME'),
            'shop_email' => Configuration::get('PS_SHOP_EMAIL'),
            'date' => date('d.m.Y H:i')
        ];

        $htmlContent = $this->templateManager->renderTemplate('order_confirmation', $templateVars);
        $textContent = $this->templateManager->renderTemplate('order_confirmation_text', $templateVars);

        return $this->sendEmail(
            $orderData['email'],
            $orderData['first_name'] . ' ' . $orderData['last_name'],
            $subject,
            $htmlContent,
            $textContent
        );
    }

    /**
     * Odešle admin upozornění
     */
    public function sendAdminNotification(string $type, array $data): bool
    {
        $adminEmail = Configuration::get('PS_SHOP_EMAIL');
        if (empty($adminEmail)) {
            return false;
        }

        $subjects = [
            'error' => 'Chyba v modulu Katalogy',
            'warning' => 'Upozornění z modulu Katalogy',
            'info' => 'Informace z modulu Katalogy'
        ];

        $subject = $subjects[$type] ?? 'Notifikace z modulu Katalogy';
        
        $templateVars = [
            'type' => $type,
            'data' => $data,
            'shop_name' => Configuration::get('PS_SHOP_NAME'),
            'date' => date('d.m.Y H:i'),
            'server_info' => [
                'php_version' => PHP_VERSION,
                'prestashop_version' => _PS_VERSION_,
                'module_version' => '1.0.0'
            ]
        ];

        $htmlContent = $this->templateManager->renderTemplate('admin_alert', $templateVars);
        $textContent = $this->templateManager->renderTemplate('admin_alert_text', $templateVars);

        return $this->sendEmail(
            $adminEmail,
            Configuration::get('PS_SHOP_NAME'),
            $subject,
            $htmlContent,
            $textContent
        );
    }

    /**
     * Odešle hromadný email
     */
    public function sendBulkEmail(array $recipients, string $subject, string $templateName, array $templateVars): array
    {
        $results = [];
        $htmlContent = $this->templateManager->renderTemplate($templateName, $templateVars);
        $textContent = $this->templateManager->renderTemplate($templateName . '_text', $templateVars);

        foreach ($recipients as $recipient) {
            $email = $recipient['email'];
            $name = $recipient['name'] ?? '';
            
            $personalizedVars = array_merge($templateVars, [
                'recipient_name' => $name,
                'recipient_email' => $email
            ]);
            
            $personalizedHtml = $this->templateManager->renderTemplate($templateName, $personalizedVars);
            $personalizedText = $this->templateManager->renderTemplate($templateName . '_text', $personalizedVars);
            
            $results[$email] = $this->sendEmail(
                $email,
                $name,
                $subject,
                $personalizedHtml,
                $personalizedText
            );
            
            // Malá pauza mezi emaily
            usleep(100000); // 0.1 sekundy
        }

        return $results;
    }

    /**
     * Základní metoda pro odeslání emailu
     */
    private function sendEmail(
        string $toEmail, 
        string $toName, 
        string $subject, 
        string $htmlContent, 
        string $textContent = '',
        string $replyToEmail = '',
        string $replyToName = ''
    ): bool {
        try {
            $context = Context::getContext();
            $idLang = $context->language->id;
            $idShop = $context->shop->id;

            // Fallback na textový obsah pokud není HTML
            if (empty($textContent)) {
                $textContent = strip_tags($htmlContent);
            }

            // Reply-to nastavení
            $replyTo = null;
            if (!empty($replyToEmail)) {
                $replyTo = $replyToEmail;
            }

            // Použití PrestaShop Mail třídy
            return Mail::send(
                $idLang,
                'cig_catalog_custom', // Template name
                $subject,
                [
                    '{content}' => $htmlContent,
                    '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
                    '{shop_url}' => Tools::getShopDomainSsl(true)
                ],
                $toEmail,
                $toName,
                null, // From email (použije se výchozí)
                null, // From name (použije se výchozí)
                null, // File attachment
                null, // Mode
                $this->moduleDir . '/mails/', // Template path
                false, // Die
                $idShop,
                $replyTo
            );
        } catch (\Exception $e) {
            error_log('EmailService error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validuje email adresu
     */
    public function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Získá seznam dostupných email templates
     */
    public function getAvailableTemplates(): array
    {
        return $this->templateManager->getAvailableTemplates();
    }

    /**
     * Testuje email konfiguraci
     */
    public function testEmailConfiguration(): array
    {
        $results = [
            'smtp_configured' => false,
            'test_email_sent' => false,
            'errors' => []
        ];

        try {
            // Kontrola SMTP konfigurace
            $mailMethod = Configuration::get('PS_MAIL_METHOD');
            $smtpServer = Configuration::get('PS_MAIL_SERVER');
            
            $results['smtp_configured'] = !empty($smtpServer) && $mailMethod == 2;

            // Test odeslání emailu
            $adminEmail = Configuration::get('PS_SHOP_EMAIL');
            if (!empty($adminEmail)) {
                $testSubject = 'Test email z modulu Katalogy';
                $testContent = 'Toto je testovací email pro ověření funkčnosti email systému.';
                
                $results['test_email_sent'] = $this->sendEmail(
                    $adminEmail,
                    'Test',
                    $testSubject,
                    $testContent
                );
            } else {
                $results['errors'][] = 'Admin email není nastaven';
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Získá statistiky odeslaných emailů
     */
    public function getEmailStatistics(): array
    {
        // Toto by v reálné aplikaci četlo z databáze
        return [
            'total_sent' => 0,
            'order_notifications' => 0,
            'order_confirmations' => 0,
            'admin_alerts' => 0,
            'last_sent' => null,
            'failed_attempts' => 0
        ];
    }

    /**
     * Vytvoří email queue pro hromadné odeslání
     */
    public function queueBulkEmails(array $emails): bool
    {
        // V reálné implementaci by se emaily uložily do queue tabulky
        // a zpracovávaly by se pomocí cron jobu
        
        foreach ($emails as $emailData) {
            // Simulace uložení do queue
            $queueData = [
                'to_email' => $emailData['to_email'],
                'to_name' => $emailData['to_name'],
                'subject' => $emailData['subject'],
                'template' => $emailData['template'],
                'template_vars' => json_encode($emailData['template_vars']),
                'priority' => $emailData['priority'] ?? 'normal',
                'scheduled_at' => $emailData['scheduled_at'] ?? date('Y-m-d H:i:s'),
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            // Zde by se data uložila do databáze
        }

        return true;
    }

    /**
     * Zpracuje email queue
     */
    public function processEmailQueue(int $limit = 50): array
    {
        $processed = [];
        
        // V reálné implementaci by se načetly pending emaily z databáze
        // a postupně by se odeslaly
        
        return [
            'processed' => count($processed),
            'failed' => 0,
            'remaining' => 0
        ];
    }
}
