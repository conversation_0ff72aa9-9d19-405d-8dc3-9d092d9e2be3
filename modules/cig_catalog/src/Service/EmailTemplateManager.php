<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;

/**
 * EmailTemplateManager - správa email templates
 */
class EmailTemplateManager
{
    private string $templatesPath;
    private array $templateCache = [];

    public function __construct(string $moduleDir)
    {
        $this->templatesPath = $moduleDir . '/templates/emails';
        $this->ensureTemplatesDirectoryExists();
    }

    /**
     * Renderuje email template s proměnnými
     */
    public function renderTemplate(string $templateName, array $variables): string
    {
        $templatePath = $this->getTemplatePath($templateName);
        
        if (!file_exists($templatePath)) {
            // Pokud template neexistuje, vytvoříme základní
            $this->createDefaultTemplate($templateName);
        }
        
        // Cache kontrola
        $cacheKey = $templateName . '_' . md5(serialize($variables));
        if (isset($this->templateCache[$cacheKey])) {
            return $this->templateCache[$cacheKey];
        }
        
        // Renderování template
        $content = $this->renderTemplateFile($templatePath, $variables);
        
        // Uložení do cache
        $this->templateCache[$cacheKey] = $content;
        
        return $content;
    }

    /**
     * Získá seznam dostupných templates
     */
    public function getAvailableTemplates(): array
    {
        return [
            'order_notification' => 'Notifikace o nové objednávce (admin)',
            'order_notification_text' => 'Notifikace o nové objednávce - textová verze',
            'order_confirmation' => 'Potvrzení objednávky (zákazník)',
            'order_confirmation_text' => 'Potvrzení objednávky - textová verze',
            'admin_alert' => 'Admin upozornění',
            'admin_alert_text' => 'Admin upozornění - textová verze',
            'newsletter' => 'Newsletter template',
            'newsletter_text' => 'Newsletter - textová verze'
        ];
    }

    /**
     * Vytvoří nový template
     */
    public function createTemplate(string $templateName, string $content): bool
    {
        $templatePath = $this->getTemplatePath($templateName);
        
        if (file_exists($templatePath)) {
            throw new DomainException("Template {$templateName} již existuje");
        }
        
        return file_put_contents($templatePath, $content) !== false;
    }

    /**
     * Aktualizuje existující template
     */
    public function updateTemplate(string $templateName, string $content): bool
    {
        $templatePath = $this->getTemplatePath($templateName);
        
        if (!file_exists($templatePath)) {
            throw new DomainException("Template {$templateName} neexistuje");
        }
        
        // Backup starého template
        $backupPath = $templatePath . '.backup.' . time();
        copy($templatePath, $backupPath);
        
        $result = file_put_contents($templatePath, $content) !== false;
        
        // Vyčištění cache
        $this->clearTemplateCache($templateName);
        
        return $result;
    }

    /**
     * Smaže template
     */
    public function deleteTemplate(string $templateName): bool
    {
        $templatePath = $this->getTemplatePath($templateName);
        
        if (!file_exists($templatePath)) {
            return true;
        }
        
        $result = unlink($templatePath);
        
        // Vyčištění cache
        $this->clearTemplateCache($templateName);
        
        return $result;
    }

    /**
     * Získá obsah template
     */
    public function getTemplateContent(string $templateName): string
    {
        $templatePath = $this->getTemplatePath($templateName);
        
        if (!file_exists($templatePath)) {
            throw new DomainException("Template {$templateName} neexistuje");
        }
        
        return file_get_contents($templatePath);
    }

    /**
     * Validuje template syntax
     */
    public function validateTemplate(string $content): array
    {
        $errors = [];
        
        // Kontrola PHP syntaxe
        $tempFile = tempnam(sys_get_temp_dir(), 'template_validation');
        file_put_contents($tempFile, '<?php ' . $content);
        
        $output = [];
        $returnCode = 0;
        exec("php -l {$tempFile} 2>&1", $output, $returnCode);
        
        if ($returnCode !== 0) {
            $errors[] = 'PHP syntax error: ' . implode(' ', $output);
        }
        
        unlink($tempFile);
        
        // Kontrola povinných proměnných
        $requiredVars = ['shop_name', 'date'];
        foreach ($requiredVars as $var) {
            if (strpos($content, '$' . $var) === false && strpos($content, '<?= $' . $var) === false) {
                $errors[] = "Chybí povinná proměnná: \${$var}";
            }
        }
        
        return $errors;
    }

    /**
     * Exportuje všechny templates
     */
    public function exportTemplates(): array
    {
        $templates = [];
        $availableTemplates = $this->getAvailableTemplates();
        
        foreach (array_keys($availableTemplates) as $templateName) {
            $templatePath = $this->getTemplatePath($templateName);
            if (file_exists($templatePath)) {
                $templates[$templateName] = file_get_contents($templatePath);
            }
        }
        
        return $templates;
    }

    /**
     * Importuje templates ze zálohy
     */
    public function importTemplates(array $templates): bool
    {
        foreach ($templates as $templateName => $content) {
            $templatePath = $this->getTemplatePath($templateName);
            
            // Backup existujícího template
            if (file_exists($templatePath)) {
                $backupPath = $templatePath . '.backup.' . time();
                copy($templatePath, $backupPath);
            }
            
            file_put_contents($templatePath, $content);
        }
        
        // Vyčištění celé cache
        $this->templateCache = [];
        
        return true;
    }

    /**
     * Renderuje template soubor s proměnnými
     */
    private function renderTemplateFile(string $templatePath, array $variables): string
    {
        // Extrakce proměnných do lokálního scope
        extract($variables);
        
        // Zachycení výstupu
        ob_start();
        
        try {
            include $templatePath;
            $content = ob_get_clean();
        } catch (\Exception $e) {
            ob_end_clean();
            throw new DomainException("Chyba při renderování template: " . $e->getMessage());
        }
        
        return $content;
    }

    /**
     * Získá cestu k template souboru
     */
    private function getTemplatePath(string $templateName): string
    {
        // Sanitizace názvu template
        $templateName = preg_replace('/[^a-zA-Z0-9_-]/', '', $templateName);
        return $this->templatesPath . '/' . $templateName . '.php';
    }

    /**
     * Zajistí existenci adresáře pro templates
     */
    private function ensureTemplatesDirectoryExists(): void
    {
        if (!is_dir($this->templatesPath)) {
            mkdir($this->templatesPath, 0755, true);
        }
        
        // Vytvoření .htaccess pro bezpečnost
        $htaccessPath = $this->templatesPath . '/.htaccess';
        if (!file_exists($htaccessPath)) {
            file_put_contents($htaccessPath, "Order deny,allow\nDeny from all");
        }
        
        // Vytvoření index.php
        $indexPath = $this->templatesPath . '/index.php';
        if (!file_exists($indexPath)) {
            file_put_contents($indexPath, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Forbidden');\n");
        }
    }

    /**
     * Vytvoří výchozí template pokud neexistuje
     */
    private function createDefaultTemplate(string $templateName): void
    {
        $templatePath = $this->getTemplatePath($templateName);
        $content = $this->getDefaultTemplateContent($templateName);
        
        file_put_contents($templatePath, $content);
    }

    /**
     * Získá výchozí obsah pro template
     */
    private function getDefaultTemplateContent(string $templateName): string
    {
        switch ($templateName) {
            case 'order_notification':
                return $this->getOrderNotificationTemplate();
            case 'order_notification_text':
                return $this->getOrderNotificationTextTemplate();
            case 'order_confirmation':
                return $this->getOrderConfirmationTemplate();
            case 'order_confirmation_text':
                return $this->getOrderConfirmationTextTemplate();
            case 'admin_alert':
                return $this->getAdminAlertTemplate();
            case 'admin_alert_text':
                return $this->getAdminAlertTextTemplate();
            default:
                return $this->getGenericTemplate();
        }
    }

    /**
     * Template pro notifikaci o objednávce
     */
    private function getOrderNotificationTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Nová objednávka katalogu</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">Nová objednávka katalogu</h2>
        
        <h3>Informace o katalogu:</h3>
        <p><strong>Název:</strong> <?= htmlspecialchars($catalog[\'title\']) ?></p>
        <p><strong>Popis:</strong> <?= htmlspecialchars($catalog[\'description\']) ?></p>
        
        <h3>Informace o zákazníkovi:</h3>
        <p><strong>Jméno:</strong> <?= htmlspecialchars($order[\'first_name\'] . \' \' . $order[\'last_name\']) ?></p>
        <p><strong>Email:</strong> <?= htmlspecialchars($order[\'email\']) ?></p>
        <p><strong>Telefon:</strong> <?= htmlspecialchars($order[\'phone\']) ?></p>
        <p><strong>Adresa:</strong> <?= nl2br(htmlspecialchars($order[\'address\'])) ?></p>
        
        <?php if (!empty($order[\'company_name\'])): ?>
        <h3>Firemní údaje:</h3>
        <p><strong>Název firmy:</strong> <?= htmlspecialchars($order[\'company_name\']) ?></p>
        <p><strong>IČO:</strong> <?= htmlspecialchars($order[\'company_id\']) ?></p>
        <?php endif; ?>
        
        <?php if (!empty($order[\'note\'])): ?>
        <h3>Poznámka:</h3>
        <p><?= nl2br(htmlspecialchars($order[\'note\'])) ?></p>
        <?php endif; ?>
        
        <hr style="margin: 20px 0;">
        <p style="font-size: 12px; color: #666;">
            Objednávka byla vytvořena <?= $date ?> v obchodě <?= htmlspecialchars($shop_name) ?>
        </p>
    </div>
</body>
</html>';
    }

    /**
     * Textová verze notifikace o objednávce
     */
    private function getOrderNotificationTextTemplate(): string
    {
        return 'Nová objednávka katalogu

Informace o katalogu:
Název: <?= $catalog[\'title\'] ?>
Popis: <?= $catalog[\'description\'] ?>

Informace o zákazníkovi:
Jméno: <?= $order[\'first_name\'] . \' \' . $order[\'last_name\'] ?>
Email: <?= $order[\'email\'] ?>
Telefon: <?= $order[\'phone\'] ?>
Adresa: <?= $order[\'address\'] ?>

<?php if (!empty($order[\'company_name\'])): ?>
Firemní údaje:
Název firmy: <?= $order[\'company_name\'] ?>
IČO: <?= $order[\'company_id\'] ?>
<?php endif; ?>

<?php if (!empty($order[\'note\'])): ?>
Poznámka:
<?= $order[\'note\'] ?>
<?php endif; ?>

---
Objednávka byla vytvořena <?= $date ?> v obchodě <?= $shop_name ?>';
    }

    /**
     * Template pro potvrzení objednávky
     */
    private function getOrderConfirmationTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Potvrzení objednávky katalogu</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #27ae60;">Děkujeme za Vaši objednávku!</h2>
        
        <p>Vážený zákazníku,</p>
        <p>děkujeme za Vaši objednávku katalogu <strong><?= htmlspecialchars($catalog[\'title\']) ?></strong>.</p>
        
        <h3>Shrnutí objednávky:</h3>
        <p><strong>Katalog:</strong> <?= htmlspecialchars($catalog[\'title\']) ?></p>
        <p><strong>Datum objednávky:</strong> <?= $date ?></p>
        
        <h3>Vaše údaje:</h3>
        <p><strong>Jméno:</strong> <?= htmlspecialchars($order[\'first_name\'] . \' \' . $order[\'last_name\']) ?></p>
        <p><strong>Email:</strong> <?= htmlspecialchars($order[\'email\']) ?></p>
        <p><strong>Adresa:</strong> <?= nl2br(htmlspecialchars($order[\'address\'])) ?></p>
        
        <p>Váš katalog bude odeslán na uvedenou adresu v nejbližší možné době.</p>
        
        <p>V případě jakýchkoliv dotazů nás neváhejte kontaktovat na emailu <?= htmlspecialchars($shop_email) ?>.</p>
        
        <p>S pozdravem,<br>
        tým <?= htmlspecialchars($shop_name) ?></p>
    </div>
</body>
</html>';
    }

    /**
     * Textová verze potvrzení objednávky
     */
    private function getOrderConfirmationTextTemplate(): string
    {
        return 'Děkujeme za Vaši objednávku!

Vážený zákazníku,
děkujeme za Vaši objednávku katalogu <?= $catalog[\'title\'] ?>.

Shrnutí objednávky:
Katalog: <?= $catalog[\'title\'] ?>
Datum objednávky: <?= $date ?>

Vaše údaje:
Jméno: <?= $order[\'first_name\'] . \' \' . $order[\'last_name\'] ?>
Email: <?= $order[\'email\'] ?>
Adresa: <?= $order[\'address\'] ?>

Váš katalog bude odeslán na uvedenou adresu v nejbližší možné době.

V případě jakýchkoliv dotazů nás neváhejte kontaktovat na emailu <?= $shop_email ?>.

S pozdravem,
tým <?= $shop_name ?>';
    }

    /**
     * Template pro admin upozornění
     */
    private function getAdminAlertTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Admin upozornění</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #e74c3c;">Admin upozornění</h2>
        
        <p><strong>Typ:</strong> <?= strtoupper($type) ?></p>
        <p><strong>Datum:</strong> <?= $date ?></p>
        
        <h3>Detaily:</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
<?= print_r($data, true) ?>
        </pre>
        
        <h3>Systémové informace:</h3>
        <p><strong>PHP verze:</strong> <?= $server_info[\'php_version\'] ?></p>
        <p><strong>PrestaShop verze:</strong> <?= $server_info[\'prestashop_version\'] ?></p>
        <p><strong>Modul verze:</strong> <?= $server_info[\'module_version\'] ?></p>
    </div>
</body>
</html>';
    }

    /**
     * Textová verze admin upozornění
     */
    private function getAdminAlertTextTemplate(): string
    {
        return 'Admin upozornění

Typ: <?= strtoupper($type) ?>
Datum: <?= $date ?>

Detaily:
<?= print_r($data, true) ?>

Systémové informace:
PHP verze: <?= $server_info[\'php_version\'] ?>
PrestaShop verze: <?= $server_info[\'prestashop_version\'] ?>
Modul verze: <?= $server_info[\'module_version\'] ?>';
    }

    /**
     * Obecný template
     */
    private function getGenericTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Email z <?= htmlspecialchars($shop_name) ?></title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2>Email z <?= htmlspecialchars($shop_name) ?></h2>
        
        <p>Tento email byl odeslán <?= $date ?>.</p>
        
        <!-- Zde přidejte vlastní obsah -->
        
    </div>
</body>
</html>';
    }

    /**
     * Vyčistí cache pro konkrétní template
     */
    private function clearTemplateCache(string $templateName): void
    {
        foreach (array_keys($this->templateCache) as $cacheKey) {
            if (strpos($cacheKey, $templateName . '_') === 0) {
                unset($this->templateCache[$cacheKey]);
            }
        }
    }
}
