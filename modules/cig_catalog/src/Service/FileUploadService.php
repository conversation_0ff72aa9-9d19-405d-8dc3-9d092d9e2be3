<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * FileUploadService - správa upload souborů a obrázků
 */
class FileUploadService
{
    private string $uploadsPath;
    private string $imagesPath;
    private string $filesPath;
    private ImageOptimizer $imageOptimizer;

    // Povolené typy souborů
    private const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
    private const ALLOWED_FILE_TYPES = ['application/pdf', 'application/zip', 'application/x-rar-compressed'];
    
    // Maximální velikosti
    private const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
    private const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

    public function __construct(string $moduleDir)
    {
        $this->uploadsPath = $moduleDir . '/uploads';
        $this->imagesPath = $this->uploadsPath . '/images';
        $this->filesPath = $this->uploadsPath . '/files';
        $this->imageOptimizer = new ImageOptimizer();
        
        $this->ensureDirectoriesExist();
    }

    /**
     * Upload obrázku katalogu
     */
    public function uploadImage(UploadedFile $file, string $type = 'catalog'): string
    {
        $this->validateImageFile($file);
        
        $filename = $this->generateSafeFilename($file->getClientOriginalName());
        $targetPath = $this->imagesPath . '/' . $type . '/' . $filename;
        
        // Vytvoření podadresáře pokud neexistuje
        $targetDir = dirname($targetPath);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }
        
        // Upload souboru
        $file->move($targetDir, basename($targetPath));
        
        // Optimalizace obrázku
        $optimizedImages = $this->imageOptimizer->optimizeAndResize($targetPath);
        
        // Vrátíme cestu k původnímu obrázku
        return str_replace($this->uploadsPath . '/', '', $targetPath);
    }

    /**
     * Upload souboru katalogu (PDF, ZIP, atd.)
     */
    public function uploadCatalogFile(UploadedFile $file): string
    {
        $this->validateCatalogFile($file);
        
        $filename = $this->generateSafeFilename($file->getClientOriginalName());
        $targetPath = $this->filesPath . '/' . $filename;
        
        // Upload souboru
        $file->move($this->filesPath, $filename);
        
        return str_replace($this->uploadsPath . '/', '', $targetPath);
    }

    /**
     * Smaže soubor
     */
    public function deleteFile(string $relativePath): bool
    {
        $fullPath = $this->uploadsPath . '/' . $relativePath;
        
        if (file_exists($fullPath)) {
            unlink($fullPath);
            
            // Smazání optimalizovaných verzí obrázků
            if (strpos($relativePath, 'images/') === 0) {
                $this->deleteOptimizedImages($fullPath);
            }
            
            return true;
        }
        
        return false;
    }

    /**
     * Duplikuje soubor
     */
    public function duplicateFile(string $relativePath): string
    {
        $sourcePath = $this->uploadsPath . '/' . $relativePath;
        
        if (!file_exists($sourcePath)) {
            throw new DomainException('Zdrojový soubor neexistuje');
        }
        
        $pathInfo = pathinfo($sourcePath);
        $newFilename = $pathInfo['filename'] . '_copy_' . time() . '.' . $pathInfo['extension'];
        $newPath = $pathInfo['dirname'] . '/' . $newFilename;
        
        if (copy($sourcePath, $newPath)) {
            // Duplikace optimalizovaných verzí pro obrázky
            if (strpos($relativePath, 'images/') === 0) {
                $this->duplicateOptimizedImages($sourcePath, $newPath);
            }
            
            return str_replace($this->uploadsPath . '/', '', $newPath);
        }
        
        throw new DomainException('Nepodařilo se duplikovat soubor');
    }

    /**
     * Validuje obrázek
     */
    public function validateImageFile(UploadedFile $file): bool
    {
        // Kontrola velikosti
        if ($file->getSize() > self::MAX_IMAGE_SIZE) {
            throw new DomainException('Obrázek je příliš velký (max 5MB)');
        }
        
        // Kontrola MIME typu
        if (!in_array($file->getMimeType(), self::ALLOWED_IMAGE_TYPES)) {
            throw new DomainException('Nepodporovaný formát obrázku (povolené: JPEG, PNG, WebP)');
        }
        
        // Kontrola, že je to skutečně obrázek
        $imageInfo = getimagesize($file->getPathname());
        if ($imageInfo === false) {
            throw new DomainException('Soubor není platný obrázek');
        }
        
        return true;
    }

    /**
     * Validuje soubor katalogu
     */
    public function validateCatalogFile(UploadedFile $file): bool
    {
        // Kontrola velikosti
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            throw new DomainException('Soubor je příliš velký (max 50MB)');
        }
        
        // Kontrola MIME typu
        if (!in_array($file->getMimeType(), self::ALLOWED_FILE_TYPES)) {
            throw new DomainException('Nepodporovaný formát souboru (povolené: PDF, ZIP, RAR)');
        }
        
        return true;
    }

    /**
     * Generuje bezpečný název souboru
     */
    private function generateSafeFilename(string $originalName): string
    {
        $pathInfo = pathinfo($originalName);
        $extension = strtolower($pathInfo['extension']);
        $basename = $pathInfo['filename'];
        
        // Odstranění nebezpečných znaků
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = preg_replace('/_+/', '_', $basename);
        $basename = trim($basename, '_');
        
        // Přidání timestamp pro unikátnost
        $timestamp = time();
        
        return $basename . '_' . $timestamp . '.' . $extension;
    }

    /**
     * Zajistí existenci potřebných adresářů
     */
    private function ensureDirectoriesExist(): void
    {
        $directories = [
            $this->uploadsPath,
            $this->imagesPath,
            $this->imagesPath . '/catalog',
            $this->filesPath
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // Vytvoření .htaccess pro bezpečnost
        $this->createSecurityFiles();
    }

    /**
     * Vytvoří bezpečnostní soubory
     */
    private function createSecurityFiles(): void
    {
        // .htaccess pro images (povolí zobrazení obrázků)
        $imagesHtaccess = $this->imagesPath . '/.htaccess';
        if (!file_exists($imagesHtaccess)) {
            file_put_contents($imagesHtaccess, "Options -Indexes\n<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\nOrder allow,deny\nDeny from all\n</Files>");
        }
        
        // .htaccess pro files (zakáže přímý přístup)
        $filesHtaccess = $this->filesPath . '/.htaccess';
        if (!file_exists($filesHtaccess)) {
            file_put_contents($filesHtaccess, "Order deny,allow\nDeny from all");
        }
        
        // index.php soubory
        $indexContent = "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Forbidden');\n";
        
        $indexFiles = [
            $this->uploadsPath . '/index.php',
            $this->imagesPath . '/index.php',
            $this->filesPath . '/index.php'
        ];
        
        foreach ($indexFiles as $indexFile) {
            if (!file_exists($indexFile)) {
                file_put_contents($indexFile, $indexContent);
            }
        }
    }

    /**
     * Smaže optimalizované verze obrázků
     */
    private function deleteOptimizedImages(string $originalPath): void
    {
        $pathInfo = pathinfo($originalPath);
        $sizes = ['thumbnail', 'medium', 'large'];
        
        foreach ($sizes as $size) {
            $optimizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $size . '.webp';
            if (file_exists($optimizedPath)) {
                unlink($optimizedPath);
            }
        }
    }

    /**
     * Duplikuje optimalizované verze obrázků
     */
    private function duplicateOptimizedImages(string $sourcePath, string $targetPath): void
    {
        $sourceInfo = pathinfo($sourcePath);
        $targetInfo = pathinfo($targetPath);
        $sizes = ['thumbnail', 'medium', 'large'];
        
        foreach ($sizes as $size) {
            $sourceOptimized = $sourceInfo['dirname'] . '/' . $sourceInfo['filename'] . '_' . $size . '.webp';
            $targetOptimized = $targetInfo['dirname'] . '/' . $targetInfo['filename'] . '_' . $size . '.webp';
            
            if (file_exists($sourceOptimized)) {
                copy($sourceOptimized, $targetOptimized);
            }
        }
    }

    /**
     * Získá URL pro obrázek
     */
    public function getImageUrl(string $relativePath, string $size = 'original'): string
    {
        if ($size !== 'original') {
            $pathInfo = pathinfo($relativePath);
            $optimizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $size . '.webp';
            
            if (file_exists($this->uploadsPath . '/' . $optimizedPath)) {
                return $optimizedPath;
            }
        }
        
        return $relativePath;
    }

    /**
     * Získá informace o souboru
     */
    public function getFileInfo(string $relativePath): array
    {
        $fullPath = $this->uploadsPath . '/' . $relativePath;
        
        if (!file_exists($fullPath)) {
            throw new DomainException('Soubor neexistuje');
        }
        
        return [
            'size' => filesize($fullPath),
            'mime_type' => mime_content_type($fullPath),
            'modified' => filemtime($fullPath),
            'exists' => true
        ];
    }
}
