<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;

/**
 * ImageOptimizer - optimalizace a změna velikosti obrázků
 */
class ImageOptimizer
{
    // Definované velikosti obrázků
    private const SIZES = [
        'thumbnail' => [300, 200],
        'medium' => [600, 400], 
        'large' => [1200, 800]
    ];

    // Kvalita komprese
    private const WEBP_QUALITY = 85;
    private const JPEG_QUALITY = 90;

    /**
     * Optimalizuje a vytvoří různé velikosti obrázku
     */
    public function optimizeAndResize(string $imagePath): array
    {
        if (!file_exists($imagePath)) {
            throw new DomainException('Zdrojový obrázek neexistuje');
        }

        $imageInfo = getimagesize($imagePath);
        if ($imageInfo === false) {
            throw new DomainException('Soubor není platn<PERSON> obr<PERSON>zek');
        }

        $optimizedImages = [];
        
        foreach (self::SIZES as $sizeName => $dimensions) {
            try {
                $optimizedPath = $this->createResizedImage(
                    $imagePath, 
                    $dimensions[0], 
                    $dimensions[1],
                    $sizeName
                );
                $optimizedImages[$sizeName] = $optimizedPath;
            } catch (\Exception $e) {
                // Pokud se nepodaří vytvořit optimalizovanou verzi, pokračujeme
                error_log("Chyba při optimalizaci obrázku {$sizeName}: " . $e->getMessage());
            }
        }
        
        return $optimizedImages;
    }

    /**
     * Vytvoří obrázek se změněnou velikostí
     */
    private function createResizedImage(string $source, int $width, int $height, string $suffix): string
    {
        $imageInfo = getimagesize($source);
        $mime = $imageInfo['mime'];
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];

        // Načtení zdrojového obrázku podle typu
        $sourceImage = $this->createImageFromFile($source, $mime);
        
        // Výpočet nových rozměrů se zachováním poměru stran
        $newDimensions = $this->calculateAspectRatio($originalWidth, $originalHeight, $width, $height);
        
        // Vytvoření nového obrázku
        $resizedImage = imagecreatetruecolor($newDimensions['width'], $newDimensions['height']);
        
        // Zachování průhlednosti pro PNG
        if ($mime === 'image/png') {
            imagealphablending($resizedImage, false);
            imagesavealpha($resizedImage, true);
            $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
            imagefill($resizedImage, 0, 0, $transparent);
        }
        
        // Změna velikosti
        imagecopyresampled(
            $resizedImage, 
            $sourceImage, 
            0, 0, 0, 0, 
            $newDimensions['width'], 
            $newDimensions['height'], 
            $originalWidth, 
            $originalHeight
        );
        
        // Vytvoření cesty pro nový soubor
        $pathInfo = pathinfo($source);
        $newPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $suffix . '.webp';
        
        // Uložení jako WebP pro lepší kompresi
        imagewebp($resizedImage, $newPath, self::WEBP_QUALITY);
        
        // Uvolnění paměti
        imagedestroy($sourceImage);
        imagedestroy($resizedImage);
        
        return $newPath;
    }

    /**
     * Vytvoří image resource ze souboru podle MIME typu
     */
    private function createImageFromFile(string $filepath, string $mime)
    {
        switch ($mime) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filepath);
            case 'image/png':
                return imagecreatefrompng($filepath);
            case 'image/webp':
                return imagecreatefromwebp($filepath);
            case 'image/gif':
                return imagecreatefromgif($filepath);
            default:
                throw new DomainException('Nepodporovaný formát obrázku: ' . $mime);
        }
    }

    /**
     * Vypočítá nové rozměry se zachováním poměru stran
     */
    private function calculateAspectRatio(int $originalWidth, int $originalHeight, int $maxWidth, int $maxHeight): array
    {
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        
        return [
            'width' => (int)round($originalWidth * $ratio),
            'height' => (int)round($originalHeight * $ratio)
        ];
    }

    /**
     * Optimalizuje existující obrázek bez změny velikosti
     */
    public function optimizeExisting(string $imagePath): string
    {
        if (!file_exists($imagePath)) {
            throw new DomainException('Obrázek neexistuje');
        }

        $imageInfo = getimagesize($imagePath);
        if ($imageInfo === false) {
            throw new DomainException('Soubor není platný obrázek');
        }

        $mime = $imageInfo['mime'];
        $sourceImage = $this->createImageFromFile($imagePath, $mime);
        
        // Vytvoření optimalizované verze
        $pathInfo = pathinfo($imagePath);
        $optimizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_optimized.webp';
        
        imagewebp($sourceImage, $optimizedPath, self::WEBP_QUALITY);
        imagedestroy($sourceImage);
        
        return $optimizedPath;
    }

    /**
     * Vytvoří thumbnail s přesnou velikostí (crop)
     */
    public function createThumbnailCrop(string $source, int $width, int $height): string
    {
        $imageInfo = getimagesize($source);
        $mime = $imageInfo['mime'];
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];

        $sourceImage = $this->createImageFromFile($source, $mime);
        
        // Výpočet crop pozice pro centrování
        $sourceRatio = $originalWidth / $originalHeight;
        $targetRatio = $width / $height;
        
        if ($sourceRatio > $targetRatio) {
            // Zdrojový obrázek je širší - crop ze stran
            $cropHeight = $originalHeight;
            $cropWidth = (int)($originalHeight * $targetRatio);
            $cropX = (int)(($originalWidth - $cropWidth) / 2);
            $cropY = 0;
        } else {
            // Zdrojový obrázek je vyšší - crop shora/zdola
            $cropWidth = $originalWidth;
            $cropHeight = (int)($originalWidth / $targetRatio);
            $cropX = 0;
            $cropY = (int)(($originalHeight - $cropHeight) / 2);
        }
        
        // Vytvoření thumbnail
        $thumbnail = imagecreatetruecolor($width, $height);
        
        // Zachování průhlednosti pro PNG
        if ($mime === 'image/png') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }
        
        imagecopyresampled(
            $thumbnail, 
            $sourceImage, 
            0, 0, 
            $cropX, $cropY, 
            $width, $height, 
            $cropWidth, $cropHeight
        );
        
        // Uložení
        $pathInfo = pathinfo($source);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb_crop.webp';
        
        imagewebp($thumbnail, $thumbnailPath, self::WEBP_QUALITY);
        
        imagedestroy($sourceImage);
        imagedestroy($thumbnail);
        
        return $thumbnailPath;
    }

    /**
     * Získá informace o obrázku
     */
    public function getImageInfo(string $imagePath): array
    {
        if (!file_exists($imagePath)) {
            throw new DomainException('Obrázek neexistuje');
        }

        $imageInfo = getimagesize($imagePath);
        if ($imageInfo === false) {
            throw new DomainException('Soubor není platný obrázek');
        }

        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime' => $imageInfo['mime'],
            'size' => filesize($imagePath),
            'ratio' => round($imageInfo[0] / $imageInfo[1], 2)
        ];
    }

    /**
     * Kontroluje, zda jsou dostupné potřebné GD funkce
     */
    public function checkGdSupport(): array
    {
        $support = [
            'gd_enabled' => extension_loaded('gd'),
            'jpeg_support' => false,
            'png_support' => false,
            'webp_support' => false,
            'gif_support' => false
        ];

        if ($support['gd_enabled']) {
            $gdInfo = gd_info();
            $support['jpeg_support'] = $gdInfo['JPEG Support'] ?? false;
            $support['png_support'] = $gdInfo['PNG Support'] ?? false;
            $support['webp_support'] = $gdInfo['WebP Support'] ?? false;
            $support['gif_support'] = $gdInfo['GIF Read Support'] ?? false;
        }

        return $support;
    }

    /**
     * Vytvoří watermark na obrázku
     */
    public function addWatermark(string $imagePath, string $watermarkPath, string $position = 'bottom-right'): string
    {
        if (!file_exists($imagePath) || !file_exists($watermarkPath)) {
            throw new DomainException('Zdrojový obrázek nebo watermark neexistuje');
        }

        $imageInfo = getimagesize($imagePath);
        $watermarkInfo = getimagesize($watermarkPath);
        
        $sourceImage = $this->createImageFromFile($imagePath, $imageInfo['mime']);
        $watermarkImage = $this->createImageFromFile($watermarkPath, $watermarkInfo['mime']);
        
        // Výpočet pozice watermarku
        $positions = $this->calculateWatermarkPosition(
            $imageInfo[0], $imageInfo[1],
            $watermarkInfo[0], $watermarkInfo[1],
            $position
        );
        
        // Aplikace watermarku
        imagecopymerge(
            $sourceImage, 
            $watermarkImage, 
            $positions['x'], $positions['y'], 
            0, 0, 
            $watermarkInfo[0], $watermarkInfo[1], 
            70 // 70% průhlednost
        );
        
        // Uložení
        $pathInfo = pathinfo($imagePath);
        $watermarkedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_watermarked.webp';
        
        imagewebp($sourceImage, $watermarkedPath, self::WEBP_QUALITY);
        
        imagedestroy($sourceImage);
        imagedestroy($watermarkImage);
        
        return $watermarkedPath;
    }

    /**
     * Vypočítá pozici watermarku
     */
    private function calculateWatermarkPosition(int $imageWidth, int $imageHeight, int $watermarkWidth, int $watermarkHeight, string $position): array
    {
        $margin = 10;
        
        switch ($position) {
            case 'top-left':
                return ['x' => $margin, 'y' => $margin];
            case 'top-right':
                return ['x' => $imageWidth - $watermarkWidth - $margin, 'y' => $margin];
            case 'bottom-left':
                return ['x' => $margin, 'y' => $imageHeight - $watermarkHeight - $margin];
            case 'bottom-right':
                return ['x' => $imageWidth - $watermarkWidth - $margin, 'y' => $imageHeight - $watermarkHeight - $margin];
            case 'center':
                return [
                    'x' => (int)(($imageWidth - $watermarkWidth) / 2),
                    'y' => (int)(($imageHeight - $watermarkHeight) / 2)
                ];
            default:
                return ['x' => $imageWidth - $watermarkWidth - $margin, 'y' => $imageHeight - $watermarkHeight - $margin];
        }
    }
}
