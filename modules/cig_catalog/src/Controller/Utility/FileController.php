<?php
/**
 * File Utility Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Utility;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use CigCatalog\Service\FileManager;

class FileController extends FrameworkBundleAdminController
{
    private FileManager $fileManager;

    public function __construct(FileManager $fileManager)
    {
        $this->fileManager = $fileManager;
    }

    /**
     * Serve image files
     */
    public function serveImageAction(Request $request, string $filename): Response
    {
        try {
            $filePath = $this->fileManager->getImagePath($filename);
            
            if (!file_exists($filePath)) {
                throw $this->createNotFoundException('Obrázek nebyl nalezen.');
            }

            $response = new BinaryFileResponse($filePath);
            $response->headers->set('Content-Type', $this->getMimeType($filePath));
            
            return $response;
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Obrázek nebyl nalezen.');
        }
    }

    /**
     * Serve catalog files
     */
    public function serveFileAction(Request $request, string $filename): Response
    {
        try {
            $filePath = $this->fileManager->getFilePath($filename);
            
            if (!file_exists($filePath)) {
                throw $this->createNotFoundException('Soubor nebyl nalezen.');
            }

            $response = new BinaryFileResponse($filePath);
            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $filename
            );
            
            return $response;
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Soubor nebyl nalezen.');
        }
    }

    /**
     * Get MIME type for file
     */
    private function getMimeType(string $filePath): string
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            'zip' => 'application/zip',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
