<?php
/**
 * Robots.txt Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Utility;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class RobotsController extends FrameworkBundleAdminController
{
    /**
     * Generate robots.txt
     */
    public function indexAction(Request $request): Response
    {
        $robotsTxt = $this->generateRobotsTxt();

        $response = new Response($robotsTxt);
        $response->headers->set('Content-Type', 'text/plain');
        
        return $response;
    }

    private function generateRobotsTxt(): string
    {
        $content = "# Robots.txt for CIG Catalog Module\n\n";
        $content .= "User-agent: *\n";
        $content .= "Allow: /module/cig_catalog/\n";
        $content .= "Disallow: /module/cig_catalog/uploads/\n";
        $content .= "Disallow: /admin/catalog/\n\n";
        
        // Add sitemap reference
        $sitemapUrl = $this->generateUrl('catalog_sitemap');
        $content .= "Sitemap: " . $sitemapUrl . "\n";

        return $content;
    }
}
