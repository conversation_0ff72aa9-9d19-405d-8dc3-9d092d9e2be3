<?php
/**
 * Frontend Order Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Front;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use CigCatalog\Service\CatalogOrderService;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Validator\OrderValidator;

class OrderController extends FrameworkBundleAdminController
{
    private CatalogOrderService $orderService;
    private CatalogRepository $catalogRepository;
    private OrderValidator $orderValidator;

    public function __construct(
        CatalogOrderService $orderService,
        CatalogRepository $catalogRepository,
        OrderValidator $orderValidator
    ) {
        $this->orderService = $orderService;
        $this->catalogRepository = $catalogRepository;
        $this->orderValidator = $orderValidator;
    }

    /**
     * Create order
     */
    public function createAction(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Neplatný formát dat.'
                ], 400);
            }

            // Validate CSRF token
            if (!$this->isCsrfTokenValid('catalog_order', $data['_token'] ?? '')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Neplatný bezpečnostní token.'
                ], 403);
            }

            // Validate order data
            $validationResult = $this->orderValidator->validate($data);
            if (!$validationResult['valid']) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Neplatné údaje.',
                    'errors' => $validationResult['errors']
                ], 400);
            }

            // Check if catalog exists
            $catalogId = (int)($data['catalog_id'] ?? 0);
            $catalog = $this->catalogRepository->findById($catalogId);
            
            if (!$catalog || !$catalog->active) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Katalog nebyl nalezen.'
                ], 404);
            }

            // Create order
            $orderId = $this->orderService->createOrder($data);

            return new JsonResponse([
                'success' => true,
                'message' => 'Objednávka byla úspěšně vytvořena.',
                'order_id' => $orderId
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při vytváření objednávky: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Order success page
     */
    public function successAction(Request $request): Response
    {
        return $this->render('@Modules/cig_catalog/templates/front/order/success.html.twig', [
            'pageTitle' => 'Objednávka byla odeslána'
        ]);
    }

    /**
     * Order error page
     */
    public function errorAction(Request $request): Response
    {
        return $this->render('@Modules/cig_catalog/templates/front/order/error.html.twig', [
            'pageTitle' => 'Chyba při objednávce'
        ]);
    }
}
