<?php
/**
 * Admin Catalog Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Admin;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Service\CatalogManager;
use CigCatalog\Service\FileManager;
use CigCatalog\Form\CatalogType;
use CigCatalog\Entity\Catalog;
use Psr\Log\LoggerInterface;

class AdminCatalogController extends FrameworkBundleAdminController
{
    private CatalogRepository $catalogRepository;
    private CatalogManager $catalogManager;
    private FileManager $fileManager;
    private LoggerInterface $logger;

    public function __construct(
        CatalogRepository $catalogRepository,
        CatalogManager $catalogManager,
        FileManager $fileManager,
        LoggerInterface $logger
    ) {
        $this->catalogRepository = $catalogRepository;
        $this->catalogManager = $catalogManager;
        $this->fileManager = $fileManager;
        $this->logger = $logger;
    }

    /**
     * Index action - listing katalogů
     */
    public function indexAction(Request $request): Response
    {
        $page = max(1, $request->query->getInt('page', 1));
        $perPage = 20;
        $search = $request->query->get('search', '');
        $langId = $this->getContext()->language->id;

        try {
            if ($search) {
                $catalogs = $this->catalogRepository->search($search, $langId, $page, $perPage);
                $totalCatalogs = $this->catalogRepository->countSearch($search, $langId);
            } else {
                $catalogs = $this->catalogRepository->findWithPagination($page, $perPage, $langId);
                $totalCatalogs = $this->catalogRepository->count();
            }

            $totalPages = ceil($totalCatalogs / $perPage);

            return $this->render('@Modules/cig_catalog/templates/admin/catalog/index.html.twig', [
                'catalogs' => $catalogs,
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'totalCatalogs' => $totalCatalogs,
                'search' => $search,
                'perPage' => $perPage
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při načítání katalogů: ' . $e->getMessage());
            
            return $this->render('@Modules/cig_catalog/templates/admin/catalog/index.html.twig', [
                'catalogs' => [],
                'currentPage' => 1,
                'totalPages' => 0,
                'totalCatalogs' => 0,
                'search' => $search,
                'perPage' => $perPage
            ]);
        }
    }

    /**
     * New action - formulář pro nový katalog
     */
    public function newAction(Request $request): Response
    {
        $catalog = new Catalog();
        $form = $this->createForm(CatalogType::class, $catalog);

        return $this->render('@Modules/cig_catalog/templates/admin/catalog/form.html.twig', [
            'form' => $form->createView(),
            'catalog' => $catalog,
            'isEdit' => false,
            'pageTitle' => 'Nový katalog'
        ]);
    }

    /**
     * Create action - zpracování vytvoření
     */
    public function createAction(Request $request): Response
    {
        $catalog = new Catalog();
        $form = $this->createForm(CatalogType::class, $catalog);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Zpracování upload souborů
                $imageFile = $form->get('image_file')->getData();
                $catalogFile = $form->get('catalog_file')->getData();

                $catalogData = [
                    'active' => $catalog->active ? 1 : 0,
                    'position' => $catalog->position ?: $this->catalogRepository->getNextPosition(),
                    'is_new' => $catalog->is_new ? 1 : 0,
                    'lang' => []
                ];

                // Multijazyčné údaje - zatím jen pro češtinu (ID 1)
                $catalogData['lang'][1] = [
                    'name' => $form->get('name_1')->getData() ?? '',
                    'description' => $form->get('description_1')->getData() ?? '',
                    'slug' => $this->catalogManager->generateSlug($form->get('name_1')->getData() ?? '')
                ];

                $catalogId = $this->catalogRepository->create($catalogData);

                // Upload obrázku
                if ($imageFile) {
                    $imagePath = $this->fileManager->uploadImage($imageFile, $catalogId);
                    $this->catalogRepository->update($catalogId, ['image' => $imagePath]);
                }

                // Upload souboru katalogu
                if ($catalogFile) {
                    $filePath = $this->fileManager->uploadCatalogFile($catalogFile, $catalogId);
                    $this->catalogRepository->update($catalogId, ['file_path' => $filePath]);
                }

                $this->addFlash('success', 'Katalog byl úspěšně vytvořen.');
                
                return $this->redirectToRoute('admin_catalog_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Chyba při vytváření katalogu: ' . $e->getMessage());
            }
        }

        return $this->render('@Modules/cig_catalog/templates/admin/catalog/form.html.twig', [
            'form' => $form->createView(),
            'catalog' => $catalog,
            'isEdit' => false,
            'pageTitle' => 'Nový katalog'
        ]);
    }

    /**
     * Edit action - formulář pro editaci
     */
    public function editAction(Request $request, int $id): Response
    {
        try {
            $catalog = $this->catalogRepository->findById($id);
            if (!$catalog) {
                $this->addFlash('error', 'Katalog nebyl nalezen.');
                return $this->redirectToRoute('admin_catalog_index');
            }

            $form = $this->createForm(CatalogType::class, $catalog);

            return $this->render('@Modules/cig_catalog/templates/admin/catalog/form.html.twig', [
                'form' => $form->createView(),
                'catalog' => $catalog,
                'isEdit' => true,
                'pageTitle' => 'Editace katalogu: ' . ($catalog->name[$this->getContext()->language->id] ?? '')
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při načítání katalogu: ' . $e->getMessage());
            return $this->redirectToRoute('admin_catalog_index');
        }
    }

    /**
     * Update action - zpracování aktualizace
     */
    public function updateAction(Request $request, int $id): Response
    {
        try {
            $catalog = $this->catalogRepository->findById($id);
            if (!$catalog) {
                $this->addFlash('error', 'Katalog nebyl nalezen.');
                return $this->redirectToRoute('admin_catalog_index');
            }

            $form = $this->createForm(CatalogType::class, $catalog);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Zpracování upload souborů
                $imageFile = $form->get('image_file')->getData();
                $catalogFile = $form->get('catalog_file')->getData();

                $catalogData = [
                    'active' => $catalog->active ? 1 : 0,
                    'position' => $catalog->position,
                    'is_new' => $catalog->is_new ? 1 : 0,
                    'lang' => []
                ];

                // Multijazyčné údaje - zatím jen pro češtinu (ID 1)
                $catalogData['lang'][1] = [
                    'name' => $form->get('name_1')->getData() ?? '',
                    'description' => $form->get('description_1')->getData() ?? '',
                    'slug' => $this->catalogManager->generateSlug($form->get('name_1')->getData() ?? '')
                ];

                $this->catalogRepository->update($id, $catalogData);

                // Upload nového obrázku
                if ($imageFile) {
                    // Smazání starého obrázku
                    if ($catalog->image) {
                        $this->fileManager->deleteFile($catalog->image);
                    }
                    $imagePath = $this->fileManager->uploadImage($imageFile, $id);
                    $this->catalogRepository->update($id, ['image' => $imagePath]);
                }

                // Upload nového souboru katalogu
                if ($catalogFile) {
                    // Smazání starého souboru
                    if ($catalog->file_path) {
                        $this->fileManager->deleteFile($catalog->file_path);
                    }
                    $filePath = $this->fileManager->uploadCatalogFile($catalogFile, $id);
                    $this->catalogRepository->update($id, ['file_path' => $filePath]);
                }

                $this->addFlash('success', 'Katalog byl úspěšně aktualizován.');
                
                return $this->redirectToRoute('admin_catalog_index');
            }

            return $this->render('@Modules/cig_catalog/templates/admin/catalog/form.html.twig', [
                'form' => $form->createView(),
                'catalog' => $catalog,
                'isEdit' => true,
                'pageTitle' => 'Editace katalogu: ' . ($catalog->name[$this->getContext()->language->id] ?? '')
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při aktualizaci katalogu: ' . $e->getMessage());
            return $this->redirectToRoute('admin_catalog_index');
        }
    }

    /**
     * Delete action - smazání katalogu
     */
    public function deleteAction(Request $request, int $id): JsonResponse
    {
        if (!$this->isCsrfTokenValid('delete_catalog_' . $id, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný CSRF token.'], 400);
        }

        try {
            $catalog = $this->catalogRepository->findById($id);
            if (!$catalog) {
                return new JsonResponse(['success' => false, 'message' => 'Katalog nebyl nalezen.'], 404);
            }

            // Smazání souborů
            if ($catalog->image) {
                $this->fileManager->deleteFile($catalog->image);
            }
            if ($catalog->file_path) {
                $this->fileManager->deleteFile($catalog->file_path);
            }

            $this->catalogRepository->delete($id);

            return new JsonResponse(['success' => true, 'message' => 'Katalog byl úspěšně smazán.']);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Chyba při mazání katalogu: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Toggle active action - přepnutí aktivní/neaktivní
     */
    public function toggleActiveAction(Request $request, int $id): JsonResponse
    {
        if (!$this->isCsrfTokenValid('toggle_active_' . $id, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný CSRF token.'], 400);
        }

        try {
            $catalog = $this->catalogRepository->findById($id);
            if (!$catalog) {
                return new JsonResponse(['success' => false, 'message' => 'Katalog nebyl nalezen.'], 404);
            }

            $newStatus = !$catalog->active;
            $this->catalogRepository->update($id, ['active' => $newStatus ? 1 : 0]);

            return new JsonResponse([
                'success' => true,
                'message' => 'Stav katalogu byl změněn.',
                'active' => $newStatus
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Chyba při změně stavu: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Duplicate action - duplikace katalogu
     */
    public function duplicateAction(Request $request, int $id): Response
    {
        try {
            $originalCatalog = $this->catalogRepository->findById($id);
            if (!$originalCatalog) {
                $this->addFlash('error', 'Katalog nebyl nalezen.');
                return $this->redirectToRoute('admin_catalog_index');
            }

            $duplicatedId = $this->catalogManager->duplicateCatalog($id);

            $this->addFlash('success', 'Katalog byl úspěšně duplikován.');

            return $this->redirectToRoute('admin_catalog_edit', ['id' => $duplicatedId]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při duplikaci katalogu: ' . $e->getMessage());
            return $this->redirectToRoute('admin_catalog_index');
        }
    }

    /**
     * Bulk action - hromadné operace
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $action = $request->request->get('action');
        $catalogIds = $request->request->get('catalog_ids', []);

        if (empty($catalogIds) || !is_array($catalogIds)) {
            return new JsonResponse(['success' => false, 'message' => 'Nebyly vybrány žádné katalogy.'], 400);
        }

        if (!$this->isCsrfTokenValid('bulk_action', $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný CSRF token.'], 400);
        }

        try {
            $count = 0;

            switch ($action) {
                case 'activate':
                    foreach ($catalogIds as $catalogId) {
                        $this->catalogRepository->update((int)$catalogId, ['active' => 1]);
                        $count++;
                    }
                    $message = "Aktivováno {$count} katalogů.";
                    break;

                case 'deactivate':
                    foreach ($catalogIds as $catalogId) {
                        $this->catalogRepository->update((int)$catalogId, ['active' => 0]);
                        $count++;
                    }
                    $message = "Deaktivováno {$count} katalogů.";
                    break;

                case 'delete':
                    foreach ($catalogIds as $catalogId) {
                        $catalog = $this->catalogRepository->findById((int)$catalogId);
                        if ($catalog) {
                            // Smazání souborů
                            if ($catalog->image) {
                                $this->fileManager->deleteFile($catalog->image);
                            }
                            if ($catalog->file_path) {
                                $this->fileManager->deleteFile($catalog->file_path);
                            }
                            $this->catalogRepository->delete((int)$catalogId);
                            $count++;
                        }
                    }
                    $message = "Smazáno {$count} katalogů.";
                    break;

                default:
                    return new JsonResponse(['success' => false, 'message' => 'Neplatná akce.'], 400);
            }

            return new JsonResponse(['success' => true, 'message' => $message]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Chyba při hromadné operaci: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Reorder action - změna pořadí katalogů
     */
    public function reorderAction(Request $request): JsonResponse
    {
        // Security checks
        if (!$this->validateAjaxRequest($request)) {
            return $this->createErrorResponse('Neplatný požadavek.', 400);
        }

        $data = json_decode($request->getContent(), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->warning('Invalid JSON in reorder request', [
                'content' => $request->getContent(),
                'error' => json_last_error_msg()
            ]);
            return $this->createErrorResponse('Neplatný JSON formát.', 400);
        }

        $positions = $data['positions'] ?? [];

        if (empty($positions) || !is_array($positions)) {
            return $this->createErrorResponse('Nebyla zadána žádná pozice.', 400);
        }

        if (!$this->isCsrfTokenValid('reorder_catalogs', $data['_token'] ?? '')) {
            $this->logger->warning('Invalid CSRF token in reorder request', [
                'user_id' => $this->getContext()->employee->id ?? null,
                'ip' => $request->getClientIp()
            ]);
            return $this->createErrorResponse('Neplatný CSRF token.', 403);
        }

        // Validate positions data
        foreach ($positions as $catalogId => $position) {
            if (!is_numeric($catalogId) || !is_numeric($position) || $position < 1) {
                return $this->createErrorResponse('Neplatné pozice katalogů.', 400);
            }
        }

        try {
            $success = $this->catalogManager->reorderCatalogs($positions);

            if ($success) {
                $this->logger->info('Catalogs reordered successfully', [
                    'user_id' => $this->getContext()->employee->id ?? null,
                    'positions_count' => count($positions)
                ]);

                return new JsonResponse([
                    'success' => true,
                    'message' => 'Pořadí katalogů bylo úspěšně aktualizováno.'
                ]);
            } else {
                return $this->createErrorResponse('Chyba při aktualizaci pořadí katalogů.', 500);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error reordering catalogs', [
                'error' => $e->getMessage(),
                'user_id' => $this->getContext()->employee->id ?? null,
                'positions' => $positions
            ]);

            return $this->createErrorResponse('Chyba při aktualizaci pořadí: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Upload action - nahrávání souborů
     */
    public function uploadAction(Request $request): JsonResponse
    {
        // Security validation
        if (!$this->validateAjaxRequest($request)) {
            return $this->createErrorResponse('Neplatný požadavek.', 400);
        }

        $file = $request->files->get('file');

        if (!$file) {
            return $this->createErrorResponse('Nebyl nahrán žádný soubor.', 400);
        }

        if (!$this->isCsrfTokenValid('file_upload', $request->request->get('_token'))) {
            $this->logger->warning('Invalid CSRF token in upload request', [
                'user_id' => $this->getContext()->employee->id ?? null,
                'ip' => $request->getClientIp(),
                'file_name' => $file->getClientOriginalName()
            ]);
            return $this->createErrorResponse('Neplatný CSRF token.', 403);
        }

        // Sanitize uploaded file
        if (!$this->sanitizeUploadedFile($file)) {
            $this->logger->warning('Unsafe file upload attempt', [
                'user_id' => $this->getContext()->employee->id ?? null,
                'ip' => $request->getClientIp(),
                'file_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize()
            ]);
            return $this->createErrorResponse('Soubor nesplňuje bezpečnostní požadavky.', 400);
        }

        try {
            // Additional file validation
            if ($file->getError() !== UPLOAD_ERR_OK) {
                $uploadErrors = [
                    UPLOAD_ERR_INI_SIZE => 'Soubor překračuje maximální povolenou velikost.',
                    UPLOAD_ERR_FORM_SIZE => 'Soubor překračuje maximální velikost formuláře.',
                    UPLOAD_ERR_PARTIAL => 'Soubor byl nahrán pouze částečně.',
                    UPLOAD_ERR_NO_FILE => 'Nebyl vybrán žádný soubor.',
                    UPLOAD_ERR_NO_TMP_DIR => 'Chybí dočasný adresář.',
                    UPLOAD_ERR_CANT_WRITE => 'Nepodařilo se zapsat soubor na disk.',
                    UPLOAD_ERR_EXTENSION => 'Nahrávání bylo zastaveno rozšířením.'
                ];

                $errorMessage = $uploadErrors[$file->getError()] ?? 'Neznámá chyba při nahrávání.';
                return $this->createErrorResponse($errorMessage, 400);
            }

            // Upload souboru
            $uploadedPath = $this->fileManager->uploadFile($file);

            $response = [
                'file_path' => $uploadedPath,
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'formatted_size' => $this->formatFileSize($file->getSize()),
                'upload_time' => date('c')
            ];

            // Pro obrázky vytvoříme náhledy
            if (strpos($file->getMimeType(), 'image/') === 0) {
                try {
                    $thumbnails = $this->fileManager->createThumbnails($uploadedPath);
                    $response['thumbnails'] = $thumbnails;
                    $response['is_image'] = true;
                } catch (\Exception $e) {
                    // Náhledy nejsou kritické, pokračujeme bez nich
                    $response['thumbnail_error'] = $e->getMessage();
                    $this->logger->warning('Failed to create thumbnails', [
                        'file_path' => $uploadedPath,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $this->logger->info('File uploaded successfully', [
                'user_id' => $this->getContext()->employee->id ?? null,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $uploadedPath,
                'file_size' => $file->getSize()
            ]);

            return $this->createSuccessResponse('Soubor byl úspěšně nahrán.', $response);
        } catch (\Exception $e) {
            $this->logger->error('File upload failed', [
                'user_id' => $this->getContext()->employee->id ?? null,
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);

            return $this->createErrorResponse('Chyba při nahrávání souboru: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete file action - smazání nahraného souboru
     */
    public function deleteFileAction(Request $request, int $id, string $type): JsonResponse
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Pouze AJAX požadavky jsou povoleny.'], 400);
        }

        if (!in_array($type, ['image', 'file'])) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný typ souboru.'], 400);
        }

        if (!$this->isCsrfTokenValid('delete_file_' . $id . '_' . $type, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný CSRF token.'], 400);
        }

        try {
            $catalog = $this->catalogRepository->findById($id);
            if (!$catalog) {
                return new JsonResponse(['success' => false, 'message' => 'Katalog nebyl nalezen.'], 404);
            }

            $filePath = null;
            $updateData = [];

            if ($type === 'image' && $catalog->image) {
                $filePath = $catalog->image;
                $updateData['image'] = null;
            } elseif ($type === 'file' && $catalog->file_path) {
                $filePath = $catalog->file_path;
                $updateData['file_path'] = null;
            }

            if ($filePath) {
                // Smazání souboru z disku
                $this->fileManager->deleteFile($filePath);

                // Aktualizace databáze
                $this->catalogRepository->update($id, $updateData);

                return new JsonResponse([
                    'success' => true,
                    'message' => 'Soubor byl úspěšně smazán.'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Soubor nebyl nalezen.'
                ], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při mazání souboru: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format file size helper
     */
    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';

        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Email configuration action - konfigurace emailů
     */
    public function emailConfigurationAction(Request $request): Response
    {
        try {
            // Načtení aktuální konfigurace
            $currentConfig = $this->catalogManager->getEmailConfiguration();

            $form = $this->createForm(\CigCatalog\Form\EmailConfigurationType::class, $currentConfig);
            $form->handleRequest($request);

            if ($form->isSubmitted()) {
                if ($form->getClickedButton() && $form->getClickedButton()->getName() === 'test_email') {
                    // Testovací email
                    return $this->handleTestEmail($form->getData());
                } elseif ($form->isValid()) {
                    // Uložení konfigurace
                    $this->catalogManager->saveEmailConfiguration($form->getData());
                    $this->addFlash('success', 'Email konfigurace byla úspěšně uložena.');

                    return $this->redirectToRoute('admin_catalog_email_config');
                }
            }

            return $this->render('@Modules/cig_catalog/templates/admin/email/configuration.html.twig', [
                'form' => $form->createView(),
                'pageTitle' => 'Konfigurace emailů'
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při načítání konfigurace: ' . $e->getMessage());

            return $this->render('@Modules/cig_catalog/templates/admin/email/configuration.html.twig', [
                'form' => null,
                'pageTitle' => 'Konfigurace emailů'
            ]);
        }
    }

    /**
     * Test email action - odeslání testovacího emailu
     */
    public function testEmailAction(Request $request): JsonResponse
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Pouze AJAX požadavky jsou povoleny.'], 400);
        }

        if (!$this->isCsrfTokenValid('test_email', $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Neplatný CSRF token.'], 400);
        }

        try {
            $emailConfig = $request->request->all();
            $testEmail = $emailConfig['test_email_address'] ?? '';

            if (empty($testEmail) || !filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Zadejte platnou email adresu pro test.'
                ], 400);
            }

            // Odeslání testovacího emailu
            $result = $this->catalogManager->sendTestEmail($testEmail, $emailConfig);

            if ($result) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Testovací email byl úspěšně odeslán na adresu: ' . $testEmail
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Chyba při odesílání testovacího emailu.'
                ], 500);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při odesílání emailu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle test email from form
     */
    private function handleTestEmail(array $formData): Response
    {
        try {
            // Získání test email adresy z formuláře nebo použití první z příjemců
            $testEmail = $formData['email_from_address'] ?? '';
            if (empty($testEmail)) {
                $recipients = array_map('trim', explode(',', $formData['email_recipients'] ?? ''));
                $testEmail = $recipients[0] ?? '';
            }

            if (empty($testEmail) || !filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                $this->addFlash('error', 'Nelze odeslat testovací email - není zadána platná email adresa.');
            } else {
                $result = $this->catalogManager->sendTestEmail($testEmail, $formData);

                if ($result) {
                    $this->addFlash('success', 'Testovací email byl úspěšně odeslán na adresu: ' . $testEmail);
                } else {
                    $this->addFlash('error', 'Chyba při odesílání testovacího emailu.');
                }
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při odesílání emailu: ' . $e->getMessage());
        }

        return $this->redirectToRoute('admin_catalog_email_config');
    }

    /**
     * Validate AJAX request
     */
    private function validateAjaxRequest(Request $request): bool
    {
        // Check if request is AJAX
        if (!$request->isXmlHttpRequest()) {
            return false;
        }

        // Check rate limiting (simple implementation)
        $session = $request->getSession();
        $lastRequest = $session->get('last_ajax_request', 0);
        $now = time();

        if ($now - $lastRequest < 1) { // Max 1 request per second
            $this->logger->warning('Rate limit exceeded for AJAX request', [
                'ip' => $request->getClientIp(),
                'user_agent' => $request->headers->get('User-Agent')
            ]);
            return false;
        }

        $session->set('last_ajax_request', $now);

        // Check content type for POST requests
        if ($request->isMethod('POST')) {
            $contentType = $request->headers->get('Content-Type');
            $allowedTypes = [
                'application/x-www-form-urlencoded',
                'application/json',
                'multipart/form-data'
            ];

            $isValidContentType = false;
            foreach ($allowedTypes as $type) {
                if (strpos($contentType, $type) === 0) {
                    $isValidContentType = true;
                    break;
                }
            }

            if (!$isValidContentType) {
                $this->logger->warning('Invalid content type for AJAX request', [
                    'content_type' => $contentType,
                    'ip' => $request->getClientIp()
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Create standardized error response
     */
    private function createErrorResponse(string $message, int $statusCode = 400): JsonResponse
    {
        return new JsonResponse([
            'success' => false,
            'message' => $message,
            'timestamp' => date('c')
        ], $statusCode);
    }

    /**
     * Create standardized success response
     */
    private function createSuccessResponse(string $message, array $data = []): JsonResponse
    {
        return new JsonResponse(array_merge([
            'success' => true,
            'message' => $message,
            'timestamp' => date('c')
        ], $data));
    }

    /**
     * Validate catalog ownership (for multi-shop environments)
     */
    private function validateCatalogAccess(int $catalogId): bool
    {
        // In multi-shop environment, check if user has access to this catalog
        // For now, return true - implement based on PrestaShop's shop context
        return true;
    }

    /**
     * Sanitize file upload
     */
    private function sanitizeUploadedFile(\Symfony\Component\HttpFoundation\File\UploadedFile $file): bool
    {
        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'zip', 'doc', 'docx', 'xls', 'xlsx'];
        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($extension, $allowedExtensions)) {
            return false;
        }

        // Check MIME type
        $allowedMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'application/zip',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            return false;
        }

        // Check file size (50MB max)
        if ($file->getSize() > 50 * 1024 * 1024) {
            return false;
        }

        return true;
    }

    /**
     * Configuration action - konfigurace modulu
     */
    public function configurationAction(Request $request): Response
    {
        // Tato metoda bude implementována v pozdějších krocích
        return $this->render('@Modules/cig_catalog/templates/admin/configuration/index.html.twig');
    }
}
