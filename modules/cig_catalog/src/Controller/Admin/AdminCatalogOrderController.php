<?php
/**
 * CIG Catalog Order Admin Controller
 * 
 * Handles admin management of catalog orders
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;
use Symfony\Component\HttpFoundation\JsonResponse;

class AdminCatalogOrderController extends ModuleAdminController
{
    /** @var string */
    public $bootstrap = true;
    
    /** @var CatalogOrderService */
    private $orderService;
    
    /** @var CatalogRepository */
    private $catalogRepository;

    /**
     * Initialize controller
     */
    public function __construct()
    {
        $this->table = 'catalog_order';
        $this->className = 'CatalogOrder';
        $this->lang = false;
        $this->bootstrap = true;
        $this->context = Context::getContext();
        
        parent::__construct();
        
        $this->meta_title = $this->trans('Catalog Orders', [], 'Modules.Cigcatalog.Admin');
        
        // Initialize services
        $this->orderService = $this->module->get('cig_catalog.service.catalog_order');
        $this->catalogRepository = $this->module->get('cig_catalog.repository.catalog');
        
        // Configure list
        $this->configureList();
        
        // Configure bulk actions
        $this->configureBulkActions();
    }

    /**
     * Configure list display
     */
    private function configureList(): void
    {
        $this->fields_list = [
            'id_catalog_order' => [
                'title' => $this->trans('ID', [], 'Admin.Global'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
            ],
            'catalog_name' => [
                'title' => $this->trans('Catalog', [], 'Modules.Cigcatalog.Admin'),
                'callback' => 'getCatalogName',
            ],
            'company_name' => [
                'title' => $this->trans('Company', [], 'Modules.Cigcatalog.Admin'),
                'filter_key' => 'a!company_name',
            ],
            'contact_name' => [
                'title' => $this->trans('Contact', [], 'Modules.Cigcatalog.Admin'),
                'callback' => 'getContactName',
            ],
            'email' => [
                'title' => $this->trans('Email', [], 'Admin.Global'),
                'filter_key' => 'a!email',
            ],
            'phone' => [
                'title' => $this->trans('Phone', [], 'Admin.Global'),
                'filter_key' => 'a!phone',
            ],
            'status' => [
                'title' => $this->trans('Status', [], 'Admin.Global'),
                'type' => 'select',
                'list' => $this->getStatusOptions(),
                'filter_key' => 'a!status',
                'callback' => 'getStatusBadge',
            ],
            'date_add' => [
                'title' => $this->trans('Date', [], 'Admin.Global'),
                'align' => 'right',
                'type' => 'datetime',
                'filter_key' => 'a!date_add',
            ],
        ];
        
        // Default sorting
        $this->_defaultOrderBy = 'date_add';
        $this->_defaultOrderWay = 'DESC';
        
        // Actions
        $this->actions = ['view', 'edit', 'delete'];
        
        // List filters
        $this->_filter = 'AND a.`id_shop` = ' . (int) $this->context->shop->id;
    }

    /**
     * Configure bulk actions
     */
    private function configureBulkActions(): void
    {
        $this->bulk_actions = [
            'updateStatus' => [
                'text' => $this->trans('Update Status', [], 'Modules.Cigcatalog.Admin'),
                'icon' => 'icon-refresh',
                'confirm' => $this->trans('Update status for selected orders?', [], 'Modules.Cigcatalog.Admin'),
            ],
            'delete' => [
                'text' => $this->trans('Delete selected', [], 'Admin.Actions'),
                'icon' => 'icon-trash',
                'confirm' => $this->trans('Delete selected items?', [], 'Admin.Notifications.Warning'),
            ],
        ];
    }

    /**
     * Get status options for filter
     */
    private function getStatusOptions(): array
    {
        return [
            'pending' => $this->trans('Pending', [], 'Modules.Cigcatalog.Admin'),
            'processing' => $this->trans('Processing', [], 'Modules.Cigcatalog.Admin'),
            'shipped' => $this->trans('Shipped', [], 'Modules.Cigcatalog.Admin'),
            'delivered' => $this->trans('Delivered', [], 'Modules.Cigcatalog.Admin'),
            'cancelled' => $this->trans('Cancelled', [], 'Modules.Cigcatalog.Admin'),
        ];
    }

    /**
     * Render list with custom query
     */
    public function renderList()
    {
        // Custom query to join with catalog table
        $this->_select = 'cl.name as catalog_name';
        $this->_join = '
            LEFT JOIN `' . _DB_PREFIX_ . 'catalog` c ON (c.id_catalog = a.id_catalog)
            LEFT JOIN `' . _DB_PREFIX_ . 'catalog_lang` cl ON (cl.id_catalog = c.id_catalog AND cl.id_lang = ' . (int) $this->context->language->id . ')
        ';
        
        return parent::renderList();
    }

    /**
     * Render view form
     */
    public function renderView()
    {
        $orderId = (int) Tools::getValue('id_catalog_order');
        $order = $this->orderService->getOrderById($orderId);
        
        if (!$order) {
            $this->errors[] = $this->trans('Order not found', [], 'Modules.Cigcatalog.Admin');
            return $this->renderList();
        }
        
        $this->context->smarty->assign([
            'order' => $order,
            'status_options' => $this->getStatusOptions(),
            'update_status_url' => $this->context->link->getAdminLink('AdminCatalogOrder', true, [], ['action' => 'updateStatus', 'id_catalog_order' => $orderId]),
        ]);
        
        return $this->context->smarty->fetch($this->getTemplatePath() . 'order_view.tpl');
    }

    /**
     * Render edit form
     */
    public function renderForm()
    {
        $orderId = (int) Tools::getValue('id_catalog_order');
        $order = null;
        
        if ($orderId) {
            $order = $this->orderService->getOrderById($orderId);
            if (!$order) {
                $this->errors[] = $this->trans('Order not found', [], 'Modules.Cigcatalog.Admin');
                return $this->renderList();
            }
        }
        
        $this->fields_form = [
            'legend' => [
                'title' => $this->trans('Order Details', [], 'Modules.Cigcatalog.Admin'),
                'icon' => 'icon-shopping-cart',
            ],
            'input' => [
                [
                    'type' => 'select',
                    'label' => $this->trans('Status', [], 'Admin.Global'),
                    'name' => 'status',
                    'required' => true,
                    'options' => [
                        'query' => array_map(function($key, $value) {
                            return ['id' => $key, 'name' => $value];
                        }, array_keys($this->getStatusOptions()), $this->getStatusOptions()),
                        'id' => 'id',
                        'name' => 'name',
                    ],
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->trans('Admin Note', [], 'Modules.Cigcatalog.Admin'),
                    'name' => 'admin_note',
                    'rows' => 4,
                    'cols' => 60,
                ],
                [
                    'type' => 'switch',
                    'label' => $this->trans('Send Email Notification', [], 'Modules.Cigcatalog.Admin'),
                    'name' => 'send_notification',
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'send_notification_on',
                            'value' => 1,
                            'label' => $this->trans('Yes', [], 'Admin.Global'),
                        ],
                        [
                            'id' => 'send_notification_off',
                            'value' => 0,
                            'label' => $this->trans('No', [], 'Admin.Global'),
                        ],
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->trans('Save', [], 'Admin.Actions'),
                'class' => 'btn btn-default pull-right',
            ],
        ];
        
        if ($order) {
            $this->fields_value = [
                'status' => $order['status'],
                'admin_note' => $order['admin_note'] ?? '',
                'send_notification' => 1,
            ];
        }
        
        return parent::renderForm();
    }

    /**
     * Process form submission
     */
    public function postProcess()
    {
        if (Tools::isSubmit('submitAddcatalog_order')) {
            $orderId = (int) Tools::getValue('id_catalog_order');
            $status = Tools::getValue('status');
            $adminNote = Tools::getValue('admin_note');
            $sendNotification = (bool) Tools::getValue('send_notification');
            
            try {
                $this->orderService->updateOrderStatus($orderId, $status, $adminNote, $sendNotification);
                $this->confirmations[] = $this->trans('Order updated successfully', [], 'Modules.Cigcatalog.Admin');
            } catch (Exception $e) {
                $this->errors[] = $e->getMessage();
            }
        }
        
        return parent::postProcess();
    }

    /**
     * Handle bulk status update
     */
    public function processBulkUpdateStatus()
    {
        $orderIds = Tools::getValue('catalog_orderBox');
        $newStatus = Tools::getValue('bulk_status');
        
        if (empty($orderIds) || empty($newStatus)) {
            $this->errors[] = $this->trans('Please select orders and status', [], 'Modules.Cigcatalog.Admin');
            return;
        }
        
        $updated = 0;
        foreach ($orderIds as $orderId) {
            try {
                $this->orderService->updateOrderStatus((int) $orderId, $newStatus);
                $updated++;
            } catch (Exception $e) {
                $this->errors[] = sprintf('Error updating order %d: %s', $orderId, $e->getMessage());
            }
        }
        
        if ($updated > 0) {
            $this->confirmations[] = sprintf(
                $this->trans('%d orders updated successfully', [], 'Modules.Cigcatalog.Admin'),
                $updated
            );
        }
    }

    /**
     * AJAX update status
     */
    public function displayAjaxUpdateStatus()
    {
        try {
            $orderId = (int) Tools::getValue('id_catalog_order');
            $status = Tools::getValue('status');
            $adminNote = Tools::getValue('admin_note', '');
            $sendNotification = (bool) Tools::getValue('send_notification', false);
            
            $this->orderService->updateOrderStatus($orderId, $status, $adminNote, $sendNotification);
            
            return $this->ajaxResponse([
                'success' => true,
                'message' => $this->trans('Status updated successfully', [], 'Modules.Cigcatalog.Admin'),
            ]);
            
        } catch (Exception $e) {
            return $this->ajaxResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get catalog name callback
     */
    public function getCatalogName($catalogName, $row)
    {
        return $catalogName ?: $this->trans('Unknown catalog', [], 'Modules.Cigcatalog.Admin');
    }

    /**
     * Get contact name callback
     */
    public function getContactName($value, $row)
    {
        return $row['first_name'] . ' ' . $row['last_name'];
    }

    /**
     * Get status badge callback
     */
    public function getStatusBadge($status, $row)
    {
        $badges = [
            'pending' => 'warning',
            'processing' => 'info',
            'shipped' => 'primary',
            'delivered' => 'success',
            'cancelled' => 'danger',
        ];
        
        $badgeClass = $badges[$status] ?? 'default';
        $statusText = $this->getStatusOptions()[$status] ?? $status;
        
        return '<span class="badge badge-' . $badgeClass . '">' . $statusText . '</span>';
    }

    /**
     * Get template path
     */
    private function getTemplatePath(): string
    {
        return $this->module->getLocalPath() . 'views/templates/admin/order/';
    }

    /**
     * Create AJAX response
     */
    private function ajaxResponse(array $data, int $statusCode = 200): JsonResponse
    {
        $response = new JsonResponse($data, $statusCode);
        $response->headers->set('Content-Type', 'application/json');
        
        return $response;
    }
}
