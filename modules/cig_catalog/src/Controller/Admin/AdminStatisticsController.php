<?php
/**
 * Admin Statistics Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Admin;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use CigCatalog\Service\StatisticsService;

class AdminStatisticsController extends FrameworkBundleAdminController
{
    private StatisticsService $statisticsService;

    public function __construct(StatisticsService $statisticsService)
    {
        $this->statisticsService = $statisticsService;
    }

    /**
     * Statistics dashboard
     */
    public function indexAction(Request $request): Response
    {
        try {
            $stats = $this->statisticsService->getOverallStatistics();
            $chartData = $this->statisticsService->getChartData();

            return $this->render('@Modules/cig_catalog/templates/admin/statistics/index.html.twig', [
                'stats' => $stats,
                'chartData' => $chartData,
                'pageTitle' => 'Statistiky katalogů'
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Chyba při načítání statistik: ' . $e->getMessage());
            
            return $this->render('@Modules/cig_catalog/templates/admin/statistics/index.html.twig', [
                'stats' => [],
                'chartData' => [],
                'pageTitle' => 'Statistiky katalogů'
            ]);
        }
    }

    /**
     * AJAX data endpoint
     */
    public function dataAction(Request $request): JsonResponse
    {
        try {
            $type = $request->query->get('type', 'overview');
            $period = $request->query->get('period', '30');

            switch ($type) {
                case 'downloads':
                    $data = $this->statisticsService->getDownloadStatistics((int)$period);
                    break;
                case 'orders':
                    $data = $this->statisticsService->getOrderStatistics((int)$period);
                    break;
                case 'popular':
                    $data = $this->statisticsService->getPopularCatalogs((int)$period);
                    break;
                default:
                    $data = $this->statisticsService->getOverallStatistics();
            }

            return new JsonResponse([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při načítání dat: ' . $e->getMessage()
            ], 500);
        }
    }
}
