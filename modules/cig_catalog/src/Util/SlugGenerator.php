<?php
/**
 * Slug Generator Utility
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Util;

class SlugGenerator
{
    /**
     * Generate URL-friendly slug from text
     */
    public function generate(string $text, int $maxLength = 255): string
    {
        // Convert to lowercase
        $slug = mb_strtolower($text, 'UTF-8');
        
        // Replace Czech characters
        $slug = $this->replaceCzechCharacters($slug);
        
        // Replace spaces and special characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        
        // Remove leading and trailing hyphens
        $slug = trim($slug, '-');
        
        // Remove multiple consecutive hyphens
        $slug = preg_replace('/-+/', '-', $slug);
        
        // Truncate if too long
        if (strlen($slug) > $maxLength) {
            $slug = substr($slug, 0, $maxLength);
            $slug = rtrim($slug, '-');
        }
        
        return $slug ?: 'item';
    }

    /**
     * Replace Czech characters with ASCII equivalents
     */
    private function replaceCzechCharacters(string $text): string
    {
        $replacements = [
            'á' => 'a', 'à' => 'a', 'ä' => 'a', 'â' => 'a', 'ā' => 'a', 'ą' => 'a',
            'č' => 'c', 'ć' => 'c', 'ç' => 'c',
            'ď' => 'd', 'đ' => 'd',
            'é' => 'e', 'è' => 'e', 'ë' => 'e', 'ê' => 'e', 'ē' => 'e', 'ę' => 'e', 'ě' => 'e',
            'í' => 'i', 'ì' => 'i', 'ï' => 'i', 'î' => 'i', 'ī' => 'i', 'į' => 'i',
            'ľ' => 'l', 'ĺ' => 'l', 'ł' => 'l',
            'ň' => 'n', 'ń' => 'n', 'ñ' => 'n',
            'ó' => 'o', 'ò' => 'o', 'ö' => 'o', 'ô' => 'o', 'ō' => 'o', 'ő' => 'o',
            'ř' => 'r', 'ŕ' => 'r',
            'š' => 's', 'ś' => 's', 'ş' => 's',
            'ť' => 't', 'ţ' => 't',
            'ú' => 'u', 'ù' => 'u', 'ü' => 'u', 'û' => 'u', 'ū' => 'u', 'ů' => 'u', 'ű' => 'u',
            'ý' => 'y', 'ÿ' => 'y',
            'ž' => 'z', 'ź' => 'z', 'ż' => 'z'
        ];

        return strtr($text, $replacements);
    }

    /**
     * Generate unique slug by checking against existing slugs
     */
    public function generateUnique(string $text, callable $existsCallback, int $maxLength = 255): string
    {
        $baseSlug = $this->generate($text, $maxLength - 10); // Reserve space for suffix
        $slug = $baseSlug;
        $counter = 1;

        while ($existsCallback($slug)) {
            $suffix = '-' . $counter;
            $maxBaseLength = $maxLength - strlen($suffix);
            
            if (strlen($baseSlug) > $maxBaseLength) {
                $truncatedBase = substr($baseSlug, 0, $maxBaseLength);
                $truncatedBase = rtrim($truncatedBase, '-');
                $slug = $truncatedBase . $suffix;
            } else {
                $slug = $baseSlug . $suffix;
            }
            
            $counter++;
            
            // Prevent infinite loop
            if ($counter > 1000) {
                $slug = $baseSlug . '-' . uniqid();
                break;
            }
        }

        return $slug;
    }

    /**
     * Validate slug format
     */
    public function isValid(string $slug): bool
    {
        // Check if slug contains only allowed characters
        return preg_match('/^[a-z0-9\-]+$/', $slug) && 
               !preg_match('/^-|-$|--/', $slug) && 
               strlen($slug) > 0;
    }
}
