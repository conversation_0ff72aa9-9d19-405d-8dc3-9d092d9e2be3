<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Validator;

/**
 * Czech specific validators
 * 
 * Contains validation methods for Czech specific data like ICO, DIC, etc.
 */
class CzechValidator
{
    /**
     * Validate Czech ICO (company identification number)
     *
     * @param string $ico
     * @return bool
     */
    public static function validateICO($ico)
    {
        if (empty($ico)) {
            return true; // ICO is optional
        }

        // Remove spaces and non-numeric characters
        $ico = preg_replace('/[^0-9]/', '', $ico);

        // ICO must be 8 digits
        if (strlen($ico) !== 8) {
            return false;
        }

        // Calculate checksum using modulo 11 algorithm
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int) $ico[$i] * (8 - $i);
        }

        $remainder = $sum % 11;
        
        if ($remainder < 2) {
            $checksum = $remainder;
        } else {
            $checksum = 11 - $remainder;
        }

        return (int) $ico[7] === $checksum;
    }

    /**
     * Validate Czech DIC (tax identification number)
     *
     * @param string $dic
     * @return bool
     */
    public static function validateDIC($dic)
    {
        if (empty($dic)) {
            return true; // DIC is optional
        }

        // Remove spaces and convert to uppercase
        $dic = strtoupper(preg_replace('/\s+/', '', $dic));

        // DIC must start with CZ and be followed by 8-10 digits
        if (!preg_match('/^CZ[0-9]{8,10}$/', $dic)) {
            return false;
        }

        // Extract the numeric part
        $numericPart = substr($dic, 2);

        // If it's 8 digits, validate as ICO
        if (strlen($numericPart) === 8) {
            return self::validateICO($numericPart);
        }

        // For 9-10 digits, it's a personal tax number - basic format check is sufficient
        return true;
    }

    /**
     * Validate Czech postal code
     *
     * @param string $postalCode
     * @return bool
     */
    public static function validatePostalCode($postalCode)
    {
        if (empty($postalCode)) {
            return true; // Postal code is optional
        }

        // Remove spaces
        $postalCode = preg_replace('/\s+/', '', $postalCode);

        // Czech postal code format: 5 digits or 3 digits + space + 2 digits
        return preg_match('/^[0-9]{5}$/', $postalCode) || 
               preg_match('/^[0-9]{3}\s?[0-9]{2}$/', $postalCode);
    }

    /**
     * Validate Czech phone number
     *
     * @param string $phone
     * @return bool
     */
    public static function validatePhoneNumber($phone)
    {
        if (empty($phone)) {
            return true; // Phone is optional
        }

        // Remove spaces, dashes, and parentheses
        $phone = preg_replace('/[\s\-\(\)]/', '', $phone);

        // Czech phone number patterns
        $patterns = [
            '/^\+420[0-9]{9}$/',     // +420xxxxxxxxx
            '/^00420[0-9]{9}$/',     // 00420xxxxxxxxx
            '/^420[0-9]{9}$/',       // 420xxxxxxxxx
            '/^[0-9]{9}$/',          // xxxxxxxxx (domestic)
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $phone)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Format Czech ICO for display
     *
     * @param string $ico
     * @return string
     */
    public static function formatICO($ico)
    {
        if (empty($ico)) {
            return '';
        }

        $ico = preg_replace('/[^0-9]/', '', $ico);
        
        if (strlen($ico) === 8) {
            return substr($ico, 0, 2) . ' ' . substr($ico, 2, 3) . ' ' . substr($ico, 5, 3);
        }

        return $ico;
    }

    /**
     * Format Czech postal code for display
     *
     * @param string $postalCode
     * @return string
     */
    public static function formatPostalCode($postalCode)
    {
        if (empty($postalCode)) {
            return '';
        }

        $postalCode = preg_replace('/[^0-9]/', '', $postalCode);
        
        if (strlen($postalCode) === 5) {
            return substr($postalCode, 0, 3) . ' ' . substr($postalCode, 3, 2);
        }

        return $postalCode;
    }

    /**
     * Format Czech phone number for display
     *
     * @param string $phone
     * @return string
     */
    public static function formatPhoneNumber($phone)
    {
        if (empty($phone)) {
            return '';
        }

        // Remove all non-numeric characters except +
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        // Format based on pattern
        if (preg_match('/^\+420([0-9]{9})$/', $phone, $matches)) {
            $number = $matches[1];
            return '+420 ' . substr($number, 0, 3) . ' ' . substr($number, 3, 3) . ' ' . substr($number, 6, 3);
        } elseif (preg_match('/^([0-9]{9})$/', $phone, $matches)) {
            $number = $matches[1];
            return substr($number, 0, 3) . ' ' . substr($number, 3, 3) . ' ' . substr($number, 6, 3);
        }

        return $phone;
    }

    /**
     * Validate and format address
     *
     * @param array $addressData
     * @return array
     */
    public static function validateAndFormatAddress($addressData)
    {
        $errors = [];
        $formatted = [];

        // Validate required fields
        if (empty($addressData['street'])) {
            $errors[] = 'Street address is required';
        } else {
            $formatted['street'] = trim($addressData['street']);
        }

        if (empty($addressData['city'])) {
            $errors[] = 'City is required';
        } else {
            $formatted['city'] = trim($addressData['city']);
        }

        // Validate optional fields
        if (!empty($addressData['postal_code'])) {
            if (self::validatePostalCode($addressData['postal_code'])) {
                $formatted['postal_code'] = self::formatPostalCode($addressData['postal_code']);
            } else {
                $errors[] = 'Invalid postal code format';
            }
        }

        if (!empty($addressData['country'])) {
            $formatted['country'] = trim($addressData['country']);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'formatted' => $formatted
        ];
    }

    /**
     * Get list of Czech regions
     *
     * @return array
     */
    public static function getCzechRegions()
    {
        return [
            'Praha' => 'Hlavní město Praha',
            'Stredocesky' => 'Středočeský kraj',
            'Jihocesky' => 'Jihočeský kraj',
            'Plzensky' => 'Plzeňský kraj',
            'Karlovarsky' => 'Karlovarský kraj',
            'Ustecky' => 'Ústecký kraj',
            'Liberecky' => 'Liberecký kraj',
            'Kralovehradecky' => 'Královéhradecký kraj',
            'Pardubicky' => 'Pardubický kraj',
            'Vysocina' => 'Kraj Vysočina',
            'Jihomoravsky' => 'Jihomoravský kraj',
            'Olomoucky' => 'Olomoucký kraj',
            'Zlinsky' => 'Zlínský kraj',
            'Moravskoslezsky' => 'Moravskoslezský kraj'
        ];
    }
}
