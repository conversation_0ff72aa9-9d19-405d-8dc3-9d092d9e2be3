/**
 * Admin Catalog CSS
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

/* Základn<PERSON> styly pro admin rozhraní */
.content-div {
    padding: 20px;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.25rem;
    font-weight: 500;
}

.card-title .material-icons {
    vertical-align: middle;
    margin-right: 0.5rem;
}

/* Tabulka katal<PERSON>ů */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.table .img-thumbnail {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-success {
    background-color: #28a745;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-info {
    background-color: #17a2b8;
}

.badge-secondary {
    background-color: #6c757d;
}

/* Tlačítka */
.btn {
    border-radius: 0.25rem;
    font-weight: 500;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn .material-icons {
    font-size: 18px;
    vertical-align: middle;
}

/* Hromadné akce */
.bulk-actions {
    background-color: #e9ecef;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.bulk-actions .btn {
    margin-right: 0.5rem;
    transition: all 0.2s ease;
}

.bulk-actions .btn.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.bulk-actions .btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.selected-count {
    font-weight: 500;
    color: #495057;
}

/* Formuláře */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-input {
    margin-top: 0.3rem;
}

.form-check-label {
    margin-left: 0.25rem;
}

.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Jazykové záložky */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: #495057;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    padding: 1rem 0;
}

/* Stránkování */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
}

.page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Notifikace */
.admin-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
}

.alert {
    border-radius: 0.375rem;
    padding: 0.75rem 1.25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Responzivní design */
@media (max-width: 768px) {
    .content-div {
        padding: 10px;
    }
    
    .card-header .row {
        flex-direction: column;
    }
    
    .card-header .col-md-6:last-child {
        text-align: left !important;
        margin-top: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
        margin-right: 0;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .bulk-actions {
        text-align: center;
    }
    
    .bulk-actions .btn {
        margin-bottom: 0.5rem;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Drag and drop řazení */
.drag-handle {
    cursor: move;
    color: #6c757d;
    margin-right: 0.5rem;
    display: inline-flex;
    align-items: center;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.drag-handle:hover {
    color: #007bff;
    background-color: #f8f9fa;
}

.drag-handle .material-icons {
    font-size: 18px;
}

.sortable-placeholder {
    background-color: #e3f2fd;
    border: 2px dashed #2196f3;
    height: 60px;
    opacity: 0.6;
}

.sortable-placeholder-content {
    text-align: center;
    color: #2196f3;
    font-style: italic;
    padding: 1rem;
    line-height: 1.5;
}

.sortable-ghost {
    opacity: 0.4;
    background-color: #f0f8ff;
}

.sortable-chosen {
    background-color: #e3f2fd;
}

.sortable-drag {
    background-color: #ffffff;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.3);
    transform: rotate(2deg);
    z-index: 1000;
}

#sortable-catalogs tbody tr {
    transition: background-color 0.2s ease;
}

#sortable-catalogs tbody tr:hover {
    background-color: #f8f9fa;
}

#sortable-catalogs tbody tr.dragging {
    opacity: 0.5;
    background-color: #e3f2fd;
}

/* Loading overlay pro drag&drop */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.loading-spinner {
    background: white;
    padding: 1.25rem 1.875rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    font-size: 1rem;
    box-shadow: 0 0.25rem 1.25rem rgba(0,0,0,0.3);
}

.loading-spinner .material-icons {
    font-size: 1.5rem;
    color: #007bff;
}

.rotating {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* File upload preview */
.image-preview {
    margin-top: 0.5rem;
}

.image-preview img {
    max-width: 150px;
    max-height: 150px;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.current-image,
.current-file {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

/* Utility classes */
.text-muted {
    color: #6c757d !important;
}

.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

.mt-2 {
    margin-top: 0.5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

.mr-1 {
    margin-right: 0.25rem !important;
}

/* File Upload Styles */
.file-upload-container {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    position: relative;
}

.file-upload-container.drag-over {
    border-color: #007bff;
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.drop-zone {
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.drop-zone .material-icons {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.drop-zone-text {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.drop-zone-subtext {
    color: #adb5bd;
    font-size: 0.9rem;
}

.browse-btn {
    margin-top: 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.browse-btn:hover {
    background-color: #0056b3;
}

.upload-progress {
    margin-top: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.upload-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: white;
    transition: all 0.3s ease;
}

.upload-item.upload-success {
    border-color: #28a745;
    background-color: #d4edda;
}

.upload-item.upload-error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.upload-info {
    flex: 1;
    text-align: left;
    margin-right: 1rem;
}

.file-name {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

.upload-progress-bar {
    flex: 2;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 1rem;
}

.progress-fill {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.upload-success .progress-fill {
    background-color: #28a745;
}

.upload-error .progress-fill {
    background-color: #dc3545;
}

.upload-status {
    min-width: 80px;
    text-align: center;
    font-size: 0.875rem;
    color: #6c757d;
    margin-right: 0.5rem;
}

.upload-cancel {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.upload-cancel:hover {
    background-color: #f8d7da;
}

.upload-cancel .material-icons {
    font-size: 18px;
}

/* Upload notifications */
.upload-notification {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* File input hidden */
.file-upload-container input[type="file"] {
    position: absolute;
    left: -9999px;
    opacity: 0;
}

/* Upload controls */
.upload-controls {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.clear-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.clear-btn:hover {
    background-color: #545b62;
}

/* Responsive design for upload */
@media (max-width: 768px) {
    .file-upload-container {
        padding: 1rem;
    }

    .drop-zone {
        min-height: 120px;
    }

    .drop-zone .material-icons {
        font-size: 2rem;
    }

    .upload-item {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .upload-info {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .upload-progress-bar {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .upload-status {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}
