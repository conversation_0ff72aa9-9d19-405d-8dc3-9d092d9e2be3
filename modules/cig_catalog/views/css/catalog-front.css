/**
 * CIG Catalog Frontend Styles
 * 
 * Responsive styles for catalog frontend display
 */

/* ==========================================================================
   Main Layout
   ========================================================================== */

#catalog-page {
    margin-bottom: 2rem;
}

.page-description {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* ==========================================================================
   Search Form
   ========================================================================== */

.catalog-search {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.catalog-search .form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
}

.catalog-search .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.catalog-search .btn {
    border-radius: 0.375rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

/* ==========================================================================
   Results Info
   ========================================================================== */

.catalog-results-info {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.catalog-results-info .btn-outline-secondary {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* ==========================================================================
   Catalog Cards
   ========================================================================== */

.catalog-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
    height: 100%;
}

.catalog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: #0d6efd;
}

/* Card Image */
.catalog-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
}

.catalog-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.catalog-card:hover .catalog-image {
    transform: scale(1.05);
}

/* Badges */
.catalog-new-badge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.catalog-download-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Card Content */
.catalog-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    color: #212529;
}

.catalog-description {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.catalog-meta {
    font-size: 0.8rem;
    color: #6c757d;
}

.catalog-meta i {
    margin-right: 0.25rem;
}

/* Card Actions */
.catalog-actions {
    margin-top: auto;
}

.catalog-actions .btn {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.catalog-actions .btn-download {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.catalog-actions .btn-download:hover {
    background: #218838;
    border-color: #1e7e34;
    transform: translateY(-1px);
}

.catalog-actions .btn-order {
    border-color: #0d6efd;
    color: #0d6efd;
}

.catalog-actions .btn-order:hover {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
    transform: translateY(-1px);
}

/* Card Footer */
.card-footer .btn-link {
    font-size: 0.875rem;
    color: #6c757d;
}

.card-footer .btn-link:hover {
    color: #0d6efd;
}

.catalog-full-description {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
}

/* ==========================================================================
   Pagination
   ========================================================================== */

.pagination {
    margin-top: 2rem;
}

.pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
}

.pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* ==========================================================================
   Order Modal
   ========================================================================== */

#orderModal .modal-dialog {
    max-width: 600px;
}

#orderModal .modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

#orderModal .modal-title {
    color: #495057;
    font-weight: 600;
}

/* Form Sections */
.form-section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.form-section-title i {
    margin-right: 0.5rem;
    color: #0d6efd;
}

/* Form Fields */
.form-label.required {
    font-weight: 500;
}

.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* GDPR Consent */
.form-check {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Submit Button */
#submit-order-btn {
    min-width: 150px;
    position: relative;
}

#submit-order-btn .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

#loading-indicator {
    padding: 2rem;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Button Loading States */
.btn.loading {
    pointer-events: none;
    opacity: 0.6;
}

.btn.loading .btn-text {
    visibility: hidden;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .catalog-search {
        padding: 1rem;
    }
    
    .catalog-search .row {
        margin: 0;
    }
    
    .catalog-search .col-md-8,
    .catalog-search .col-md-4 {
        padding: 0;
        margin-bottom: 0.5rem;
    }
    
    .catalog-search .col-md-4 {
        margin-bottom: 0;
    }
    
    .catalog-results-info .row {
        text-align: center;
    }
    
    .catalog-results-info .col-md-6:last-child {
        margin-top: 0.5rem;
    }
    
    .catalog-image-container {
        height: 150px;
    }
    
    .catalog-title {
        font-size: 1.1rem;
    }
    
    .catalog-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
    
    #orderModal .modal-dialog {
        margin: 0.5rem;
        max-width: none;
    }
    
    .form-section-title {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .catalog-card {
        margin-bottom: 1rem;
    }
    
    .catalog-actions .row {
        margin: 0;
    }
    
    .catalog-actions .col-6 {
        padding: 0 0.25rem;
    }
    
    .pagination {
        font-size: 0.875rem;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
    }
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

.btn:focus,
.form-control:focus,
.form-check-input:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .catalog-search,
    .catalog-actions,
    .pagination,
    #orderModal {
        display: none !important;
    }
    
    .catalog-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .catalog-image {
        filter: grayscale(100%);
    }
}
