{**
 * Order View Template
 * 
 * Admin template for viewing order details
 *}

<div class="panel">
    <div class="panel-heading">
        <i class="icon-shopping-cart"></i>
        {l s='Order Details' mod='cig_catalog'} #{$order.id_catalog_order}
        
        <div class="panel-heading-action">
            <div class="btn-group">
                <a href="{$link->getAdminLink('AdminCatalogOrder')}" class="btn btn-default">
                    <i class="icon-arrow-left"></i> {l s='Back to list' mod='cig_catalog'}
                </a>
                <a href="{$link->getAdminLink('AdminCatalogOrder', true, [], ['action' => 'edit', 'id_catalog_order' => $order.id_catalog_order])}" class="btn btn-primary">
                    <i class="icon-edit"></i> {l s='Edit' mod='cig_catalog'}
                </a>
            </div>
        </div>
    </div>
    
    <div class="panel-body">
        
        <div class="row">
            
            {* Order Information *}
            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="icon-info-circle"></i> {l s='Order Information' mod='cig_catalog'}
                        </h3>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>{l s='Order ID:' mod='cig_catalog'}</dt>
                            <dd>#{$order.id_catalog_order}</dd>
                            
                            <dt>{l s='Date:' mod='cig_catalog'}</dt>
                            <dd>{$order.date_add|date_format:'%d.%m.%Y %H:%M'}</dd>
                            
                            <dt>{l s='Status:' mod='cig_catalog'}</dt>
                            <dd>
                                <span class="badge badge-{if $order.status == 'pending'}warning{elseif $order.status == 'processing'}info{elseif $order.status == 'shipped'}primary{elseif $order.status == 'delivered'}success{elseif $order.status == 'cancelled'}danger{else}default{/if}">
                                    {$status_options[$order.status]|default:$order.status}
                                </span>
                            </dd>
                            
                            <dt>{l s='IP Address:' mod='cig_catalog'}</dt>
                            <dd>{$order.ip_address|escape:'html':'UTF-8'}</dd>
                            
                            <dt>{l s='User Agent:' mod='cig_catalog'}</dt>
                            <dd class="text-muted small">{$order.user_agent|escape:'html':'UTF-8'|truncate:100}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            
            {* Catalog Information *}
            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="icon-book"></i> {l s='Requested Catalog' mod='cig_catalog'}
                        </h3>
                    </div>
                    <div class="panel-body">
                        {if $order.catalog_name}
                            <h4>{$order.catalog_name|escape:'html':'UTF-8'}</h4>
                            {if $order.catalog_image}
                                <img src="{$module_dir}uploads/images/{$order.catalog_image}" 
                                     alt="{$order.catalog_name|escape:'html':'UTF-8'}" 
                                     class="img-thumbnail" 
                                     style="max-width: 200px; max-height: 150px;">
                            {/if}
                            <p class="text-muted">
                                <a href="{$link->getAdminLink('AdminCatalog', true, [], ['action' => 'edit', 'id_catalog' => $order.id_catalog])}" 
                                   class="btn btn-sm btn-default">
                                    <i class="icon-edit"></i> {l s='Edit Catalog' mod='cig_catalog'}
                                </a>
                            </p>
                        {else}
                            <p class="text-danger">{l s='Catalog not found or deleted' mod='cig_catalog'}</p>
                        {/if}
                    </div>
                </div>
            </div>
            
        </div>
        
        <div class="row">
            
            {* Company Information *}
            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="icon-building"></i> {l s='Company Information' mod='cig_catalog'}
                        </h3>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>{l s='Company Name:' mod='cig_catalog'}</dt>
                            <dd><strong>{$order.company_name|escape:'html':'UTF-8'}</strong></dd>
                            
                            {if $order.company_id}
                            <dt>{l s='Company ID (IČO):' mod='cig_catalog'}</dt>
                            <dd>{$order.company_id|escape:'html':'UTF-8'}</dd>
                            {/if}
                            
                            <dt>{l s='Contact Person:' mod='cig_catalog'}</dt>
                            <dd>{$order.first_name|escape:'html':'UTF-8'} {$order.last_name|escape:'html':'UTF-8'}</dd>
                            
                            <dt>{l s='Email:' mod='cig_catalog'}</dt>
                            <dd>
                                <a href="mailto:{$order.email|escape:'html':'UTF-8'}">{$order.email|escape:'html':'UTF-8'}</a>
                            </dd>
                            
                            {if $order.phone}
                            <dt>{l s='Phone:' mod='cig_catalog'}</dt>
                            <dd>
                                <a href="tel:{$order.phone|escape:'html':'UTF-8'}">{$order.phone|escape:'html':'UTF-8'}</a>
                            </dd>
                            {/if}
                        </dl>
                    </div>
                </div>
            </div>
            
            {* Delivery Information *}
            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="icon-truck"></i> {l s='Delivery Information' mod='cig_catalog'}
                        </h3>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>{l s='Address:' mod='cig_catalog'}</dt>
                            <dd>{$order.address|escape:'html':'UTF-8'|nl2br}</dd>
                            
                            {if $order.note}
                            <dt>{l s='Customer Note:' mod='cig_catalog'}</dt>
                            <dd class="well well-sm">{$order.note|escape:'html':'UTF-8'|nl2br}</dd>
                            {/if}
                            
                            <dt>{l s='GDPR Consent:' mod='cig_catalog'}</dt>
                            <dd>
                                {if $order.gdpr_consent}
                                    <span class="label label-success">{l s='Given' mod='cig_catalog'}</span>
                                {else}
                                    <span class="label label-danger">{l s='Not given' mod='cig_catalog'}</span>
                                {/if}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            
        </div>
        
        {* Admin Notes *}
        {if $order.admin_note}
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="icon-comment"></i> {l s='Admin Notes' mod='cig_catalog'}
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="well">
                            {$order.admin_note|escape:'html':'UTF-8'|nl2br}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {/if}
        
        {* Quick Status Update *}
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="icon-refresh"></i> {l s='Quick Status Update' mod='cig_catalog'}
                        </h3>
                    </div>
                    <div class="panel-body">
                        <form id="quick-status-form" class="form-inline">
                            <input type="hidden" name="id_catalog_order" value="{$order.id_catalog_order}">
                            
                            <div class="form-group">
                                <label for="status">{l s='Status:' mod='cig_catalog'}</label>
                                <select name="status" id="status" class="form-control">
                                    {foreach from=$status_options key=status_key item=status_name}
                                        <option value="{$status_key}" {if $order.status == $status_key}selected{/if}>
                                            {$status_name}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="admin_note">{l s='Note:' mod='cig_catalog'}</label>
                                <input type="text" name="admin_note" id="admin_note" class="form-control" 
                                       placeholder="{l s='Optional note...' mod='cig_catalog'}" style="width: 200px;">
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-inline">
                                    <input type="checkbox" name="send_notification" value="1" checked>
                                    {l s='Send email notification' mod='cig_catalog'}
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-refresh"></i> {l s='Update Status' mod='cig_catalog'}
                            </button>
                        </form>
                        
                        <div id="status-update-result" class="alert" style="display: none; margin-top: 15px;"></div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    
    // Quick status update
    $('#quick-status-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var button = form.find('button[type="submit"]');
        var resultDiv = $('#status-update-result');
        
        // Show loading state
        button.prop('disabled', true).html('<i class="icon-spinner icon-spin"></i> {l s='Updating...' mod='cig_catalog' js=1}');
        
        $.ajax({
            url: '{$update_status_url}',
            type: 'POST',
            data: form.serialize() + '&ajax=1',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    resultDiv.removeClass('alert-danger')
                             .addClass('alert-success')
                             .html('<i class="icon-check"></i> ' + response.message)
                             .show();
                    
                    // Reload page after 2 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    resultDiv.removeClass('alert-success')
                             .addClass('alert-danger')
                             .html('<i class="icon-warning"></i> ' + (response.error || '{l s='Update failed' mod='cig_catalog' js=1}'))
                             .show();
                }
            },
            error: function() {
                resultDiv.removeClass('alert-success')
                         .addClass('alert-danger')
                         .html('<i class="icon-warning"></i> {l s='An error occurred while updating status' mod='cig_catalog' js=1}')
                         .show();
            },
            complete: function() {
                button.prop('disabled', false).html('<i class="icon-refresh"></i> {l s='Update Status' mod='cig_catalog' js=1}');
            }
        });
    });
    
});
</script>
