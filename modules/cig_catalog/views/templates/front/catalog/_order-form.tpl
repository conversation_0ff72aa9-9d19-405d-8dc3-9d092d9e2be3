{**
 * Catalog Order Form Template
 * 
 * Modal form for ordering catalogs
 *}

<form id="catalog-order-form" novalidate>
    
    {* Hidden fields *}
    <input type="hidden" id="catalog_id" name="catalog_id" value="">
    <input type="hidden" name="token" value="{Tools::getToken(false)}">
    
    {* Selected catalog info *}
    <div id="selected-catalog-info" class="alert alert-info mb-4" style="display: none;">
        <h6 class="alert-heading">
            <i class="fas fa-book"></i> {l s='Selected catalog' mod='cig_catalog'}:
        </h6>
        <span id="selected-catalog-name" class="fw-bold"></span>
    </div>
    
    {* Company information *}
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="form-section-title">
                <i class="fas fa-building"></i> {l s='Company Information' mod='cig_catalog'}
            </h6>
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-md-8">
            <label for="company_name" class="form-label required">
                {l s='Company Name' mod='cig_catalog'} <span class="text-danger">*</span>
            </label>
            <input type="text" 
                   class="form-control" 
                   id="company_name" 
                   name="company_name" 
                   required
                   maxlength="100">
            <div class="invalid-feedback"></div>
        </div>
        <div class="col-md-4">
            <label for="company_id" class="form-label">
                {l s='Company ID (IČO)' mod='cig_catalog'}
            </label>
            <input type="text" 
                   class="form-control" 
                   id="company_id" 
                   name="company_id" 
                   maxlength="20"
                   placeholder="12345678">
            <div class="invalid-feedback"></div>
            <small class="form-text text-muted">{l s='Optional, but recommended for Czech companies' mod='cig_catalog'}</small>
        </div>
    </div>
    
    {* Contact person *}
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="form-section-title">
                <i class="fas fa-user"></i> {l s='Contact Person' mod='cig_catalog'}
            </h6>
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-md-6">
            <label for="first_name" class="form-label required">
                {l s='First Name' mod='cig_catalog'} <span class="text-danger">*</span>
            </label>
            <input type="text" 
                   class="form-control" 
                   id="first_name" 
                   name="first_name" 
                   required
                   maxlength="50">
            <div class="invalid-feedback"></div>
        </div>
        <div class="col-md-6">
            <label for="last_name" class="form-label required">
                {l s='Last Name' mod='cig_catalog'} <span class="text-danger">*</span>
            </label>
            <input type="text" 
                   class="form-control" 
                   id="last_name" 
                   name="last_name" 
                   required
                   maxlength="50">
            <div class="invalid-feedback"></div>
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-md-6">
            <label for="email" class="form-label required">
                {l s='Email Address' mod='cig_catalog'} <span class="text-danger">*</span>
            </label>
            <input type="email" 
                   class="form-control" 
                   id="email" 
                   name="email" 
                   required
                   maxlength="100">
            <div class="invalid-feedback"></div>
        </div>
        <div class="col-md-6">
            <label for="phone" class="form-label">
                {l s='Phone Number' mod='cig_catalog'}
            </label>
            <input type="tel" 
                   class="form-control" 
                   id="phone" 
                   name="phone" 
                   maxlength="20"
                   placeholder="+420 123 456 789">
            <div class="invalid-feedback"></div>
        </div>
    </div>
    
    {* Address *}
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="form-section-title">
                <i class="fas fa-map-marker-alt"></i> {l s='Delivery Address' mod='cig_catalog'}
            </h6>
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-12">
            <label for="address" class="form-label required">
                {l s='Complete Address' mod='cig_catalog'} <span class="text-danger">*</span>
            </label>
            <textarea class="form-control" 
                      id="address" 
                      name="address" 
                      rows="3" 
                      required
                      maxlength="500"
                      placeholder="{l s='Street, City, Postal Code, Country' mod='cig_catalog'}"></textarea>
            <div class="invalid-feedback"></div>
        </div>
    </div>
    
    {* Additional note *}
    <div class="row mb-3">
        <div class="col-12">
            <label for="note" class="form-label">
                {l s='Additional Note' mod='cig_catalog'}
            </label>
            <textarea class="form-control" 
                      id="note" 
                      name="note" 
                      rows="2" 
                      maxlength="500"
                      placeholder="{l s='Any additional information or special requests...' mod='cig_catalog'}"></textarea>
            <div class="invalid-feedback"></div>
        </div>
    </div>
    
    {* GDPR Consent *}
    <div class="row mb-4">
        <div class="col-12">
            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       id="gdpr_consent" 
                       name="gdpr_consent" 
                       required>
                <label class="form-check-label required" for="gdpr_consent">
                    {l s='I agree to the processing of my personal data for the purpose of processing this order.' mod='cig_catalog'} 
                    <span class="text-danger">*</span>
                </label>
                <div class="invalid-feedback"></div>
                <small class="form-text text-muted">
                    {l s='Your data will be used only for processing your catalog order and will not be shared with third parties.' mod='cig_catalog'}
                </small>
            </div>
        </div>
    </div>
    
    {* Form actions *}
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> {l s='Cancel' mod='cig_catalog'}
                </button>
                <button type="submit" class="btn btn-primary" id="submit-order-btn">
                    <span class="btn-text">
                        <i class="fas fa-paper-plane"></i> {l s='Submit Order' mod='cig_catalog'}
                    </span>
                    <span class="btn-loading" style="display: none;">
                        <span class="spinner-border spinner-border-sm" role="status"></span>
                        {l s='Submitting...' mod='cig_catalog'}
                    </span>
                </button>
            </div>
        </div>
    </div>
    
    {* Form messages *}
    <div id="form-messages" class="mt-3" style="display: none;">
        <div class="alert" role="alert"></div>
    </div>
    
</form>

{* Form validation messages *}
<script type="text/javascript">
    var orderFormValidation = {
        messages: {
            required: '{l s='This field is required' mod='cig_catalog' js=1}',
            email: '{l s='Please enter a valid email address' mod='cig_catalog' js=1}',
            minLength: '{l s='This field is too short' mod='cig_catalog' js=1}',
            maxLength: '{l s='This field is too long' mod='cig_catalog' js=1}',
            companyId: '{l s='Please enter a valid company ID (IČO)' mod='cig_catalog' js=1}',
            phone: '{l s='Please enter a valid phone number' mod='cig_catalog' js=1}'
        }
    };
</script>
