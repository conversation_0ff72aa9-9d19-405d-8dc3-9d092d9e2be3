{**
 * Catalog Card Template
 * 
 * Template for individual catalog card display
 *}

<div class="catalog-card card h-100" data-catalog-id="{$catalog.id_catalog}">
    
    {* Card image *}
    <div class="catalog-image-container">
        <img src="{$catalog.image_url}" 
             alt="{$catalog.name|escape:'html':'UTF-8'}" 
             class="card-img-top catalog-image"
             loading="lazy"
             onerror="this.src='{$module_dir}views/img/no-image.png'">
        
        {* New badge *}
        {if $catalog.is_new}
            <span class="badge bg-success catalog-new-badge">
                <i class="fas fa-star"></i> {l s='New' mod='cig_catalog'}
            </span>
        {/if}
        
        {* Download count *}
        {if $catalog.download_count > 0}
            <span class="badge bg-info catalog-download-badge">
                <i class="fas fa-download"></i> {$catalog.download_count}
            </span>
        {/if}
    </div>

    {* Card body *}
    <div class="card-body d-flex flex-column">
        
        {* Title *}
        <h5 class="card-title catalog-title">
            {$catalog.name|escape:'html':'UTF-8'}
        </h5>
        
        {* Short description *}
        {if $catalog.short_description}
            <p class="card-text catalog-description text-muted">
                {$catalog.short_description|truncate:120:'...'|escape:'html':'UTF-8'}
            </p>
        {/if}
        
        {* Meta info *}
        <div class="catalog-meta mb-3">
            <small class="text-muted">
                <i class="fas fa-calendar-alt"></i> 
                {l s='Added' mod='cig_catalog'}: {$catalog.formatted_date}
            </small>
        </div>
        
        {* Actions *}
        <div class="catalog-actions mt-auto">
            <div class="row g-2">
                
                {* Download button *}
                {if $catalog.catalog_file}
                    <div class="col-6">
                        <button type="button" 
                                class="btn btn-primary btn-download w-100" 
                                data-catalog-id="{$catalog.id_catalog}"
                                data-catalog-name="{$catalog.name|escape:'html':'UTF-8'}">
                            <i class="fas fa-download"></i> 
                            <span class="d-none d-sm-inline">{l s='Download' mod='cig_catalog'}</span>
                        </button>
                    </div>
                {/if}
                
                {* Order button *}
                {if $enable_ordering}
                    <div class="col-{if $catalog.catalog_file}6{else}12{/if}">
                        <button type="button" 
                                class="btn btn-outline-primary btn-order w-100" 
                                data-catalog-id="{$catalog.id_catalog}"
                                data-catalog-name="{$catalog.name|escape:'html':'UTF-8'}"
                                data-bs-toggle="modal" 
                                data-bs-target="#orderModal">
                            <i class="fas fa-shopping-cart"></i> 
                            <span class="d-none d-sm-inline">{l s='Order' mod='cig_catalog'}</span>
                        </button>
                    </div>
                {/if}
                
                {* View details button (if no download available) *}
                {if !$catalog.catalog_file && !$enable_ordering}
                    <div class="col-12">
                        <button type="button" 
                                class="btn btn-outline-secondary btn-details w-100" 
                                data-catalog-id="{$catalog.id_catalog}">
                            <i class="fas fa-info-circle"></i> {l s='View Details' mod='cig_catalog'}
                        </button>
                    </div>
                {/if}
                
            </div>
        </div>
        
    </div>
    
    {* Card footer with additional info *}
    {if $catalog.description}
        <div class="card-footer bg-transparent">
            <button type="button" 
                    class="btn btn-link btn-sm p-0 text-decoration-none" 
                    data-bs-toggle="collapse" 
                    data-bs-target="#description-{$catalog.id_catalog}" 
                    aria-expanded="false">
                <i class="fas fa-chevron-down"></i> {l s='Show description' mod='cig_catalog'}
            </button>
            
            <div class="collapse mt-2" id="description-{$catalog.id_catalog}">
                <div class="catalog-full-description">
                    {$catalog.description|nl2br|escape:'html':'UTF-8'}
                </div>
            </div>
        </div>
    {/if}
    
</div>

{* Catalog details modal (optional) *}
<div class="modal fade" id="detailsModal-{$catalog.id_catalog}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-book"></i> {$catalog.name|escape:'html':'UTF-8'}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <img src="{$catalog.image_url}" 
                             alt="{$catalog.name|escape:'html':'UTF-8'}" 
                             class="img-fluid rounded">
                    </div>
                    <div class="col-md-8">
                        {if $catalog.description}
                            <div class="catalog-description">
                                {$catalog.description|nl2br|escape:'html':'UTF-8'}
                            </div>
                        {/if}
                        
                        <div class="catalog-info mt-3">
                            <ul class="list-unstyled">
                                <li><strong>{l s='Added' mod='cig_catalog'}:</strong> {$catalog.formatted_date}</li>
                                {if $catalog.download_count > 0}
                                    <li><strong>{l s='Downloads' mod='cig_catalog'}:</strong> {$catalog.download_count}</li>
                                {/if}
                                {if $catalog.is_new}
                                    <li><span class="badge bg-success">{l s='New' mod='cig_catalog'}</span></li>
                                {/if}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                {if $catalog.catalog_file}
                    <button type="button" 
                            class="btn btn-primary btn-download" 
                            data-catalog-id="{$catalog.id_catalog}">
                        <i class="fas fa-download"></i> {l s='Download' mod='cig_catalog'}
                    </button>
                {/if}
                {if $enable_ordering}
                    <button type="button" 
                            class="btn btn-outline-primary btn-order" 
                            data-catalog-id="{$catalog.id_catalog}"
                            data-bs-dismiss="modal"
                            data-bs-toggle="modal" 
                            data-bs-target="#orderModal">
                        <i class="fas fa-shopping-cart"></i> {l s='Order' mod='cig_catalog'}
                    </button>
                {/if}
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {l s='Close' mod='cig_catalog'}
                </button>
            </div>
        </div>
    </div>
</div>
