{**
 * Order Success Template
 * 
 * Template for successful order confirmation
 *}

{extends file='page.tpl'}

{block name='page_title'}
    <h1 class="h1 text-success">
        <i class="fas fa-check-circle"></i> {$page_title}
    </h1>
{/block}

{block name='page_content_container'}
    <section id="order-success-page" class="page-content card card-block">
        <div class="card-block text-center">
            
            {* Success Icon *}
            <div class="success-icon mb-4">
                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
            </div>
            
            {* Success Message *}
            <div class="success-message mb-4">
                <h2 class="h3 text-success mb-3">{$success_message}</h2>
                <p class="lead text-muted">
                    {l s='We have received your catalog order and will process it shortly.' mod='cig_catalog'}
                </p>
            </div>
            
            {* Order Details *}
            {if $order}
                <div class="order-details card bg-light mb-4">
                    <div class="card-header">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> {l s='Order Details' mod='cig_catalog'}
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-5">{l s='Order ID:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">#{$order.id_catalog_order}</dd>
                                    
                                    <dt class="col-sm-5">{l s='Catalog:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">{$order.catalog_name|escape:'html':'UTF-8'}</dd>
                                    
                                    <dt class="col-sm-5">{l s='Company:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">{$order.company_name|escape:'html':'UTF-8'}</dd>
                                    
                                    <dt class="col-sm-5">{l s='Contact:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">{$order.first_name|escape:'html':'UTF-8'} {$order.last_name|escape:'html':'UTF-8'}</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-5">{l s='Email:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">{$order.email|escape:'html':'UTF-8'}</dd>
                                    
                                    <dt class="col-sm-5">{l s='Date:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">{$order.date_add|date_format:'%d.%m.%Y %H:%M'}</dd>
                                    
                                    <dt class="col-sm-5">{l s='Status:' mod='cig_catalog'}</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-info">{l s='Processing' mod='cig_catalog'}</span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
            
            {* Next Steps *}
            <div class="next-steps mb-4">
                <h3 class="h4 mb-3">{l s='What happens next?' mod='cig_catalog'}</h3>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="step-item">
                            <div class="step-icon mb-2">
                                <i class="fas fa-envelope text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <h5 class="step-title">{l s='Email Confirmation' mod='cig_catalog'}</h5>
                            <p class="step-description text-muted">
                                {l s='You will receive a confirmation email with order details.' mod='cig_catalog'}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="step-item">
                            <div class="step-icon mb-2">
                                <i class="fas fa-cogs text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <h5 class="step-title">{l s='Processing' mod='cig_catalog'}</h5>
                            <p class="step-description text-muted">
                                {l s='Our team will prepare your catalog for shipment.' mod='cig_catalog'}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="step-item">
                            <div class="step-icon mb-2">
                                <i class="fas fa-shipping-fast text-success" style="font-size: 2rem;"></i>
                            </div>
                            <h5 class="step-title">{l s='Delivery' mod='cig_catalog'}</h5>
                            <p class="step-description text-muted">
                                {l s='Your catalog will be delivered to the specified address.' mod='cig_catalog'}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            {* Important Information *}
            <div class="alert alert-info mb-4">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle"></i> {l s='Important Information' mod='cig_catalog'}
                </h5>
                <ul class="mb-0 text-start">
                    <li>{l s='Delivery is completely free of charge' mod='cig_catalog'}</li>
                    <li>{l s='Standard delivery time is 3-5 business days' mod='cig_catalog'}</li>
                    <li>{l s='You will receive a tracking number once shipped' mod='cig_catalog'}</li>
                    <li>{l s='For urgent requests, please contact our customer service' mod='cig_catalog'}</li>
                </ul>
            </div>
            
            {* Action Buttons *}
            <div class="action-buttons">
                <a href="{$link->getModuleLink('cig_catalog', 'catalog')}" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-book"></i> {l s='Browse More Catalogs' mod='cig_catalog'}
                </a>
                <a href="{$link->getPageLink('index')}" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-home"></i> {l s='Back to Homepage' mod='cig_catalog'}
                </a>
            </div>
            
            {* Contact Information *}
            <div class="contact-info mt-5 pt-4 border-top">
                <h4 class="h5 mb-3">{l s='Need Help?' mod='cig_catalog'}</h4>
                <p class="text-muted">
                    {l s='If you have any questions about your order, please don\'t hesitate to contact us:' mod='cig_catalog'}
                </p>
                <div class="contact-details">
                    <p class="mb-1">
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:{Configuration::get('PS_SHOP_EMAIL')}">{Configuration::get('PS_SHOP_EMAIL')}</a>
                    </p>
                    <p class="mb-1">
                        <i class="fas fa-phone me-2"></i>
                        <a href="tel:{Configuration::get('PS_SHOP_PHONE')}">{Configuration::get('PS_SHOP_PHONE')}</a>
                    </p>
                </div>
            </div>
            
        </div>
    </section>
{/block}

{block name='page_footer'}
    {* Auto-redirect after 30 seconds (optional) *}
    <script type="text/javascript">
        // Optional: Auto-redirect to catalog page after 30 seconds
        // setTimeout(function() {
        //     window.location.href = '{$link->getModuleLink('cig_catalog', 'catalog')}';
        // }, 30000);
    </script>
{/block}

{block name='stylesheets'}
    {$parent_stylesheets}
    <style>
        .success-icon {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .step-item {
            text-align: center;
            padding: 1rem;
        }
        
        .step-icon {
            transition: transform 0.3s ease;
        }
        
        .step-item:hover .step-icon {
            transform: scale(1.1);
        }
        
        .contact-details a {
            color: inherit;
            text-decoration: none;
        }
        
        .contact-details a:hover {
            color: #0d6efd;
            text-decoration: underline;
        }
    </style>
{/block}
