/**
 * Admin Catalog JavaScript
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize drag & drop sorting
    initializeSortable();

    // Hromadn<PERSON> v<PERSON>ěr
    const selectAllCheckbox = document.getElementById('select-all');
    const catalogCheckboxes = document.querySelectorAll('.catalog-checkbox');
    const bulkActions = document.querySelector('.bulk-actions');
    const selectedCount = document.querySelector('.selected-count');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            catalogCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    catalogCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.catalog-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = count + ' vybraných';
        } else {
            bulkActions.style.display = 'none';
        }

        // Aktualizace select-all checkbox
        if (selectAllCheckbox) {
            selectAllCheckbox.indeterminate = count > 0 && count < catalogCheckboxes.length;
            selectAllCheckbox.checked = count === catalogCheckboxes.length;
        }
    }

    // Hromadné akce
    document.querySelectorAll('.bulk-action').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const checkedBoxes = document.querySelectorAll('.catalog-checkbox:checked');
            const catalogIds = Array.from(checkedBoxes).map(cb => cb.value);

            if (catalogIds.length === 0) {
                alert('Vyberte alespoň jeden katalog.');
                return;
            }

            let confirmMessage = '';
            switch (action) {
                case 'activate':
                    confirmMessage = `Opravdu chcete aktivovat ${catalogIds.length} katalogů?`;
                    break;
                case 'deactivate':
                    confirmMessage = `Opravdu chcete deaktivovat ${catalogIds.length} katalogů?`;
                    break;
                case 'delete':
                    confirmMessage = `Opravdu chcete smazat ${catalogIds.length} katalogů? Tato akce je nevratná!`;
                    break;
            }

            if (confirm(confirmMessage)) {
                performBulkAction(action, catalogIds);
            }
        });
    });

    function performBulkAction(action, catalogIds) {
        const token = document.getElementById('bulk-token').value;

        // Disable bulk action buttons during processing
        const bulkButtons = document.querySelectorAll('.bulk-action');
        bulkButtons.forEach(btn => {
            btn.disabled = true;
            btn.classList.add('loading');
        });

        showLoadingOverlay();

        fetch(window.location.origin + '/admin/catalog/bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                action: action,
                catalog_ids: catalogIds,
                _token: token
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');

                // Update UI based on action instead of full reload
                if (action === 'delete') {
                    // Remove deleted rows from table
                    catalogIds.forEach(id => {
                        const row = document.querySelector(`tr[data-catalog-id="${id}"]`);
                        if (row) {
                            row.style.transition = 'opacity 0.3s';
                            row.style.opacity = '0';
                            setTimeout(() => {
                                row.remove();
                                updateBulkActions();
                            }, 300);
                        }
                    });
                } else if (action === 'activate' || action === 'deactivate') {
                    // Update status badges
                    catalogIds.forEach(id => {
                        const row = document.querySelector(`tr[data-catalog-id="${id}"]`);
                        if (row) {
                            const statusBadge = row.querySelector('.badge');
                            const toggleBtn = row.querySelector('.toggle-active');
                            const icon = toggleBtn ? toggleBtn.querySelector('.material-icons') : null;

                            if (action === 'activate') {
                                statusBadge.className = 'badge badge-success';
                                statusBadge.textContent = 'Aktivní';
                                if (icon) {
                                    icon.textContent = 'visibility_off';
                                    toggleBtn.title = 'Deaktivovat';
                                }
                            } else {
                                statusBadge.className = 'badge badge-danger';
                                statusBadge.textContent = 'Neaktivní';
                                if (icon) {
                                    icon.textContent = 'visibility';
                                    toggleBtn.title = 'Aktivovat';
                                }
                            }
                        }
                    });
                }

                // Clear selection
                clearBulkSelection();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Došlo k chybě při zpracování požadavku.', 'error');
        })
        .finally(() => {
            // Re-enable bulk action buttons
            bulkButtons.forEach(btn => {
                btn.disabled = false;
                btn.classList.remove('loading');
            });
            hideLoadingOverlay();
        });
    }

    /**
     * Clear bulk selection
     */
    function clearBulkSelection() {
        // Uncheck all checkboxes
        const checkboxes = document.querySelectorAll('.catalog-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Uncheck select all
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }

        // Hide bulk actions
        updateBulkActions();
    }

    // Toggle aktivní stav
    document.querySelectorAll('.toggle-active').forEach(button => {
        button.addEventListener('click', function() {
            const catalogId = this.dataset.id;
            const token = document.getElementById('toggle-token-' + catalogId).value;
            
            fetch(window.location.origin + '/admin/catalog/' + catalogId + '/toggle-active', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({
                    _token: token
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    
                    // Aktualizace UI
                    const row = this.closest('tr');
                    const statusBadge = row.querySelector('.badge');
                    const icon = this.querySelector('.material-icons');
                    
                    if (data.active) {
                        statusBadge.className = 'badge badge-success';
                        statusBadge.textContent = 'Aktivní';
                        icon.textContent = 'visibility_off';
                        this.title = 'Deaktivovat';
                    } else {
                        statusBadge.className = 'badge badge-danger';
                        statusBadge.textContent = 'Neaktivní';
                        icon.textContent = 'visibility';
                        this.title = 'Aktivovat';
                    }
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Došlo k chybě při změně stavu.', 'error');
            });
        });
    });

    // Smazání katalogu
    document.querySelectorAll('.delete-catalog').forEach(button => {
        button.addEventListener('click', function() {
            const catalogId = this.dataset.id;
            const row = this.closest('tr');
            const catalogName = row.querySelector('strong').textContent;
            
            if (confirm(`Opravdu chcete smazat katalog "${catalogName}"? Tato akce je nevratná!`)) {
                const token = document.getElementById('delete-token-' + catalogId).value;
                
                fetch(window.location.origin + '/admin/catalog/' + catalogId + '/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        _token: token
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message, 'success');
                        row.style.transition = 'opacity 0.3s';
                        row.style.opacity = '0';
                        setTimeout(() => {
                            row.remove();
                            updateBulkActions();
                        }, 300);
                    } else {
                        showNotification(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Došlo k chybě při mazání katalogu.', 'error');
                });
            }
        });
    });

    /**
     * Initialize sortable functionality for catalog list
     */
    function initializeSortable() {
        const sortableTable = document.getElementById('sortable-catalogs');
        if (!sortableTable) {
            return;
        }

        // Add drag handles to each row
        const rows = sortableTable.querySelectorAll('tbody tr');
        rows.forEach(row => {
            if (!row.querySelector('.drag-handle')) {
                const firstCell = row.querySelector('td:first-child');
                if (firstCell) {
                    const dragHandle = document.createElement('span');
                    dragHandle.className = 'drag-handle';
                    dragHandle.innerHTML = '<i class="material-icons">drag_indicator</i>';
                    dragHandle.title = 'Přetáhnout pro změnu pořadí';
                    dragHandle.style.cursor = 'move';
                    dragHandle.style.marginRight = '10px';
                    firstCell.insertBefore(dragHandle, firstCell.firstChild);
                }
            }
        });

        // Initialize Sortable.js if available
        if (typeof Sortable !== 'undefined') {
            const tbody = sortableTable.querySelector('tbody');
            if (tbody) {
                new Sortable(tbody, {
                    handle: '.drag-handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onEnd: function(evt) {
                        updatePositions();
                    }
                });
            }
        } else {
            // Fallback to basic drag and drop
            initializeBasicDragDrop(sortableTable);
        }
    }

    /**
     * Basic drag and drop implementation
     */
    function initializeBasicDragDrop(table) {
        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        let draggedElement = null;

        tbody.addEventListener('dragstart', function(e) {
            if (e.target.closest('.drag-handle')) {
                draggedElement = e.target.closest('tr');
                draggedElement.style.opacity = '0.5';
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', draggedElement.outerHTML);
            }
        });

        tbody.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        });

        tbody.addEventListener('drop', function(e) {
            e.preventDefault();
            const targetRow = e.target.closest('tr');

            if (targetRow && draggedElement && targetRow !== draggedElement) {
                const rect = targetRow.getBoundingClientRect();
                const midpoint = rect.top + rect.height / 2;

                if (e.clientY < midpoint) {
                    tbody.insertBefore(draggedElement, targetRow);
                } else {
                    tbody.insertBefore(draggedElement, targetRow.nextSibling);
                }

                updatePositions();
            }
        });

        tbody.addEventListener('dragend', function(e) {
            if (draggedElement) {
                draggedElement.style.opacity = '';
                draggedElement = null;
            }
        });

        // Make rows draggable
        const rows = tbody.querySelectorAll('tr');
        rows.forEach(row => {
            row.draggable = true;
        });
    }

    /**
     * Update catalog positions via AJAX
     */
    function updatePositions() {
        const sortableTable = document.getElementById('sortable-catalogs');
        if (!sortableTable) return;

        const rows = sortableTable.querySelectorAll('tbody tr');
        const positions = {};

        rows.forEach((row, index) => {
            const catalogId = row.dataset.catalogId || row.querySelector('[data-catalog-id]')?.dataset.catalogId;
            if (catalogId) {
                positions[catalogId] = index + 1;
            }
        });

        if (Object.keys(positions).length === 0) {
            return;
        }

        const token = document.querySelector('meta[name="csrf-token"]')?.content ||
                     document.getElementById('reorder-token')?.value;

        if (!token) {
            console.error('CSRF token not found');
            return;
        }

        showLoadingOverlay();

        fetch(window.location.origin + '/admin/catalog/reorder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                positions: positions,
                _token: token
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Pořadí bylo úspěšně aktualizováno', 'success');
                updatePositionNumbers();
            } else {
                showNotification(data.message || 'Chyba při aktualizaci pořadí', 'error');
                location.reload(); // Fallback
            }
        })
        .catch(error => {
            console.error('Reorder error:', error);
            showNotification('Chyba při komunikaci se serverem', 'error');
            location.reload();
        })
        .finally(() => {
            hideLoadingOverlay();
        });
    }

    /**
     * Update position numbers in the UI
     */
    function updatePositionNumbers() {
        const sortableTable = document.getElementById('sortable-catalogs');
        if (!sortableTable) return;

        const rows = sortableTable.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            const positionCell = row.querySelector('.position-number');
            if (positionCell) {
                positionCell.textContent = index + 1;
            }
        });
    }

    /**
     * Show loading overlay
     */
    function showLoadingOverlay() {
        let overlay = document.getElementById('loading-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'loading-overlay';
            overlay.className = 'loading-overlay';
            overlay.innerHTML = '<div class="loading-spinner"><i class="material-icons rotating">refresh</i> Načítání...</div>';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    }

    /**
     * Hide loading overlay
     */
    function hideLoadingOverlay() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    // Notifikace
    function showNotification(message, type = 'info') {
        // Odstranění existujících notifikací
        const existingNotifications = document.querySelectorAll('.admin-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show admin-notification`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Automatické skrytí po 5 sekundách
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Potvrzení před opuštěním stránky s neuloženými změnami
    let formChanged = false;
    const form = document.querySelector('.catalog-form');
    
    if (form) {
        const formElements = form.querySelectorAll('input, textarea, select');
        formElements.forEach(element => {
            element.addEventListener('change', () => {
                formChanged = true;
            });
        });

        window.addEventListener('beforeunload', (e) => {
            if (formChanged) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        form.addEventListener('submit', () => {
            formChanged = false;
        });
    }

    // Lazy loading obrázků
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
});
