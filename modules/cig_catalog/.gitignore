# CIG Catalog Module - Git Ignore File

# Composer
/vendor/
composer.lock

# PHPUnit
/coverage-html/
coverage.txt
coverage.xml
test-results.xml
.phpunit.result.cache
.phpunit.cache/

# Build and distribution
/build/
/dist/
*.zip
*.tar.gz

# Uploads (keep directories but ignore content)
uploads/images/*
uploads/files/*
!uploads/images/.gitkeep
!uploads/files/.gitkeep

# Cache files
cache/
tmp/
temp/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
var/logs/

# Configuration files (keep templates)
config/local_config.php
config/database.php

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Node.js (if using for frontend build)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package managers
package-lock.json
yarn.lock

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Documentation build
docs/api/
docs/build/

# Testing
tests/coverage/
tests/tmp/

# PrestaShop specific
config/xml/
config/themes/
config/smarty/

# Module specific ignores
translations/*.mo
translations/*.pot

# Development tools
.php_cs.cache
.phpstan.cache

# Deployment
deploy.php
deploy/

# Database dumps
*.sql
*.dump

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Sensitive data
secrets/
private/
*.key
*.pem
*.p12

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# Lock files
*.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
