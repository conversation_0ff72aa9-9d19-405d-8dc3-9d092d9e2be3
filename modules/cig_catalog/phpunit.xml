<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         cacheResultFile=".phpunit.result.cache"
         executionOrder="depends,defects"
         forceCoversAnnotation="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         convertDeprecationsToExceptions="true"
         failOnRisky="true"
         failOnWarning="true"
         verbose="true">
    
    <testsuites>
        <testsuite name="Unit Tests">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration Tests">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="Performance Tests">
            <directory>tests/Performance</directory>
        </testsuite>
        <testsuite name="Security Tests">
            <directory>tests/Security</directory>
        </testsuite>
    </testsuites>

    <coverage cacheDirectory=".phpunit.cache/code-coverage"
              processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
            <directory suffix=".php">classes</directory>
            <directory suffix=".php">controllers</directory>
        </include>
        <exclude>
            <directory>tests</directory>
            <directory>vendor</directory>
            <directory>uploads</directory>
            <directory>views</directory>
            <directory>templates</directory>
            <directory>translations</directory>
        </exclude>
        <report>
            <html outputDirectory="coverage-html"/>
            <text outputFile="coverage.txt"/>
            <clover outputFile="coverage.xml"/>
        </report>
    </coverage>

    <logging>
        <junit outputFile="test-results.xml"/>
    </logging>

    <php>
        <ini name="error_reporting" value="-1"/>
        <ini name="memory_limit" value="512M"/>
        <server name="APP_ENV" value="testing"/>
        <env name="PRESTASHOP_ENV" value="test"/>
    </php>
</phpunit>
