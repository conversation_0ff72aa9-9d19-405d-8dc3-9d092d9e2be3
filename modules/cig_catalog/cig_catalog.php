<?php
/**
 * CIG Catalog Module for PrestaShop 8.2.0
 *
 * Advanced catalog management module with ordering system
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @license MIT
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

// Autoload composer dependencies
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

// Manual autoload for CigCatalog namespace
spl_autoload_register(function ($className) {
    if (strpos($className, 'CigCatalog\\') === 0) {
        $classPath = str_replace('CigCatalog\\', '', $className);
        $classPath = str_replace('\\', '/', $classPath);
        $filePath = __DIR__ . '/src/' . $classPath . '.php';

        if (file_exists($filePath)) {
            require_once $filePath;
            return true;
        }
    }
    return false;
});

use PrestaShop\PrestaShop\Core\Module\WidgetInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Symfony\Component\Config\FileLocator;
use Configuration;
use Db;
use Tab;
use Language;

class Cig_Catalog extends Module implements WidgetInterface
{
    /**
     * Module configuration
     */
    private const MODULE_VERSION = '1.0.0';
    private const PS_MIN_VERSION = '8.2.0';
    private const PHP_MIN_VERSION = '8.1';
    
    /**
     * Supported hooks
     */
    private const HOOKS = [
        'displayHeader',
        'displayBackOfficeHeader',
        'displayAdminMenuTabLink',
        'actionFrontControllerSetMedia',
        'actionObjectLanguageAddAfter',
        'actionObjectLanguageDeleteAfter',
        'actionObjectLanguageUpdateAfter',
    ];
    
    /**
     * Module constructor
     */
    public function __construct()
    {
        $this->name = 'cig_catalog';
        $this->tab = 'front_office_features';
        $this->version = self::MODULE_VERSION;
        $this->author = 'CIG Development Team';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => self::PS_MIN_VERSION,
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;
        
        parent::__construct();
        
        $this->displayName = $this->trans('CIG Catalog', [], 'Modules.Cigcatalog.Admin');
        $this->description = $this->trans(
            'Advanced catalog management module with ordering system and email notifications.',
            [],
            'Modules.Cigcatalog.Admin'
        );
        
        $this->confirmUninstall = $this->trans(
            'Are you sure you want to uninstall this module? All catalog data will be lost.',
            [],
            'Modules.Cigcatalog.Admin'
        );
        
        // Check compatibility
        $this->checkCompatibility();
        
        // Initialize dependency injection
        $this->initializeDependencyInjection();
    }
    
    /**
     * Module installation
     */
    public function install(): bool
    {
        if (!parent::install()) {
            return false;
        }
        
        // Check system requirements
        if (!$this->checkSystemRequirements()) {
            return false;
        }
        
        // Install database tables
        if (!$this->installDatabase()) {
            return false;
        }
        
        // Register hooks
        if (!$this->registerHooks()) {
            return false;
        }
        
        // Create upload directories
        if (!$this->createUploadDirectories()) {
            return false;
        }
        
        // Install admin tabs
        if (!$this->installAdminTabs()) {
            return false;
        }
        
        // Set default configuration
        if (!$this->setDefaultConfiguration()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Module uninstallation
     */
    public function uninstall(): bool
    {
        // Remove database tables
        if (!$this->uninstallDatabase()) {
            return false;
        }
        
        // Remove admin tabs
        if (!$this->uninstallAdminTabs()) {
            return false;
        }
        
        // Remove configuration
        if (!$this->removeConfiguration()) {
            return false;
        }
        
        return parent::uninstall();
    }
    
    /**
     * Check system requirements
     */
    private function checkSystemRequirements(): bool
    {
        // Check PHP version
        if (version_compare(PHP_VERSION, self::PHP_MIN_VERSION, '<')) {
            $this->_errors[] = sprintf(
                $this->trans('PHP %s or higher is required. Current version: %s', [], 'Modules.Cigcatalog.Admin'),
                self::PHP_MIN_VERSION,
                PHP_VERSION
            );
            return false;
        }
        
        // Check required PHP extensions
        $requiredExtensions = ['gd', 'curl', 'zip', 'json'];
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                $this->_errors[] = sprintf(
                    $this->trans('PHP extension "%s" is required but not installed.', [], 'Modules.Cigcatalog.Admin'),
                    $extension
                );
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check PrestaShop compatibility
     */
    private function checkCompatibility(): void
    {
        if (version_compare(_PS_VERSION_, self::PS_MIN_VERSION, '<')) {
            $this->warning = sprintf(
                $this->trans('This module requires PrestaShop %s or higher. Current version: %s', [], 'Modules.Cigcatalog.Admin'),
                self::PS_MIN_VERSION,
                _PS_VERSION_
            );
        }
    }
    
    /**
     * Initialize dependency injection container
     */
    private function initializeDependencyInjection(): void
    {
        $containerBuilder = new ContainerBuilder();
        $loader = new YamlFileLoader($containerBuilder, new FileLocator(__DIR__ . '/config'));
        
        try {
            $loader->load('services.yml');
            $containerBuilder->compile();
            $this->container = $containerBuilder;
        } catch (Exception $e) {
            // Fallback for development - services will be loaded later
            $this->container = null;
        }
    }
    
    /**
     * Register module hooks
     */
    private function registerHooks(): bool
    {
        foreach (self::HOOKS as $hook) {
            if (!$this->registerHook($hook)) {
                $this->_errors[] = sprintf(
                    $this->trans('Failed to register hook: %s', [], 'Modules.Cigcatalog.Admin'),
                    $hook
                );
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Install database tables
     */
    private function installDatabase(): bool
    {
        $db = Db::getInstance();

        // Create main catalog table
        $sql = "CREATE TABLE IF NOT EXISTS `" . _DB_PREFIX_ . "cig_catalog` (
            `id_catalog` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `active` tinyint(1) unsigned NOT NULL DEFAULT '1',
            `position` int(10) unsigned NOT NULL DEFAULT '0',
            `is_new` tinyint(1) unsigned NOT NULL DEFAULT '0',
            `image` varchar(255) DEFAULT NULL,
            `catalog_file` varchar(255) DEFAULT NULL,
            `catalog_url` varchar(500) DEFAULT NULL,
            `download_count` int(10) unsigned NOT NULL DEFAULT '0',
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_catalog`),
            KEY `active` (`active`),
            KEY `position` (`position`),
            KEY `is_new` (`is_new`),
            KEY `date_add` (`date_add`)
        ) ENGINE=" . _MYSQL_ENGINE_ . " DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$db->execute($sql)) {
            $this->_errors[] = 'Failed to create catalog table';
            return false;
        }

        // Create multilingual catalog table
        $sql = "CREATE TABLE IF NOT EXISTS `" . _DB_PREFIX_ . "cig_catalog_lang` (
            `id_catalog` int(10) unsigned NOT NULL,
            `id_lang` int(10) unsigned NOT NULL,
            `name` varchar(255) NOT NULL,
            `description` text,
            `short_description` varchar(500) DEFAULT NULL,
            `meta_title` varchar(255) DEFAULT NULL,
            `meta_description` varchar(500) DEFAULT NULL,
            `slug` varchar(255) DEFAULT NULL,
            PRIMARY KEY (`id_catalog`, `id_lang`),
            KEY `id_catalog` (`id_catalog`),
            KEY `id_lang` (`id_lang`),
            KEY `slug` (`slug`),
            KEY `name` (`name`)
        ) ENGINE=" . _MYSQL_ENGINE_ . " DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$db->execute($sql)) {
            $this->_errors[] = 'Failed to create catalog_lang table';
            return false;
        }

        // Create catalog orders table
        $sql = "CREATE TABLE IF NOT EXISTS `" . _DB_PREFIX_ . "cig_catalog_order` (
            `id_catalog_order` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `id_catalog` int(10) unsigned NOT NULL,
            `company_name` varchar(255) NOT NULL,
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `email` varchar(255) NOT NULL,
            `phone` varchar(50) DEFAULT NULL,
            `address` text NOT NULL,
            `company_id` varchar(20) DEFAULT NULL,
            `note` text,
            `gdpr_consent` tinyint(1) NOT NULL DEFAULT '0',
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` varchar(500) DEFAULT NULL,
            `status` enum('pending','processing','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
            `admin_note` text,
            `id_lang` int(10) unsigned NOT NULL DEFAULT '1',
            `id_shop` int(10) unsigned NOT NULL DEFAULT '1',
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_catalog_order`),
            KEY `id_catalog` (`id_catalog`),
            KEY `email` (`email`),
            KEY `status` (`status`),
            KEY `date_add` (`date_add`),
            KEY `id_shop` (`id_shop`)
        ) ENGINE=" . _MYSQL_ENGINE_ . " DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$db->execute($sql)) {
            $this->_errors[] = 'Failed to create catalog_order table';
            return false;
        }

        // Create configuration table
        $sql = "CREATE TABLE IF NOT EXISTS `" . _DB_PREFIX_ . "cig_catalog_config` (
            `id_config` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `config_key` varchar(100) NOT NULL,
            `config_value` text,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_config`),
            UNIQUE KEY `config_key` (`config_key`)
        ) ENGINE=" . _MYSQL_ENGINE_ . " DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$db->execute($sql)) {
            $this->_errors[] = 'Failed to create catalog_config table';
            return false;
        }

        return true;
    }
    
    /**
     * Uninstall database tables
     */
    private function uninstallDatabase(): bool
    {
        $db = Db::getInstance();
        $tables = [
            'cig_catalog_order',
            'cig_catalog_lang',
            'cig_catalog',
            'cig_catalog_config'
        ];

        foreach ($tables as $table) {
            $sql = "DROP TABLE IF EXISTS `" . _DB_PREFIX_ . $table . "`";
            if (!$db->execute($sql)) {
                $this->_errors[] = "Failed to drop table: {$table}";
                return false;
            }
        }

        return true;
    }
    
    /**
     * Create upload directories
     */
    private function createUploadDirectories(): bool
    {
        $directories = [
            __DIR__ . '/uploads/images/',
            __DIR__ . '/uploads/files/',
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    $this->_errors[] = sprintf(
                        $this->trans('Failed to create directory: %s', [], 'Modules.Cigcatalog.Admin'),
                        $dir
                    );
                    return false;
                }
            }
            
            // Create .htaccess for security
            $htaccessFile = $dir . '.htaccess';
            if (!file_exists($htaccessFile)) {
                $htaccessContent = "# Deny direct access to uploaded files\n";
                $htaccessContent .= "<FilesMatch \"\.php$\">\n";
                $htaccessContent .= "    Order Deny,Allow\n";
                $htaccessContent .= "    Deny from all\n";
                $htaccessContent .= "</FilesMatch>\n";
                
                file_put_contents($htaccessFile, $htaccessContent);
            }
        }
        
        return true;
    }
    
    /**
     * Install admin tabs
     */
    private function installAdminTabs(): bool
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminCatalog';
        $tab->route_name = 'admin_catalog_index';
        $tab->name = [];
        
        foreach (Language::getLanguages(false) as $lang) {
            $tab->name[$lang['id_lang']] = $this->trans('Catalogs', [], 'Modules.Cigcatalog.Admin', $lang['locale']);
        }
        
        $tab->id_parent = (int) Tab::getIdFromClassName('IMPROVE');
        $tab->module = $this->name;
        
        return $tab->add();
    }
    
    /**
     * Uninstall admin tabs
     */
    private function uninstallAdminTabs(): bool
    {
        $idTab = (int) Tab::getIdFromClassName('AdminCatalog');
        
        if ($idTab) {
            $tab = new Tab($idTab);
            return $tab->delete();
        }
        
        return true;
    }
    
    /**
     * Set default configuration
     */
    private function setDefaultConfiguration(): bool
    {
        // Initialize default configuration using CatalogConfig entity
        if (!\CigCatalog\Entity\CatalogConfig::initializeDefaults()) {
            $this->_errors[] = $this->trans('Failed to initialize default configuration.', [], 'Modules.Cigcatalog.Admin');
            return false;
        }

        // Set admin email if not already set
        $adminEmail = \CigCatalog\Entity\CatalogConfig::get('admin_email');
        if (empty($adminEmail)) {
            \CigCatalog\Entity\CatalogConfig::set('admin_email', Configuration::get('PS_SHOP_EMAIL'));
        }

        // Set from email if not already set
        $fromEmail = \CigCatalog\Entity\CatalogConfig::get('from_email');
        if (empty($fromEmail)) {
            \CigCatalog\Entity\CatalogConfig::set('from_email', Configuration::get('PS_SHOP_EMAIL'));
        }

        return true;
    }
    
    /**
     * Remove configuration
     */
    private function removeConfiguration(): bool
    {
        $configKeys = [
            'CIG_CATALOG_ITEMS_PER_PAGE',
            'CIG_CATALOG_ENABLE_ORDERING',
            'CIG_CATALOG_ADMIN_EMAIL',
            'CIG_CATALOG_MAX_FILE_SIZE',
            'CIG_CATALOG_ALLOWED_EXTENSIONS',
        ];
        
        foreach ($configKeys as $key) {
            Configuration::deleteByName($key);
        }
        
        return true;
    }
    
    /**
     * Widget interface methods
     */
    public function getWidgetVariables($hookName, array $configuration): array
    {
        return [];
    }
    
    public function renderWidget($hookName, array $configuration): string
    {
        return '';
    }
}
