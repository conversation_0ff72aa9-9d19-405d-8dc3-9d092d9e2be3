<?php

global $_MODULE;
$_MODULE = [];

// Module Information
$_MODULE['<{cig_catalog}prestashop>cig_catalog_'] = 'CIG Catalog';
$_MODULE['<{cig_catalog}prestashop>cig_catalog_description'] = 'Advanced catalog management module with ordering system and email notifications.';

// Admin Interface
$_MODULE['<{cig_catalog}prestashop>admin_catalog_title'] = 'Catalog Management';
$_MODULE['<{cig_catalog}prestashop>admin_catalog_list'] = 'Catalog List';
$_MODULE['<{cig_catalog}prestashop>admin_catalog_add'] = 'Add Catalog';
$_MODULE['<{cig_catalog}prestashop>admin_catalog_edit'] = 'Edit Catalog';
$_MODULE['<{cig_catalog}prestashop>admin_catalog_delete'] = 'Delete Catalog';
$_MODULE['<{cig_catalog}prestashop>admin_catalog_delete_confirm'] = 'Are you sure you want to delete this catalog?';

// Form Fields
$_MODULE['<{cig_catalog}prestashop>form_name'] = 'Name';
$_MODULE['<{cig_catalog}prestashop>form_description'] = 'Description';
$_MODULE['<{cig_catalog}prestashop>form_short_description'] = 'Short Description';
$_MODULE['<{cig_catalog}prestashop>form_image'] = 'Image';
$_MODULE['<{cig_catalog}prestashop>form_catalog_file'] = 'Catalog File';
$_MODULE['<{cig_catalog}prestashop>form_catalog_url'] = 'External URL';
$_MODULE['<{cig_catalog}prestashop>form_active'] = 'Active';
$_MODULE['<{cig_catalog}prestashop>form_position'] = 'Position';
$_MODULE['<{cig_catalog}prestashop>form_is_new'] = 'Mark as New';
$_MODULE['<{cig_catalog}prestashop>form_meta_title'] = 'Meta Title';
$_MODULE['<{cig_catalog}prestashop>form_meta_description'] = 'Meta Description';
$_MODULE['<{cig_catalog}prestashop>form_slug'] = 'URL Slug';

// Actions
$_MODULE['<{cig_catalog}prestashop>action_save'] = 'Save';
$_MODULE['<{cig_catalog}prestashop>action_cancel'] = 'Cancel';
$_MODULE['<{cig_catalog}prestashop>action_delete'] = 'Delete';
$_MODULE['<{cig_catalog}prestashop>action_edit'] = 'Edit';
$_MODULE['<{cig_catalog}prestashop>action_view'] = 'View';
$_MODULE['<{cig_catalog}prestashop>action_download'] = 'Download';
$_MODULE['<{cig_catalog}prestashop>action_order'] = 'Order';
$_MODULE['<{cig_catalog}prestashop>action_upload'] = 'Upload';
$_MODULE['<{cig_catalog}prestashop>action_search'] = 'Search';
$_MODULE['<{cig_catalog}prestashop>action_filter'] = 'Filter';
$_MODULE['<{cig_catalog}prestashop>action_reset'] = 'Reset';

// Status Messages
$_MODULE['<{cig_catalog}prestashop>success_catalog_created'] = 'Catalog has been successfully created.';
$_MODULE['<{cig_catalog}prestashop>success_catalog_updated'] = 'Catalog has been successfully updated.';
$_MODULE['<{cig_catalog}prestashop>success_catalog_deleted'] = 'Catalog has been successfully deleted.';
$_MODULE['<{cig_catalog}prestashop>success_file_uploaded'] = 'File has been successfully uploaded.';
$_MODULE['<{cig_catalog}prestashop>success_order_created'] = 'Order has been successfully created.';
$_MODULE['<{cig_catalog}prestashop>success_email_sent'] = 'Email has been successfully sent.';

// Error Messages
$_MODULE['<{cig_catalog}prestashop>error_catalog_not_found'] = 'Catalog not found.';
$_MODULE['<{cig_catalog}prestashop>error_file_upload'] = 'File upload error.';
$_MODULE['<{cig_catalog}prestashop>error_file_too_large'] = 'File is too large.';
$_MODULE['<{cig_catalog}prestashop>error_invalid_file_type'] = 'Invalid file type.';
$_MODULE['<{cig_catalog}prestashop>error_form_validation'] = 'Form contains errors.';
$_MODULE['<{cig_catalog}prestashop>error_database'] = 'Database error.';
$_MODULE['<{cig_catalog}prestashop>error_permission_denied'] = 'Access denied.';
$_MODULE['<{cig_catalog}prestashop>error_email_send'] = 'Email sending error.';

// Frontend
$_MODULE['<{cig_catalog}prestashop>frontend_catalog_title'] = 'Catalogs';
$_MODULE['<{cig_catalog}prestashop>frontend_catalog_subtitle'] = 'Browse our catalogs and order them for free.';
$_MODULE['<{cig_catalog}prestashop>frontend_no_catalogs'] = 'No catalogs available.';
$_MODULE['<{cig_catalog}prestashop>frontend_new_badge'] = 'New';
$_MODULE['<{cig_catalog}prestashop>frontend_download_count'] = 'Downloaded: %d times';
$_MODULE['<{cig_catalog}prestashop>frontend_page_of'] = 'Page %d of %d';

// Order Form
$_MODULE['<{cig_catalog}prestashop>order_form_title'] = 'Order Catalog';
$_MODULE['<{cig_catalog}prestashop>order_form_subtitle'] = 'Fill out the form and we will send you the catalog for free.';
$_MODULE['<{cig_catalog}prestashop>order_form_name'] = 'Full Name';
$_MODULE['<{cig_catalog}prestashop>order_form_email'] = 'Email';
$_MODULE['<{cig_catalog}prestashop>order_form_phone'] = 'Phone';
$_MODULE['<{cig_catalog}prestashop>order_form_company'] = 'Company';
$_MODULE['<{cig_catalog}prestashop>order_form_ico'] = 'Company ID';
$_MODULE['<{cig_catalog}prestashop>order_form_address'] = 'Address';
$_MODULE['<{cig_catalog}prestashop>order_form_city'] = 'City';
$_MODULE['<{cig_catalog}prestashop>order_form_postal_code'] = 'Postal Code';
$_MODULE['<{cig_catalog}prestashop>order_form_country'] = 'Country';
$_MODULE['<{cig_catalog}prestashop>order_form_note'] = 'Note';
$_MODULE['<{cig_catalog}prestashop>order_form_gdpr'] = 'I agree to the processing of personal data';
$_MODULE['<{cig_catalog}prestashop>order_form_submit'] = 'Order Catalog';

// Email Configuration
$_MODULE['<{cig_catalog}prestashop>email_config_title'] = 'Email Configuration';
$_MODULE['<{cig_catalog}prestashop>email_config_admin_email'] = 'Admin Email';
$_MODULE['<{cig_catalog}prestashop>email_config_from_name'] = 'From Name';
$_MODULE['<{cig_catalog}prestashop>email_config_from_email'] = 'From Email';
$_MODULE['<{cig_catalog}prestashop>email_config_smtp_enabled'] = 'Use SMTP';
$_MODULE['<{cig_catalog}prestashop>email_config_smtp_host'] = 'SMTP Host';
$_MODULE['<{cig_catalog}prestashop>email_config_smtp_port'] = 'SMTP Port';
$_MODULE['<{cig_catalog}prestashop>email_config_smtp_username'] = 'SMTP Username';
$_MODULE['<{cig_catalog}prestashop>email_config_smtp_password'] = 'SMTP Password';
$_MODULE['<{cig_catalog}prestashop>email_config_test'] = 'Test Configuration';

// Statistics
$_MODULE['<{cig_catalog}prestashop>stats_title'] = 'Statistics';
$_MODULE['<{cig_catalog}prestashop>stats_total_catalogs'] = 'Total Catalogs';
$_MODULE['<{cig_catalog}prestashop>stats_total_downloads'] = 'Total Downloads';
$_MODULE['<{cig_catalog}prestashop>stats_total_orders'] = 'Total Orders';
$_MODULE['<{cig_catalog}prestashop>stats_this_month'] = 'This Month';
$_MODULE['<{cig_catalog}prestashop>stats_popular_catalogs'] = 'Most Popular Catalogs';

// Bulk Operations
$_MODULE['<{cig_catalog}prestashop>bulk_select_all'] = 'Select All';
$_MODULE['<{cig_catalog}prestashop>bulk_with_selected'] = 'With Selected:';
$_MODULE['<{cig_catalog}prestashop>bulk_delete'] = 'Delete';
$_MODULE['<{cig_catalog}prestashop>bulk_activate'] = 'Activate';
$_MODULE['<{cig_catalog}prestashop>bulk_deactivate'] = 'Deactivate';
$_MODULE['<{cig_catalog}prestashop>bulk_export'] = 'Export';

// File Upload
$_MODULE['<{cig_catalog}prestashop>upload_drag_drop'] = 'Drag files here or click to select';
$_MODULE['<{cig_catalog}prestashop>upload_max_size'] = 'Maximum size: %s';
$_MODULE['<{cig_catalog}prestashop>upload_allowed_types'] = 'Allowed types: %s';
$_MODULE['<{cig_catalog}prestashop>upload_progress'] = 'Uploading... %d%%';
$_MODULE['<{cig_catalog}prestashop>upload_complete'] = 'Upload complete';
$_MODULE['<{cig_catalog}prestashop>upload_error'] = 'Upload error';

// Validation Messages
$_MODULE['<{cig_catalog}prestashop>validation_required'] = 'This field is required.';
$_MODULE['<{cig_catalog}prestashop>validation_email'] = 'Please enter a valid email address.';
$_MODULE['<{cig_catalog}prestashop>validation_phone'] = 'Please enter a valid phone number.';
$_MODULE['<{cig_catalog}prestashop>validation_ico'] = 'Please enter a valid company ID.';
$_MODULE['<{cig_catalog}prestashop>validation_postal_code'] = 'Please enter a valid postal code.';
$_MODULE['<{cig_catalog}prestashop>validation_min_length'] = 'Minimum length is %d characters.';
$_MODULE['<{cig_catalog}prestashop>validation_max_length'] = 'Maximum length is %d characters.';

// Navigation
$_MODULE['<{cig_catalog}prestashop>nav_previous'] = 'Previous';
$_MODULE['<{cig_catalog}prestashop>nav_next'] = 'Next';
$_MODULE['<{cig_catalog}prestashop>nav_first'] = 'First';
$_MODULE['<{cig_catalog}prestashop>nav_last'] = 'Last';

// General
$_MODULE['<{cig_catalog}prestashop>general_yes'] = 'Yes';
$_MODULE['<{cig_catalog}prestashop>general_no'] = 'No';
$_MODULE['<{cig_catalog}prestashop>general_loading'] = 'Loading...';
$_MODULE['<{cig_catalog}prestashop>general_close'] = 'Close';
$_MODULE['<{cig_catalog}prestashop>general_confirm'] = 'Confirm';
$_MODULE['<{cig_catalog}prestashop>general_back'] = 'Back';
$_MODULE['<{cig_catalog}prestashop>general_continue'] = 'Continue';

return $_MODULE;
