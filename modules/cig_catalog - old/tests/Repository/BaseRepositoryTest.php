<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Tests\Repository;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use CigCatalog\Repository\BaseRepository;
use Db;
use PrestaShopLogger;
use Exception;

/**
 * Test class for BaseRepository
 */
class BaseRepositoryTest extends TestCase
{
    /** @var BaseRepository|MockObject */
    private $repository;

    /** @var Db|MockObject */
    private $mockDb;

    /** @var PrestaShopLogger|MockObject */
    private $mockLogger;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock objects
        $this->mockDb = $this->createMock(Db::class);
        $this->mockLogger = $this->createMock(PrestaShopLogger::class);

        // Create a concrete implementation of BaseRepository for testing
        $this->repository = new class extends BaseRepository {
            public function setConnection($connection) {
                $this->connection = $connection;
            }

            public function setLogger($logger) {
                $this->logger = $logger;
            }

            // Expose protected methods for testing
            public function testExecuteQuery(string $sql, array $params = []): array {
                return $this->executeQuery($sql, $params);
            }

            public function testExecuteValue(string $sql, array $params = []) {
                return $this->executeValue($sql, $params);
            }

            public function testExecuteUpdate(string $sql, array $params = []): bool {
                return $this->executeUpdate($sql, $params);
            }

            public function testValidateInt($value, int $default = 0): int {
                return $this->validateInt($value, $default);
            }

            public function testValidateString($value, string $default = ''): string {
                return $this->validateString($value, $default);
            }

            public function testEscape(string $string): string {
                return $this->escape($string);
            }

            public function testGetTable(string $table): string {
                return $this->getTable($table);
            }
        };

        // Inject mocks
        $this->repository->setConnection($this->mockDb);
        $this->repository->setLogger($this->mockLogger);
    }

    public function testExecuteQuerySuccess(): void
    {
        $expectedResult = [
            ['id' => 1, 'name' => 'Test'],
            ['id' => 2, 'name' => 'Test2']
        ];

        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->with('SELECT * FROM test')
            ->willReturn($expectedResult);

        $result = $this->repository->testExecuteQuery('SELECT * FROM test');
        $this->assertEquals($expectedResult, $result);
    }

    public function testExecuteQueryFailure(): void
    {
        $this->mockDb->expects($this->once())
            ->method('executeS')
            ->with('SELECT * FROM test')
            ->willReturn(false);

        $this->mockDb->expects($this->once())
            ->method('getMsgError')
            ->willReturn('Database error');

        $this->mockLogger->expects($this->once())
            ->method('addLog');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Query execution failed: Database error');

        $this->repository->testExecuteQuery('SELECT * FROM test');
    }

    public function testExecuteValueSuccess(): void
    {
        $expectedValue = 'test_value';

        $this->mockDb->expects($this->once())
            ->method('getValue')
            ->with('SELECT name FROM test WHERE id = 1')
            ->willReturn($expectedValue);

        $result = $this->repository->testExecuteValue('SELECT name FROM test WHERE id = 1');
        $this->assertEquals($expectedValue, $result);
    }

    public function testExecuteUpdateSuccess(): void
    {
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('UPDATE test SET name = "updated" WHERE id = 1')
            ->willReturn(true);

        $result = $this->repository->testExecuteUpdate('UPDATE test SET name = "updated" WHERE id = 1');
        $this->assertTrue($result);
    }

    public function testExecuteUpdateFailure(): void
    {
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('UPDATE test SET name = "updated" WHERE id = 1')
            ->willReturn(false);

        $this->mockDb->expects($this->once())
            ->method('getMsgError')
            ->willReturn('Update failed');

        $this->mockLogger->expects($this->once())
            ->method('addLog');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Update query execution failed: Update failed');

        $this->repository->testExecuteUpdate('UPDATE test SET name = "updated" WHERE id = 1');
    }

    public function testValidateInt(): void
    {
        // Valid integers
        $this->assertEquals(5, $this->repository->testValidateInt(5));
        $this->assertEquals(0, $this->repository->testValidateInt(0));
        $this->assertEquals(100, $this->repository->testValidateInt('100'));

        // Invalid values should return default
        $this->assertEquals(0, $this->repository->testValidateInt(-5));
        $this->assertEquals(0, $this->repository->testValidateInt('invalid'));
        $this->assertEquals(0, $this->repository->testValidateInt(null));
        $this->assertEquals(10, $this->repository->testValidateInt('invalid', 10));
    }

    public function testValidateString(): void
    {
        // Valid strings
        $this->assertEquals('test', $this->repository->testValidateString('test'));
        $this->assertEquals('test', $this->repository->testValidateString('  test  '));
        $this->assertEquals('123', $this->repository->testValidateString(123));

        // Invalid values should return default
        $this->assertEquals('', $this->repository->testValidateString(null));
        $this->assertEquals('', $this->repository->testValidateString([]));
        $this->assertEquals('default', $this->repository->testValidateString(null, 'default'));
    }

    public function testEscape(): void
    {
        // Mock the global pSQL function
        if (!function_exists('pSQL')) {
            function pSQL($string) {
                return addslashes($string);
            }
        }

        $result = $this->repository->testEscape("test'string");
        $this->assertEquals("test\\'string", $result);
    }

    public function testGetTable(): void
    {
        // Mock the global _DB_PREFIX_ constant
        if (!defined('_DB_PREFIX_')) {
            define('_DB_PREFIX_', 'ps_');
        }

        $result = $this->repository->testGetTable('test_table');
        $this->assertEquals('ps_test_table', $result);
    }

    public function testTransactionMethods(): void
    {
        // Test begin transaction
        $this->mockDb->expects($this->once())
            ->method('execute')
            ->with('START TRANSACTION')
            ->willReturn(true);

        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('beginTransaction');
        $method->setAccessible(true);
        $result = $method->invoke($this->repository);
        $this->assertTrue($result);
    }

    public function testGetLastInsertId(): void
    {
        $expectedId = 123;

        $this->mockDb->expects($this->once())
            ->method('Insert_ID')
            ->willReturn($expectedId);

        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('getLastInsertId');
        $method->setAccessible(true);
        $result = $method->invoke($this->repository);
        $this->assertEquals($expectedId, $result);
    }

    public function testExecuteWithRetry(): void
    {
        $callCount = 0;
        $queryFunction = function() use (&$callCount) {
            $callCount++;
            if ($callCount < 3) {
                throw new Exception('Temporary failure');
            }
            return 'success';
        };

        $this->mockLogger->expects($this->exactly(2))
            ->method('addLog');

        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('executeWithRetry');
        $method->setAccessible(true);
        $result = $method->invoke($this->repository, $queryFunction, 3);

        $this->assertEquals('success', $result);
        $this->assertEquals(3, $callCount);
    }

    public function testExecuteWithRetryMaxAttemptsReached(): void
    {
        $queryFunction = function() {
            throw new Exception('Persistent failure');
        };

        $this->mockLogger->expects($this->exactly(2))
            ->method('addLog');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Persistent failure');

        $reflection = new \ReflectionClass($this->repository);
        $method = $reflection->getMethod('executeWithRetry');
        $method->setAccessible(true);
        $method->invoke($this->repository, $queryFunction, 3);
    }
}
