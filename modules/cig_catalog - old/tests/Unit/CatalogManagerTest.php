<?php
/**
 * CIG Catalog Manager Unit Tests
 * 
 * Unit tests for CatalogManager service
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

declare(strict_types=1);

namespace CigCatalog\Tests\Unit;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use CigCatalog\Service\CatalogManager;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Service\FileUploadService;
use CigCatalog\Service\CacheService;
use CigCatalog\Entity\Catalog;
use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;

class CatalogManagerTest extends TestCase
{
    /** @var CatalogManager */
    private $catalogManager;
    
    /** @var MockObject|CatalogRepository */
    private $mockRepository;
    
    /** @var MockObject|FileUploadService */
    private $mockFileUpload;
    
    /** @var MockObject|CacheService */
    private $mockCache;

    protected function setUp(): void
    {
        $this->mockRepository = $this->createMock(CatalogRepository::class);
        $this->mockFileUpload = $this->createMock(FileUploadService::class);
        $this->mockCache = $this->createMock(CacheService::class);
        
        $this->catalogManager = new CatalogManager(
            $this->mockRepository,
            $this->mockFileUpload,
            $this->mockCache
        );
    }

    /**
     * Test successful catalog creation
     */
    public function testCreateCatalogSuccess(): void
    {
        $catalogData = [
            'name' => ['cs' => 'Test Katalog', 'en' => 'Test Catalog'],
            'description' => ['cs' => 'Testovací popis', 'en' => 'Test description'],
            'active' => true,
            'is_new' => true,
            'position' => 1,
        ];

        // Mock successful creation
        $this->mockRepository
            ->expects($this->once())
            ->method('create')
            ->with($catalogData)
            ->willReturn(123);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->createCatalog($catalogData);

        $this->assertEquals(123, $result);
    }

    /**
     * Test catalog creation with invalid data
     */
    public function testCreateCatalogWithInvalidData(): void
    {
        $invalidData = [
            // Missing required fields
            'active' => true,
        ];

        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Name is required');

        $this->catalogManager->createCatalog($invalidData);
    }

    /**
     * Test catalog update
     */
    public function testUpdateCatalogSuccess(): void
    {
        $catalogId = 123;
        $updateData = [
            'name' => ['cs' => 'Aktualizovaný Katalog'],
            'active' => false,
        ];

        // Mock successful update
        $this->mockRepository
            ->expects($this->once())
            ->method('update')
            ->with($catalogId, $updateData)
            ->willReturn(true);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->updateCatalog($catalogId, $updateData);

        $this->assertTrue($result);
    }

    /**
     * Test catalog deletion
     */
    public function testDeleteCatalogSuccess(): void
    {
        $catalogId = 123;

        // Mock successful deletion
        $this->mockRepository
            ->expects($this->once())
            ->method('delete')
            ->with($catalogId)
            ->willReturn(true);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->deleteCatalog($catalogId);

        $this->assertTrue($result);
    }

    /**
     * Test file upload handling
     */
    public function testHandleFileUpload(): void
    {
        $catalogId = 123;
        $fileData = [
            'name' => 'test.jpg',
            'tmp_name' => '/tmp/test',
            'size' => 1024,
            'type' => 'image/jpeg',
        ];

        // Mock successful file upload
        $this->mockFileUpload
            ->expects($this->once())
            ->method('uploadImage')
            ->with($fileData, 'catalogs')
            ->willReturn('uploaded_image.jpg');

        // Mock repository update
        $this->mockRepository
            ->expects($this->once())
            ->method('update')
            ->with($catalogId, ['image' => 'uploaded_image.jpg'])
            ->willReturn(true);

        $result = $this->catalogManager->uploadCatalogImage($catalogId, $fileData);

        $this->assertEquals('uploaded_image.jpg', $result);
    }

    /**
     * Test file upload with invalid file
     */
    public function testHandleInvalidFileUpload(): void
    {
        $catalogId = 123;
        $invalidFileData = [
            'name' => 'test.exe',
            'tmp_name' => '/tmp/test',
            'size' => 1024,
            'type' => 'application/exe',
        ];

        // Mock file validation failure
        $this->mockFileUpload
            ->expects($this->once())
            ->method('uploadImage')
            ->with($invalidFileData, 'catalogs')
            ->willThrowException(new DomainException('Invalid file type'));

        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Invalid file type');

        $this->catalogManager->uploadCatalogImage($catalogId, $invalidFileData);
    }

    /**
     * Test catalog validation
     */
    public function testValidateCatalogData(): void
    {
        // Test valid data
        $validData = [
            'name' => ['cs' => 'Valid Name'],
            'description' => ['cs' => 'Valid Description'],
            'active' => true,
        ];

        // Should not throw exception
        $this->catalogManager->validateCatalogData($validData);
        $this->assertTrue(true); // If we get here, validation passed

        // Test invalid data - missing name
        $invalidData = [
            'description' => ['cs' => 'Valid Description'],
            'active' => true,
        ];

        $this->expectException(DomainException::class);
        $this->catalogManager->validateCatalogData($invalidData);
    }

    /**
     * Test position management
     */
    public function testUpdatePositions(): void
    {
        $positions = [
            1 => 3,
            2 => 1,
            3 => 2,
        ];

        // Mock repository calls
        $this->mockRepository
            ->expects($this->exactly(3))
            ->method('updatePosition')
            ->withConsecutive(
                [1, 3],
                [2, 1],
                [3, 2]
            )
            ->willReturn(true);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->updatePositions($positions);

        $this->assertTrue($result);
    }

    /**
     * Test bulk operations
     */
    public function testBulkActivate(): void
    {
        $catalogIds = [1, 2, 3];

        // Mock repository calls
        $this->mockRepository
            ->expects($this->exactly(3))
            ->method('update')
            ->withConsecutive(
                [1, ['active' => true]],
                [2, ['active' => true]],
                [3, ['active' => true]]
            )
            ->willReturn(true);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->bulkActivate($catalogIds);

        $this->assertTrue($result);
    }

    /**
     * Test bulk deactivation
     */
    public function testBulkDeactivate(): void
    {
        $catalogIds = [1, 2, 3];

        // Mock repository calls
        $this->mockRepository
            ->expects($this->exactly(3))
            ->method('update')
            ->withConsecutive(
                [1, ['active' => false]],
                [2, ['active' => false]],
                [3, ['active' => false]]
            )
            ->willReturn(true);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->bulkDeactivate($catalogIds);

        $this->assertTrue($result);
    }

    /**
     * Test bulk deletion
     */
    public function testBulkDelete(): void
    {
        $catalogIds = [1, 2, 3];

        // Mock repository calls
        $this->mockRepository
            ->expects($this->exactly(3))
            ->method('delete')
            ->withConsecutive([1], [2], [3])
            ->willReturn(true);

        // Mock cache invalidation
        $this->mockCache
            ->expects($this->once())
            ->method('invalidatePattern')
            ->with('catalogs_*');

        $result = $this->catalogManager->bulkDelete($catalogIds);

        $this->assertTrue($result);
    }

    /**
     * Test error handling in bulk operations
     */
    public function testBulkOperationWithError(): void
    {
        $catalogIds = [1, 2, 3];

        // Mock repository calls with one failure
        $this->mockRepository
            ->expects($this->exactly(3))
            ->method('delete')
            ->withConsecutive([1], [2], [3])
            ->willReturnOnConsecutiveCalls(true, false, true);

        $this->expectException(DomainException::class);
        $this->expectExceptionMessage('Failed to delete some catalogs');

        $this->catalogManager->bulkDelete($catalogIds);
    }

    /**
     * Test statistics generation
     */
    public function testGetStatistics(): void
    {
        $mockStats = [
            'total_catalogs' => 10,
            'active_catalogs' => 8,
            'inactive_catalogs' => 2,
            'new_catalogs' => 3,
            'total_downloads' => 150,
        ];

        $this->mockRepository
            ->expects($this->once())
            ->method('getStatistics')
            ->willReturn($mockStats);

        $result = $this->catalogManager->getStatistics();

        $this->assertEquals($mockStats, $result);
    }
}
