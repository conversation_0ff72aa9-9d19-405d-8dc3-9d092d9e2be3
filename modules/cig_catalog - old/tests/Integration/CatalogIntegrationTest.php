<?php
/**
 * CIG Catalog Integration Tests
 * 
 * Comprehensive integration tests for the catalog module
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

declare(strict_types=1);

namespace CigCatalog\Tests\Integration;

use PHPUnit\Framework\TestCase;
use CigCatalog\Entity\Catalog;
use CigCatalog\Entity\CatalogOrder;
use CigCatalog\Entity\CatalogConfig;
use CigCatalog\Service\CatalogManager;
use CigCatalog\Service\CatalogOrderService;
use CigCatalog\Service\EmailService;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Repository\CatalogOrderRepository;

class CatalogIntegrationTest extends TestCase
{
    private static $moduleInstance;
    private static $testCatalogId;
    private static $testOrderId;

    public static function setUpBeforeClass(): void
    {
        // Initialize PrestaShop environment for testing
        self::initializePrestaShop();
        
        // Get module instance
        self::$moduleInstance = \Module::getInstanceByName('cig_catalog');
        
        if (!self::$moduleInstance) {
            self::markTestSkipped('CIG Catalog module is not installed');
        }
    }

    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        self::cleanupTestData();
    }

    /**
     * Test catalog creation and management
     */
    public function testCatalogCreation(): void
    {
        $catalogData = [
            'name' => ['cs' => 'Test Katalog', 'en' => 'Test Catalog'],
            'description' => ['cs' => 'Testovací popis', 'en' => 'Test description'],
            'short_description' => ['cs' => 'Krátký popis', 'en' => 'Short description'],
            'active' => true,
            'is_new' => true,
            'position' => 1,
        ];

        $catalogManager = self::$moduleInstance->get('cig_catalog.service.catalog_manager');
        $catalogId = $catalogManager->createCatalog($catalogData);

        $this->assertIsInt($catalogId);
        $this->assertGreaterThan(0, $catalogId);

        // Store for cleanup
        self::$testCatalogId = $catalogId;

        // Verify catalog was created
        $catalog = new Catalog($catalogId);
        $this->assertTrue(\Validate::isLoadedObject($catalog));
        $this->assertEquals($catalogData['active'], $catalog->active);
        $this->assertEquals($catalogData['is_new'], $catalog->is_new);
    }

    /**
     * Test catalog repository operations
     * 
     * @depends testCatalogCreation
     */
    public function testCatalogRepository(): void
    {
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
        
        // Test findById
        $catalog = $repository->findById(self::$testCatalogId, 1); // Czech language
        $this->assertNotNull($catalog);
        $this->assertEquals('Test Katalog', $catalog['name']);

        // Test findAllActive
        $activeCatalogs = $repository->findAllActive(1);
        $this->assertIsArray($activeCatalogs);
        $this->assertNotEmpty($activeCatalogs);

        // Test search functionality
        $searchResults = $repository->searchCatalogs('Test', 1);
        $this->assertIsArray($searchResults);
        $this->assertNotEmpty($searchResults);

        // Test count operations
        $totalCount = $repository->countActive(1);
        $this->assertIsInt($totalCount);
        $this->assertGreaterThan(0, $totalCount);
    }

    /**
     * Test order creation and processing
     * 
     * @depends testCatalogCreation
     */
    public function testOrderCreation(): void
    {
        $orderData = [
            'catalog_id' => self::$testCatalogId,
            'company_name' => 'Test s.r.o.',
            'first_name' => 'Jan',
            'last_name' => 'Novák',
            'email' => '<EMAIL>',
            'phone' => '+420123456789',
            'address' => 'Testovací ulice 123, 110 00 Praha',
            'company_id' => '12345678',
            'note' => 'Testovací objednávka',
            'gdpr_consent' => true,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'PHPUnit Test',
            'language_id' => 1,
            'shop_id' => 1,
        ];

        $orderService = self::$moduleInstance->get('cig_catalog.service.catalog_order');
        $orderId = $orderService->createOrder($orderData);

        $this->assertIsInt($orderId);
        $this->assertGreaterThan(0, $orderId);

        // Store for cleanup
        self::$testOrderId = $orderId;

        // Verify order was created
        $order = $orderService->getOrderById($orderId);
        $this->assertNotNull($order);
        $this->assertEquals($orderData['email'], $order['email']);
        $this->assertEquals($orderData['company_name'], $order['company_name']);
    }

    /**
     * Test order validation
     */
    public function testOrderValidation(): void
    {
        $orderService = self::$moduleInstance->get('cig_catalog.service.catalog_order');

        // Test missing required fields
        $invalidOrderData = [
            'catalog_id' => self::$testCatalogId,
            // Missing required fields
        ];

        $this->expectException(\PrestaShop\PrestaShop\Core\Domain\Exception\DomainException::class);
        $orderService->createOrder($invalidOrderData);
    }

    /**
     * Test email functionality
     * 
     * @depends testOrderCreation
     */
    public function testEmailService(): void
    {
        $emailService = self::$moduleInstance->get('cig_catalog.service.email');

        // Test email configuration
        $testResults = $emailService->testEmailConfiguration();
        $this->assertIsArray($testResults);
        $this->assertArrayHasKey('smtp_configured', $testResults);
        $this->assertArrayHasKey('test_email_sent', $testResults);

        // Test template availability
        $templates = $emailService->getAvailableTemplates();
        $this->assertIsArray($templates);
        $this->assertArrayHasKey('order_notification', $templates);
        $this->assertArrayHasKey('order_confirmation', $templates);
    }

    /**
     * Test configuration management
     */
    public function testConfigurationManagement(): void
    {
        // Test setting configuration
        $testKey = 'test_config_key';
        $testValue = 'test_config_value';
        
        CatalogConfig::set($testKey, $testValue);
        $retrievedValue = CatalogConfig::get($testKey);
        
        $this->assertEquals($testValue, $retrievedValue);

        // Test default value
        $defaultValue = 'default_value';
        $nonExistentValue = CatalogConfig::get('non_existent_key', $defaultValue);
        
        $this->assertEquals($defaultValue, $nonExistentValue);

        // Cleanup test config
        CatalogConfig::delete($testKey);
    }

    /**
     * Test file upload functionality
     */
    public function testFileUploadService(): void
    {
        $fileUploadService = self::$moduleInstance->get('cig_catalog.service.file_upload');

        // Test allowed file types
        $allowedTypes = $fileUploadService->getAllowedImageTypes();
        $this->assertIsArray($allowedTypes);
        $this->assertContains('jpg', $allowedTypes);
        $this->assertContains('png', $allowedTypes);

        // Test file validation
        $this->assertTrue($fileUploadService->isValidImageType('test.jpg'));
        $this->assertTrue($fileUploadService->isValidFileType('test.pdf'));
        $this->assertFalse($fileUploadService->isValidImageType('test.exe'));
    }

    /**
     * Test cache functionality
     */
    public function testCacheService(): void
    {
        $cacheService = self::$moduleInstance->get('cig_catalog.service.cache');

        $testKey = 'test_cache_key';
        $testValue = ['test' => 'data'];

        // Test cache set/get
        $cacheService->set($testKey, $testValue, 3600);
        $retrievedValue = $cacheService->get($testKey);

        $this->assertEquals($testValue, $retrievedValue);

        // Test cache delete
        $cacheService->delete($testKey);
        $deletedValue = $cacheService->get($testKey);

        $this->assertNull($deletedValue);
    }

    /**
     * Test security features
     */
    public function testSecurityFeatures(): void
    {
        // Test CSRF token generation
        $token1 = \Tools::getToken(false);
        $token2 = \Tools::getToken(false);
        
        $this->assertIsString($token1);
        $this->assertEquals($token1, $token2); // Should be same for same session

        // Test input validation
        $this->assertTrue(\Validate::isEmail('<EMAIL>'));
        $this->assertFalse(\Validate::isEmail('invalid-email'));

        // Test Czech IČO validation
        $czechValidator = new \CigCatalog\Validator\CzechValidator();
        $this->assertTrue($czechValidator->validateICO('25596641')); // Valid IČO
        $this->assertFalse($czechValidator->validateICO('12345678')); // Invalid IČO
    }

    /**
     * Test performance and optimization
     */
    public function testPerformanceOptimizations(): void
    {
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');

        // Test pagination
        $startTime = microtime(true);
        $catalogs = $repository->findAllActive(1, 10, 0); // Limit 10, offset 0
        $endTime = microtime(true);

        $this->assertIsArray($catalogs);
        $this->assertLessThanOrEqual(10, count($catalogs));
        
        // Query should be fast (less than 1 second)
        $queryTime = $endTime - $startTime;
        $this->assertLessThan(1.0, $queryTime);

        // Test caching impact
        $cacheService = self::$moduleInstance->get('cig_catalog.service.cache');
        
        $startTime = microtime(true);
        $cacheService->get('non_existent_key');
        $endTime = microtime(true);
        
        $cacheTime = $endTime - $startTime;
        $this->assertLessThan(0.1, $cacheTime); // Cache operations should be very fast
    }

    /**
     * Initialize PrestaShop environment for testing
     */
    private static function initializePrestaShop(): void
    {
        if (!defined('_PS_VERSION_')) {
            // Try to find PrestaShop config
            $configPaths = [
                __DIR__ . '/../../../../config/config.inc.php',
                __DIR__ . '/../../../config/config.inc.php',
                __DIR__ . '/../../config/config.inc.php',
            ];

            foreach ($configPaths as $configPath) {
                if (file_exists($configPath)) {
                    require_once $configPath;
                    break;
                }
            }

            if (!defined('_PS_VERSION_')) {
                self::markTestSkipped('PrestaShop environment not found');
            }
        }
    }

    /**
     * Clean up test data
     */
    private static function cleanupTestData(): void
    {
        // Clean up test catalog
        if (self::$testCatalogId) {
            $catalog = new Catalog(self::$testCatalogId);
            if (\Validate::isLoadedObject($catalog)) {
                $catalog->delete();
            }
        }

        // Clean up test order
        if (self::$testOrderId) {
            $order = new CatalogOrder(self::$testOrderId);
            if (\Validate::isLoadedObject($order)) {
                $order->delete();
            }
        }
    }
}
