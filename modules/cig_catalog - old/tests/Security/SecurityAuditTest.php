<?php
/**
 * CIG Catalog Security Audit Tests
 * 
 * Security testing and vulnerability assessment
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

declare(strict_types=1);

namespace CigCatalog\Tests\Security;

use PHPUnit\Framework\TestCase;

class SecurityAuditTest extends TestCase
{
    private static $moduleInstance;

    public static function setUpBeforeClass(): void
    {
        // Initialize PrestaShop environment
        self::initializePrestaShop();
        
        // Get module instance
        self::$moduleInstance = \Module::getInstanceByName('cig_catalog');
        
        if (!self::$moduleInstance) {
            self::markTestSkipped('CIG Catalog module is not installed');
        }
    }

    /**
     * Test SQL injection protection
     */
    public function testSqlInjectionProtection(): void
    {
        $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
        
        // Test malicious SQL injection attempts
        $maliciousInputs = [
            "'; DROP TABLE ps_catalog; --",
            "1' OR '1'='1",
            "1; DELETE FROM ps_catalog_order; --",
            "1 UNION SELECT * FROM ps_customer",
            "<script>alert('xss')</script>",
        ];
        
        foreach ($maliciousInputs as $maliciousInput) {
            try {
                // Test search functionality with malicious input
                $results = $repository->searchCatalogs($maliciousInput, 1, 10, 0);
                
                // Should return empty array or safe results, not cause SQL error
                $this->assertIsArray($results);
                
                // Test catalog retrieval with malicious ID
                $catalog = $repository->findById($maliciousInput, 1);
                
                // Should return null for invalid ID, not cause SQL error
                $this->assertNull($catalog);
                
            } catch (\Exception $e) {
                // Should not throw SQL-related exceptions
                $this->assertStringNotContainsString('SQL', $e->getMessage());
                $this->assertStringNotContainsString('mysql', strtolower($e->getMessage()));
                $this->assertStringNotContainsString('database', strtolower($e->getMessage()));
            }
        }
    }

    /**
     * Test XSS protection
     */
    public function testXssProtection(): void
    {
        $orderService = self::$moduleInstance->get('cig_catalog.service.catalog_order');
        
        // Test XSS attempts in order data
        $xssPayloads = [
            '<script>alert("xss")</script>',
            '<img src="x" onerror="alert(1)">',
            'javascript:alert("xss")',
            '<svg onload="alert(1)">',
            '"><script>alert("xss")</script>',
        ];
        
        foreach ($xssPayloads as $payload) {
            $orderData = [
                'catalog_id' => 1,
                'company_name' => $payload,
                'first_name' => $payload,
                'last_name' => 'Test',
                'email' => '<EMAIL>',
                'phone' => '+420123456789',
                'address' => $payload,
                'note' => $payload,
                'gdpr_consent' => true,
                'ip_address' => '127.0.0.1',
                'user_agent' => $payload,
                'language_id' => 1,
                'shop_id' => 1,
            ];
            
            try {
                $orderId = $orderService->createOrder($orderData);
                
                if ($orderId) {
                    // Retrieve order and check if data is properly escaped
                    $order = $orderService->getOrderById($orderId);
                    
                    // Data should not contain unescaped script tags
                    $this->assertStringNotContainsString('<script>', $order['company_name']);
                    $this->assertStringNotContainsString('<script>', $order['first_name']);
                    $this->assertStringNotContainsString('<script>', $order['address']);
                    $this->assertStringNotContainsString('<script>', $order['note']);
                    
                    // Cleanup
                    $orderEntity = new \CigCatalog\Entity\CatalogOrder($orderId);
                    if (\Validate::isLoadedObject($orderEntity)) {
                        $orderEntity->delete();
                    }
                }
                
            } catch (\Exception $e) {
                // Expected for invalid data
                $this->assertInstanceOf(\PrestaShop\PrestaShop\Core\Domain\Exception\DomainException::class, $e);
            }
        }
    }

    /**
     * Test CSRF protection
     */
    public function testCsrfProtection(): void
    {
        // Test that CSRF tokens are properly validated
        $token1 = \Tools::getToken(false);
        $token2 = \Tools::getToken(false);
        
        // Tokens should be consistent for same session
        $this->assertEquals($token1, $token2);
        $this->assertNotEmpty($token1);
        $this->assertIsString($token1);
        
        // Test invalid token handling
        $invalidTokens = [
            '',
            'invalid_token',
            '12345',
            '<script>alert("xss")</script>',
        ];
        
        foreach ($invalidTokens as $invalidToken) {
            $isValid = ($invalidToken === \Tools::getToken(false));
            $this->assertFalse($isValid, "Invalid token '{$invalidToken}' should not be accepted");
        }
    }

    /**
     * Test file upload security
     */
    public function testFileUploadSecurity(): void
    {
        $fileUploadService = self::$moduleInstance->get('cig_catalog.service.file_upload');
        
        // Test malicious file types
        $maliciousFiles = [
            ['name' => 'malicious.php', 'type' => 'application/x-php'],
            ['name' => 'script.js', 'type' => 'application/javascript'],
            ['name' => 'executable.exe', 'type' => 'application/x-executable'],
            ['name' => 'shell.sh', 'type' => 'application/x-sh'],
            ['name' => 'image.php.jpg', 'type' => 'image/jpeg'], // Double extension
            ['name' => '../../../etc/passwd', 'type' => 'text/plain'], // Path traversal
        ];
        
        foreach ($maliciousFiles as $fileData) {
            $isValid = $fileUploadService->isValidImageType($fileData['name']);
            $this->assertFalse($isValid, "Malicious file '{$fileData['name']}' should not be accepted");
            
            $isValidFile = $fileUploadService->isValidFileType($fileData['name']);
            
            // Should reject dangerous file types
            if (in_array($fileData['type'], ['application/x-php', 'application/javascript', 'application/x-executable', 'application/x-sh'])) {
                $this->assertFalse($isValidFile, "Dangerous file type '{$fileData['name']}' should not be accepted");
            }
        }
    }

    /**
     * Test input validation
     */
    public function testInputValidation(): void
    {
        // Test email validation
        $invalidEmails = [
            'invalid-email',
            '@domain.com',
            'user@',
            'user@domain',
            '<script>alert("xss")</script>@domain.com',
            '<EMAIL><script>',
        ];
        
        foreach ($invalidEmails as $email) {
            $isValid = \Validate::isEmail($email);
            $this->assertFalse($isValid, "Invalid email '{$email}' should not be accepted");
        }
        
        // Test valid emails
        $validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];
        
        foreach ($validEmails as $email) {
            $isValid = \Validate::isEmail($email);
            $this->assertTrue($isValid, "Valid email '{$email}' should be accepted");
        }
    }

    /**
     * Test access control
     */
    public function testAccessControl(): void
    {
        // Test that admin controllers require proper authentication
        $adminControllers = [
            'AdminCatalogController',
            'AdminCatalogOrderController',
            'AdminEmailConfigController',
        ];
        
        foreach ($adminControllers as $controllerName) {
            $controllerFile = self::$moduleInstance->getLocalPath() . "controllers/admin/{$controllerName}.php";
            
            if (file_exists($controllerFile)) {
                $content = file_get_contents($controllerFile);
                
                // Should extend ModuleAdminController
                $this->assertStringContainsString('ModuleAdminController', $content);
                
                // Should not contain direct database queries without validation
                $this->assertStringNotContainsString('$_GET', $content);
                $this->assertStringNotContainsString('$_POST', $content);
            }
        }
    }

    /**
     * Test rate limiting
     */
    public function testRateLimiting(): void
    {
        // Test order submission rate limiting
        $orderService = self::$moduleInstance->get('cig_catalog.service.catalog_order');
        
        $orderData = [
            'catalog_id' => 1,
            'company_name' => 'Rate Limit Test',
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '+420123456789',
            'address' => 'Test Address',
            'gdpr_consent' => true,
            'ip_address' => '*************', // Use specific IP for testing
            'user_agent' => 'Rate Limit Test',
            'language_id' => 1,
            'shop_id' => 1,
        ];
        
        $successfulOrders = 0;
        $rateLimitHit = false;
        $orderIds = [];
        
        // Try to create multiple orders rapidly
        for ($i = 0; $i < 10; $i++) {
            try {
                $orderData['email'] = "ratelimit{$i}@test.com";
                $orderId = $orderService->createOrder($orderData);
                $successfulOrders++;
                $orderIds[] = $orderId;
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'rate limit') !== false || 
                    strpos($e->getMessage(), 'too many') !== false) {
                    $rateLimitHit = true;
                    break;
                }
            }
        }
        
        // Should hit rate limit before processing too many orders
        $this->assertTrue($rateLimitHit || $successfulOrders < 10, 'Rate limiting should prevent excessive order submissions');
        
        // Cleanup
        foreach ($orderIds as $orderId) {
            $order = new \CigCatalog\Entity\CatalogOrder($orderId);
            if (\Validate::isLoadedObject($order)) {
                $order->delete();
            }
        }
    }

    /**
     * Test data sanitization
     */
    public function testDataSanitization(): void
    {
        $catalogManager = self::$moduleInstance->get('cig_catalog.service.catalog_manager');
        
        // Test data with various malicious content
        $maliciousData = [
            'name' => ['cs' => '<script>alert("xss")</script>Catalog Name'],
            'description' => ['cs' => 'Description with <img src="x" onerror="alert(1)"> XSS'],
            'short_description' => ['cs' => 'Short desc with javascript:alert("xss")'],
            'active' => true,
        ];
        
        try {
            $catalogId = $catalogManager->createCatalog($maliciousData);
            
            if ($catalogId) {
                // Retrieve catalog and verify data is sanitized
                $repository = self::$moduleInstance->get('cig_catalog.repository.catalog');
                $catalog = $repository->findById($catalogId, 1);
                
                // Should not contain script tags or javascript: protocols
                $this->assertStringNotContainsString('<script>', $catalog['name']);
                $this->assertStringNotContainsString('<img', $catalog['description']);
                $this->assertStringNotContainsString('javascript:', $catalog['short_description']);
                
                // Cleanup
                $catalogEntity = new \CigCatalog\Entity\Catalog($catalogId);
                if (\Validate::isLoadedObject($catalogEntity)) {
                    $catalogEntity->delete();
                }
            }
            
        } catch (\Exception $e) {
            // Expected for invalid data
            $this->assertInstanceOf(\PrestaShop\PrestaShop\Core\Domain\Exception\DomainException::class, $e);
        }
    }

    /**
     * Test configuration security
     */
    public function testConfigurationSecurity(): void
    {
        // Test that sensitive configuration is not exposed
        $sensitiveKeys = [
            'smtp_password',
            'api_key',
            'secret_key',
        ];
        
        foreach ($sensitiveKeys as $key) {
            $value = \CigCatalog\Entity\CatalogConfig::get($key, '');
            
            // Sensitive values should not be returned in plain text in logs or debug output
            if (!empty($value)) {
                $this->assertNotEquals($value, print_r($value, true));
            }
        }
    }

    /**
     * Initialize PrestaShop environment
     */
    private static function initializePrestaShop(): void
    {
        if (!defined('_PS_VERSION_')) {
            $configPaths = [
                __DIR__ . '/../../../../config/config.inc.php',
                __DIR__ . '/../../../config/config.inc.php',
                __DIR__ . '/../../config/config.inc.php',
            ];

            foreach ($configPaths as $configPath) {
                if (file_exists($configPath)) {
                    require_once $configPath;
                    break;
                }
            }

            if (!defined('_PS_VERSION_')) {
                self::markTestSkipped('PrestaShop environment not found');
            }
        }
    }
}
