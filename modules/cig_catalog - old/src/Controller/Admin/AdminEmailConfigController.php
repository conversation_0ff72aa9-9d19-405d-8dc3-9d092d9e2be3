<?php
/**
 * CIG Catalog Email Configuration Controller
 * 
 * Handles email configuration and testing
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

use PrestaShop\PrestaShop\Adapter\SymfonyContainer;
use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class AdminEmailConfigController extends ModuleAdminController
{
    /** @var string */
    public $bootstrap = true;
    
    /** @var EmailService */
    private $emailService;
    
    /** @var EmailTemplateManager */
    private $templateManager;

    /**
     * Initialize controller
     */
    public function __construct()
    {
        $this->table = 'cig_catalog_config';
        $this->className = 'CatalogConfig';
        $this->lang = false;
        $this->bootstrap = true;
        
        parent::__construct();
        
        $this->meta_title = $this->trans('Email Configuration', [], 'Modules.Cigcatalog.Admin');
        
        // Initialize services
        $this->emailService = $this->module->get('cig_catalog.service.email');
        $this->templateManager = $this->module->get('cig_catalog.service.email_template_manager');
    }

    /**
     * Main configuration page
     */
    public function renderView()
    {
        // Get current configuration
        $config = $this->getCurrentEmailConfig();
        
        // Get available templates
        $templates = $this->templateManager->getAvailableTemplates();
        
        // Get email statistics
        $stats = $this->emailService->getEmailStatistics();
        
        $this->context->smarty->assign([
            'config' => $config,
            'templates' => $templates,
            'stats' => $stats,
            'test_email_url' => $this->context->link->getAdminLink('AdminEmailConfig', true, [], ['action' => 'test']),
            'save_config_url' => $this->context->link->getAdminLink('AdminEmailConfig', true, [], ['action' => 'save']),
            'template_preview_url' => $this->context->link->getAdminLink('AdminEmailConfig', true, [], ['action' => 'preview']),
        ]);
        
        return $this->context->smarty->fetch($this->getTemplatePath() . 'email_config.tpl');
    }

    /**
     * Handle configuration save
     */
    public function processConfiguration()
    {
        if (Tools::isSubmit('submitEmailConfig')) {
            try {
                $this->saveEmailConfiguration();
                $this->confirmations[] = $this->trans('Email configuration saved successfully.', [], 'Modules.Cigcatalog.Admin');
            } catch (Exception $e) {
                $this->errors[] = $e->getMessage();
            }
        }
    }

    /**
     * AJAX test email functionality
     */
    public function displayAjaxTest()
    {
        try {
            $testEmail = Tools::getValue('test_email');
            
            if (empty($testEmail) || !Validate::isEmail($testEmail)) {
                return $this->ajaxResponse(['error' => 'Invalid email address'], 400);
            }
            
            // Test email configuration
            $testResults = $this->emailService->testEmailConfiguration();
            
            if (!empty($testResults['errors'])) {
                return $this->ajaxResponse([
                    'error' => 'Email configuration test failed',
                    'details' => $testResults['errors']
                ], 400);
            }
            
            // Send test email
            $success = $this->sendTestEmail($testEmail);
            
            if ($success) {
                return $this->ajaxResponse([
                    'success' => true,
                    'message' => 'Test email sent successfully to ' . $testEmail
                ]);
            } else {
                return $this->ajaxResponse(['error' => 'Failed to send test email'], 500);
            }
            
        } catch (Exception $e) {
            return $this->ajaxResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * AJAX template preview
     */
    public function displayAjaxPreview()
    {
        try {
            $templateName = Tools::getValue('template');
            
            if (empty($templateName)) {
                return $this->ajaxResponse(['error' => 'Template name is required'], 400);
            }
            
            // Get sample data for preview
            $sampleData = $this->getSampleTemplateData($templateName);
            
            // Render template
            $content = $this->templateManager->renderTemplate($templateName, $sampleData);
            
            return $this->ajaxResponse([
                'success' => true,
                'content' => $content
            ]);
            
        } catch (Exception $e) {
            return $this->ajaxResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get current email configuration
     */
    private function getCurrentEmailConfig(): array
    {
        return [
            'admin_email' => CatalogConfig::get('admin_email', Configuration::get('PS_SHOP_EMAIL')),
            'from_name' => CatalogConfig::get('from_name', Configuration::get('PS_SHOP_NAME')),
            'from_email' => CatalogConfig::get('from_email', Configuration::get('PS_SHOP_EMAIL')),
            'smtp_enabled' => (bool) CatalogConfig::get('smtp_enabled', false),
            'smtp_host' => CatalogConfig::get('smtp_host', ''),
            'smtp_port' => (int) CatalogConfig::get('smtp_port', 587),
            'smtp_username' => CatalogConfig::get('smtp_username', ''),
            'smtp_password' => CatalogConfig::get('smtp_password', ''),
            'smtp_encryption' => CatalogConfig::get('smtp_encryption', 'tls'),
            'enable_order_notifications' => (bool) CatalogConfig::get('enable_order_notifications', true),
            'enable_order_confirmations' => (bool) CatalogConfig::get('enable_order_confirmations', true),
            'enable_admin_alerts' => (bool) CatalogConfig::get('enable_admin_alerts', true),
            'notification_frequency' => CatalogConfig::get('notification_frequency', 'immediate'),
        ];
    }

    /**
     * Save email configuration
     */
    private function saveEmailConfiguration(): void
    {
        $config = [
            'admin_email' => Tools::getValue('admin_email'),
            'from_name' => Tools::getValue('from_name'),
            'from_email' => Tools::getValue('from_email'),
            'smtp_enabled' => (bool) Tools::getValue('smtp_enabled'),
            'smtp_host' => Tools::getValue('smtp_host'),
            'smtp_port' => (int) Tools::getValue('smtp_port'),
            'smtp_username' => Tools::getValue('smtp_username'),
            'smtp_password' => Tools::getValue('smtp_password'),
            'smtp_encryption' => Tools::getValue('smtp_encryption'),
            'enable_order_notifications' => (bool) Tools::getValue('enable_order_notifications'),
            'enable_order_confirmations' => (bool) Tools::getValue('enable_order_confirmations'),
            'enable_admin_alerts' => (bool) Tools::getValue('enable_admin_alerts'),
            'notification_frequency' => Tools::getValue('notification_frequency'),
        ];
        
        // Validate configuration
        $this->validateEmailConfiguration($config);
        
        // Save configuration
        foreach ($config as $key => $value) {
            CatalogConfig::set($key, $value);
        }
    }

    /**
     * Validate email configuration
     */
    private function validateEmailConfiguration(array $config): void
    {
        $errors = [];
        
        // Validate admin email
        if (empty($config['admin_email']) || !Validate::isEmail($config['admin_email'])) {
            $errors[] = 'Invalid admin email address';
        }
        
        // Validate from email
        if (empty($config['from_email']) || !Validate::isEmail($config['from_email'])) {
            $errors[] = 'Invalid from email address';
        }
        
        // Validate SMTP settings if enabled
        if ($config['smtp_enabled']) {
            if (empty($config['smtp_host'])) {
                $errors[] = 'SMTP host is required when SMTP is enabled';
            }
            
            if ($config['smtp_port'] <= 0 || $config['smtp_port'] > 65535) {
                $errors[] = 'Invalid SMTP port number';
            }
            
            if (empty($config['smtp_username'])) {
                $errors[] = 'SMTP username is required when SMTP is enabled';
            }
            
            if (!in_array($config['smtp_encryption'], ['none', 'ssl', 'tls'])) {
                $errors[] = 'Invalid SMTP encryption method';
            }
        }
        
        if (!empty($errors)) {
            throw new DomainException(implode(', ', $errors));
        }
    }

    /**
     * Send test email
     */
    private function sendTestEmail(string $email): bool
    {
        $subject = 'Test Email from CIG Catalog Module';
        $templateVars = [
            'shop_name' => Configuration::get('PS_SHOP_NAME'),
            'date' => date('d.m.Y H:i'),
            'test_message' => 'This is a test email to verify your email configuration is working correctly.',
        ];
        
        $htmlContent = $this->templateManager->renderTemplate('test_email', $templateVars);
        
        return $this->emailService->sendEmail(
            $email,
            'Test Recipient',
            $subject,
            $htmlContent
        );
    }

    /**
     * Get sample data for template preview
     */
    private function getSampleTemplateData(string $templateName): array
    {
        $baseData = [
            'shop_name' => Configuration::get('PS_SHOP_NAME'),
            'shop_email' => Configuration::get('PS_SHOP_EMAIL'),
            'date' => date('d.m.Y H:i'),
        ];
        
        switch ($templateName) {
            case 'order_notification':
            case 'order_confirmation':
                return array_merge($baseData, [
                    'order' => [
                        'id_order' => 12345,
                        'first_name' => 'Jan',
                        'last_name' => 'Novák',
                        'email' => '<EMAIL>',
                        'phone' => '+*********** 789',
                        'address' => 'Testovací ulice 123, 110 00 Praha',
                        'company_name' => 'Test s.r.o.',
                        'company_id' => '12345678',
                        'note' => 'Prosím o rychlé doručení.',
                    ],
                    'catalog' => [
                        'title' => 'Ukázkový katalog produktů',
                        'description' => 'Kompletní přehled našich nejnovějších produktů a služeb.',
                    ],
                ]);
                
            case 'admin_alert':
                return array_merge($baseData, [
                    'type' => 'warning',
                    'data' => [
                        'message' => 'Ukázkové admin upozornění',
                        'details' => 'Toto je ukázka admin upozornění pro testování šablony.',
                    ],
                    'server_info' => [
                        'php_version' => PHP_VERSION,
                        'prestashop_version' => _PS_VERSION_,
                        'module_version' => '1.0.0',
                    ],
                ]);
                
            default:
                return $baseData;
        }
    }

    /**
     * Get template path
     */
    private function getTemplatePath(): string
    {
        return $this->module->getLocalPath() . 'views/templates/admin/email/';
    }

    /**
     * Create AJAX response
     */
    private function ajaxResponse(array $data, int $statusCode = 200): JsonResponse
    {
        $response = new JsonResponse($data, $statusCode);
        $response->headers->set('Content-Type', 'application/json');
        
        return $response;
    }
}
