<?php
/**
 * Health Check Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Utility;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class HealthController extends FrameworkBundleAdminController
{
    /**
     * Health check endpoint
     */
    public function checkAction(Request $request): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'files' => $this->checkFiles(),
            'permissions' => $this->checkPermissions(),
            'services' => $this->checkServices()
        ];

        $allHealthy = array_reduce($checks, function($carry, $check) {
            return $carry && $check['status'] === 'ok';
        }, true);

        return new JsonResponse([
            'status' => $allHealthy ? 'healthy' : 'unhealthy',
            'timestamp' => date('c'),
            'checks' => $checks
        ], $allHealthy ? 200 : 503);
    }

    private function checkDatabase(): array
    {
        try {
            $db = \Db::getInstance();
            $result = $db->getValue('SELECT 1');
            
            return [
                'status' => $result ? 'ok' : 'error',
                'message' => $result ? 'Database connection OK' : 'Database connection failed'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ];
        }
    }

    private function checkFiles(): array
    {
        $uploadDir = _PS_MODULE_DIR_ . 'cig_catalog/uploads/';
        $imageDir = $uploadDir . 'images/';
        $fileDir = $uploadDir . 'files/';

        $checks = [
            'upload_dir' => is_dir($uploadDir),
            'image_dir' => is_dir($imageDir),
            'file_dir' => is_dir($fileDir)
        ];

        $allOk = array_reduce($checks, function($carry, $check) {
            return $carry && $check;
        }, true);

        return [
            'status' => $allOk ? 'ok' : 'error',
            'message' => $allOk ? 'All directories exist' : 'Some directories missing',
            'details' => $checks
        ];
    }

    private function checkPermissions(): array
    {
        $uploadDir = _PS_MODULE_DIR_ . 'cig_catalog/uploads/';
        
        $writable = is_writable($uploadDir);

        return [
            'status' => $writable ? 'ok' : 'error',
            'message' => $writable ? 'Upload directory writable' : 'Upload directory not writable'
        ];
    }

    private function checkServices(): array
    {
        try {
            // Check if main services are available
            $container = $this->container;
            $services = [
                'cig_catalog.repository.catalog',
                'cig_catalog.service.catalog_manager',
                'cig_catalog.service.file_manager'
            ];

            $available = [];
            foreach ($services as $service) {
                try {
                    $available[$service] = $container->has($service);
                } catch (\Exception $e) {
                    $available[$service] = false;
                }
            }

            $allAvailable = array_reduce($available, function($carry, $check) {
                return $carry && $check;
            }, true);

            return [
                'status' => $allAvailable ? 'ok' : 'error',
                'message' => $allAvailable ? 'All services available' : 'Some services missing',
                'details' => $available
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Service check failed: ' . $e->getMessage()
            ];
        }
    }
}
