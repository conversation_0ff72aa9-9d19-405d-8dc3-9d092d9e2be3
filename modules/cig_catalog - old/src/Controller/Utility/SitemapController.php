<?php
/**
 * Sitemap Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Utility;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use CigCatalog\Repository\CatalogRepository;

class SitemapController extends FrameworkBundleAdminController
{
    private CatalogRepository $catalogRepository;

    public function __construct(CatalogRepository $catalogRepository)
    {
        $this->catalogRepository = $catalogRepository;
    }

    /**
     * Generate sitemap.xml
     */
    public function indexAction(Request $request): Response
    {
        try {
            $catalogs = $this->catalogRepository->findAllActive();
            
            $xml = $this->generateSitemapXml($catalogs);

            $response = new Response($xml);
            $response->headers->set('Content-Type', 'application/xml');
            
            return $response;
        } catch (\Exception $e) {
            return new Response('<?xml version="1.0" encoding="UTF-8"?><urlset></urlset>', 500, [
                'Content-Type' => 'application/xml'
            ]);
        }
    }

    private function generateSitemapXml(array $catalogs): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add catalog index page
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($this->generateUrl('catalog_index', ['_locale' => 'cs'])) . '</loc>' . "\n";
        $xml .= '    <changefreq>daily</changefreq>' . "\n";
        $xml .= '    <priority>0.8</priority>' . "\n";
        $xml .= '  </url>' . "\n";

        // Add individual catalog pages
        foreach ($catalogs as $catalog) {
            if (!empty($catalog['slug'])) {
                $xml .= '  <url>' . "\n";
                $xml .= '    <loc>' . htmlspecialchars($this->generateUrl('catalog_detail', [
                    '_locale' => 'cs',
                    'slug' => $catalog['slug']
                ])) . '</loc>' . "\n";
                $xml .= '    <lastmod>' . date('c', strtotime($catalog['date_upd'])) . '</lastmod>' . "\n";
                $xml .= '    <changefreq>weekly</changefreq>' . "\n";
                $xml .= '    <priority>0.6</priority>' . "\n";
                $xml .= '  </url>' . "\n";
            }
        }

        $xml .= '</urlset>';

        return $xml;
    }
}
