<?php
/**
 * Frontend Catalog Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Front;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Service\CatalogManager;
use CigCatalog\Service\FileManager;

class CatalogController extends FrameworkBundleAdminController
{
    private CatalogRepository $catalogRepository;
    private CatalogManager $catalogManager;
    private FileManager $fileManager;

    public function __construct(
        CatalogRepository $catalogRepository,
        CatalogManager $catalogManager,
        FileManager $fileManager
    ) {
        $this->catalogRepository = $catalogRepository;
        $this->catalogManager = $catalogManager;
        $this->fileManager = $fileManager;
    }

    /**
     * Catalog listing page
     */
    public function indexAction(Request $request): Response
    {
        try {
            $page = max(1, $request->query->getInt('page', 1));
            $perPage = 12;
            $search = $request->query->get('search', '');
            $langId = $this->getContext()->language->id;

            if ($search) {
                $catalogs = $this->catalogRepository->search($search, $langId, $page, $perPage);
                $totalCatalogs = $this->catalogRepository->countSearch($search, $langId);
            } else {
                $catalogs = $this->catalogRepository->findWithPagination($page, $perPage, $langId);
                $totalCatalogs = $this->catalogRepository->count();
            }

            $totalPages = ceil($totalCatalogs / $perPage);

            return $this->render('@Modules/cig_catalog/templates/front/catalog/index.html.twig', [
                'catalogs' => $catalogs,
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'totalCatalogs' => $totalCatalogs,
                'search' => $search,
                'perPage' => $perPage
            ]);
        } catch (\Exception $e) {
            return $this->render('@Modules/cig_catalog/templates/front/error.html.twig', [
                'message' => 'Chyba při načítání katalogů.'
            ]);
        }
    }

    /**
     * Catalog detail page
     */
    public function detailAction(Request $request, string $slug): Response
    {
        try {
            $langId = $this->getContext()->language->id;
            $catalog = $this->catalogRepository->findBySlug($slug, $langId);

            if (!$catalog) {
                throw $this->createNotFoundException('Katalog nebyl nalezen.');
            }

            return $this->render('@Modules/cig_catalog/templates/front/catalog/detail.html.twig', [
                'catalog' => $catalog
            ]);
        } catch (\Exception $e) {
            return $this->render('@Modules/cig_catalog/templates/front/error.html.twig', [
                'message' => 'Katalog nebyl nalezen.'
            ]);
        }
    }

    /**
     * Download catalog file
     */
    public function downloadAction(Request $request, int $id): Response
    {
        try {
            $catalog = $this->catalogRepository->findById($id);
            
            if (!$catalog || !$catalog->active) {
                throw $this->createNotFoundException('Katalog nebyl nalezen.');
            }

            // Increment download count
            $this->catalogRepository->incrementDownloadCount($id);

            // If it's a URL, redirect
            if ($catalog->catalog_url) {
                return $this->redirect($catalog->catalog_url);
            }

            // If it's a file, serve it
            if ($catalog->file_path) {
                $filePath = $this->fileManager->getFilePath($catalog->file_path);
                
                if (!file_exists($filePath)) {
                    throw $this->createNotFoundException('Soubor nebyl nalezen.');
                }

                $response = new BinaryFileResponse($filePath);
                $response->setContentDisposition(
                    ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                    basename($catalog->file_path)
                );

                return $response;
            }

            throw $this->createNotFoundException('Soubor není k dispozici.');
        } catch (\Exception $e) {
            return $this->render('@Modules/cig_catalog/templates/front/error.html.twig', [
                'message' => 'Chyba při stahování souboru.'
            ]);
        }
    }
}
