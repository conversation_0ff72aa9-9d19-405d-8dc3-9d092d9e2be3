<?php
/**
 * Order API Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Api;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use CigCatalog\Validator\OrderValidator;

class OrderApiController extends FrameworkBundleAdminController
{
    private OrderValidator $orderValidator;

    public function __construct(OrderValidator $orderValidator)
    {
        $this->orderValidator = $orderValidator;
    }

    /**
     * Validate order data
     */
    public function validateAction(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Neplatný formát dat.'
                ], 400);
            }

            $validationResult = $this->orderValidator->validate($data);

            return new JsonResponse([
                'success' => $validationResult['valid'],
                'errors' => $validationResult['errors'] ?? []
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při validaci: ' . $e->getMessage()
            ], 500);
        }
    }
}
