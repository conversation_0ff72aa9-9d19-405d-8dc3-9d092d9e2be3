<?php
/**
 * Catalog API Controller
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Controller\Api;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Service\CatalogManager;

class CatalogApiController extends FrameworkBundleAdminController
{
    private CatalogRepository $catalogRepository;
    private CatalogManager $catalogManager;

    public function __construct(
        CatalogRepository $catalogRepository,
        CatalogManager $catalogManager
    ) {
        $this->catalogRepository = $catalogRepository;
        $this->catalogManager = $catalogManager;
    }

    /**
     * Get catalog list
     */
    public function listAction(Request $request): JsonResponse
    {
        try {
            $page = max(1, $request->query->getInt('page', 1));
            $perPage = min(50, max(1, $request->query->getInt('per_page', 10)));
            $langId = $request->query->getInt('lang_id', $this->getContext()->language->id);

            $catalogs = $this->catalogRepository->findWithPagination($page, $perPage, $langId);
            $totalCatalogs = $this->catalogRepository->count();

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'catalogs' => $catalogs,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $totalCatalogs,
                        'total_pages' => ceil($totalCatalogs / $perPage)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při načítání katalogů: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search catalogs
     */
    public function searchAction(Request $request): JsonResponse
    {
        try {
            $query = $request->query->get('q', '');
            $page = max(1, $request->query->getInt('page', 1));
            $perPage = min(50, max(1, $request->query->getInt('per_page', 10)));
            $langId = $request->query->getInt('lang_id', $this->getContext()->language->id);

            if (empty($query)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Vyhledávací dotaz je povinný.'
                ], 400);
            }

            $catalogs = $this->catalogRepository->search($query, $langId, $page, $perPage);
            $totalCatalogs = $this->catalogRepository->countSearch($query, $langId);

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'catalogs' => $catalogs,
                    'query' => $query,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $totalCatalogs,
                        'total_pages' => ceil($totalCatalogs / $perPage)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Chyba při vyhledávání: ' . $e->getMessage()
            ], 500);
        }
    }
}
