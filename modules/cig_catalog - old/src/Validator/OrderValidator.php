<?php
/**
 * Order Validator
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Validator;

use Symfony\Contracts\Translation\TranslatorInterface;

class OrderValidator
{
    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * Validate order data
     */
    public function validate(array $data): array
    {
        $errors = [];

        // Required fields
        $requiredFields = [
            'catalog_id' => 'ID katalogu',
            'company_name' => 'Název společnosti',
            'first_name' => 'J<PERSON><PERSON>',
            'last_name' => 'Příjmení',
            'email' => 'Email',
            'address' => 'Adresa',
            'gdpr_consent' => 'Souhlas se zpracováním osobních ú<PERSON>'
        ];

        foreach ($requiredFields as $field => $label) {
            if (empty($data[$field])) {
                $errors[$field] = sprintf('Pole "%s" je povinné.', $label);
            }
        }

        // Validate specific fields
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Neplatný formát emailové adresy.';
        }

        if (!empty($data['phone']) && !$this->validatePhone($data['phone'])) {
            $errors['phone'] = 'Neplatný formát telefonního čísla.';
        }

        if (!empty($data['company_id']) && !$this->validateCompanyId($data['company_id'])) {
            $errors['company_id'] = 'Neplatný formát IČO.';
        }

        if (isset($data['catalog_id']) && (!is_numeric($data['catalog_id']) || $data['catalog_id'] <= 0)) {
            $errors['catalog_id'] = 'Neplatné ID katalogu.';
        }

        if (empty($data['gdpr_consent']) || $data['gdpr_consent'] !== true) {
            $errors['gdpr_consent'] = 'Musíte souhlasit se zpracováním osobních údajů.';
        }

        // Validate text lengths
        $lengthLimits = [
            'company_name' => 255,
            'first_name' => 100,
            'last_name' => 100,
            'email' => 255,
            'phone' => 50,
            'company_id' => 20,
            'note' => 1000
        ];

        foreach ($lengthLimits as $field => $maxLength) {
            if (!empty($data[$field]) && strlen($data[$field]) > $maxLength) {
                $errors[$field] = sprintf('Pole "%s" může mít maximálně %d znaků.', $field, $maxLength);
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate phone number
     */
    private function validatePhone(string $phone): bool
    {
        // Remove spaces and common separators
        $phone = preg_replace('/[\s\-\(\)\.\/]/', '', $phone);
        
        // Check if it contains only numbers and + at the beginning
        return preg_match('/^\+?[0-9]{9,15}$/', $phone);
    }

    /**
     * Validate Czech company ID (IČO)
     */
    private function validateCompanyId(string $companyId): bool
    {
        // Remove spaces
        $companyId = str_replace(' ', '', $companyId);
        
        // Check if it's 8 digits
        if (!preg_match('/^[0-9]{8}$/', $companyId)) {
            return false;
        }

        // Validate checksum
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int)$companyId[$i] * (8 - $i);
        }
        
        $remainder = $sum % 11;
        $checkDigit = $remainder < 2 ? $remainder : 11 - $remainder;
        
        return $checkDigit == (int)$companyId[7];
    }

    /**
     * Sanitize input data
     */
    public function sanitize(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Basic sanitization
                $value = trim($value);
                $value = strip_tags($value);
                
                // Special handling for specific fields
                switch ($key) {
                    case 'email':
                        $value = filter_var($value, FILTER_SANITIZE_EMAIL);
                        break;
                    case 'phone':
                        $value = preg_replace('/[^0-9\+\-\(\)\s]/', '', $value);
                        break;
                    case 'company_id':
                        $value = preg_replace('/[^0-9]/', '', $value);
                        break;
                    case 'note':
                        // Allow basic HTML for notes
                        $value = strip_tags($value, '<p><br><strong><em><ul><ol><li>');
                        break;
                }
            }
            
            $sanitized[$key] = $value;
        }
        
        return $sanitized;
    }
}
