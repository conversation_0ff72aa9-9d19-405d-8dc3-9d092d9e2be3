<?php
/**
 * Configuration Service
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Service;

use CigCatalog\Repository\CatalogConfigRepository;

class ConfigurationService
{
    private CatalogConfigRepository $configRepository;
    private CacheService $cacheService;

    public function __construct(
        CatalogConfigRepository $configRepository,
        CacheService $cacheService
    ) {
        $this->configRepository = $configRepository;
        $this->cacheService = $cacheService;
    }

    /**
     * Get configuration value
     */
    public function get(string $key, $default = null)
    {
        $cacheKey = 'config_' . $key;
        
        // Try to get from cache first
        $value = $this->cacheService->get($cacheKey);
        
        if ($value === null) {
            $value = $this->configRepository->getValue($key);
            
            if ($value !== null) {
                $this->cacheService->set($cacheKey, $value, 3600); // Cache for 1 hour
            }
        }
        
        return $value !== null ? $value : $default;
    }

    /**
     * Set configuration value
     */
    public function set(string $key, $value): bool
    {
        $result = $this->configRepository->setValue($key, $value);
        
        if ($result) {
            // Clear cache
            $cacheKey = 'config_' . $key;
            $this->cacheService->delete($cacheKey);
        }
        
        return $result;
    }

    /**
     * Get multiple configuration values
     */
    public function getMultiple(array $keys): array
    {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = $this->get($key);
        }
        
        return $result;
    }

    /**
     * Set multiple configuration values
     */
    public function setMultiple(array $values): bool
    {
        $success = true;
        
        foreach ($values as $key => $value) {
            if (!$this->set($key, $value)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Delete configuration value
     */
    public function delete(string $key): bool
    {
        $result = $this->configRepository->deleteValue($key);
        
        if ($result) {
            // Clear cache
            $cacheKey = 'config_' . $key;
            $this->cacheService->delete($cacheKey);
        }
        
        return $result;
    }

    /**
     * Get all configuration values
     */
    public function getAll(): array
    {
        return $this->configRepository->getAllValues();
    }

    /**
     * Get email configuration
     */
    public function getEmailConfiguration(): array
    {
        return $this->getMultiple([
            'email_enabled',
            'email_from_name',
            'email_from_address',
            'email_recipients',
            'email_subject_prefix',
            'email_template_order_created',
            'email_template_order_updated',
            'smtp_enabled',
            'smtp_host',
            'smtp_port',
            'smtp_username',
            'smtp_password',
            'smtp_encryption'
        ]);
    }

    /**
     * Set email configuration
     */
    public function setEmailConfiguration(array $config): bool
    {
        $emailConfig = [];
        
        foreach ($config as $key => $value) {
            if (strpos($key, 'email_') === 0 || strpos($key, 'smtp_') === 0) {
                $emailConfig[$key] = $value;
            }
        }
        
        return $this->setMultiple($emailConfig);
    }

    /**
     * Get frontend configuration
     */
    public function getFrontendConfiguration(): array
    {
        return $this->getMultiple([
            'items_per_page',
            'enable_search',
            'enable_ordering',
            'show_download_count',
            'new_badge_days',
            'enable_pagination',
            'enable_thumbnails'
        ]);
    }

    /**
     * Get admin configuration
     */
    public function getAdminConfiguration(): array
    {
        return $this->getMultiple([
            'admin_items_per_page',
            'enable_bulk_operations',
            'enable_drag_drop',
            'auto_approve_orders',
            'enable_statistics',
            'enable_email_notifications'
        ]);
    }

    /**
     * Initialize default configuration
     */
    public function initializeDefaults(): bool
    {
        $defaults = [
            // Frontend settings
            'items_per_page' => 12,
            'enable_search' => true,
            'enable_ordering' => true,
            'show_download_count' => true,
            'new_badge_days' => 30,
            'enable_pagination' => true,
            'enable_thumbnails' => true,
            
            // Admin settings
            'admin_items_per_page' => 20,
            'enable_bulk_operations' => true,
            'enable_drag_drop' => true,
            'auto_approve_orders' => false,
            'enable_statistics' => true,
            'enable_email_notifications' => true,
            
            // Email settings
            'email_enabled' => true,
            'email_from_name' => 'CIG Catalog',
            'email_subject_prefix' => '[CIG Catalog]',
            'smtp_enabled' => false,
            'smtp_port' => 587,
            'smtp_encryption' => 'tls'
        ];
        
        foreach ($defaults as $key => $value) {
            // Only set if not already exists
            if ($this->get($key) === null) {
                $this->set($key, $value);
            }
        }
        
        return true;
    }
}
