<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;

/**
 * CacheService - správa cache pro modul
 */
class CacheService
{
    private string $cacheDir;
    private string $prefix;
    private int $defaultTtl;

    // Cache strategie podle typu dat
    private array $cacheStrategies = [
        'catalog_list' => 3600,      // 1 hodina
        'catalog_detail' => 7200,    // 2 hodiny
        'configuration' => 86400,    // 24 hodin
        'image_metadata' => 604800,  // 1 týden
        'statistics' => 1800,        // 30 minut
        'order_data' => 900,         // 15 minut
        'user_session' => 3600       // 1 hodina
    ];

    public function __construct(string $moduleDir, string $prefix = 'cig_catalog_', int $defaultTtl = 3600)
    {
        $this->cacheDir = $moduleDir . '/cache';
        $this->prefix = $prefix;
        $this->defaultTtl = $defaultTtl;
        
        $this->ensureCacheDirectoryExists();
    }

    /**
     * <PERSON>ís<PERSON><PERSON> hodnotu z cache
     */
    public function get(string $key): mixed
    {
        $cacheKey = $this->getCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = $this->readCacheFile($cacheFile);
        
        if ($cacheData === null) {
            return null;
        }
        
        // Kontrola TTL
        if ($cacheData['expires'] < time()) {
            $this->delete($key);
            return null;
        }
        
        return $cacheData['data'];
    }

    /**
     * Uloží hodnotu do cache
     */
    public function set(string $key, mixed $value, ?int $ttl = null): bool
    {
        $cacheKey = $this->getCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if ($ttl === null) {
            $ttl = $this->getDefaultTtlForKey($key);
        }
        
        $cacheData = [
            'data' => $value,
            'created' => time(),
            'expires' => time() + $ttl,
            'key' => $key
        ];
        
        return $this->writeCacheFile($cacheFile, $cacheData);
    }

    /**
     * Smaže hodnotu z cache
     */
    public function delete(string $key): bool
    {
        $cacheKey = $this->getCacheKey($key);
        $cacheFile = $this->getCacheFilePath($cacheKey);
        
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        
        return true;
    }

    /**
     * Vyčistí cache podle patternu
     */
    public function clear(string $pattern = '*'): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        $files = glob($this->cacheDir . '/' . $this->prefix . '*');
        $cleared = 0;
        
        foreach ($files as $file) {
            $filename = basename($file);
            if (preg_match('/^' . $this->prefix . $pattern . '/', $filename)) {
                if (unlink($file)) {
                    $cleared++;
                }
            }
        }
        
        return $cleared > 0;
    }

    /**
     * Vyčistí celou cache
     */
    public function flush(): bool
    {
        return $this->clear('*');
    }

    /**
     * Získá informace o cache
     */
    public function getInfo(): array
    {
        $files = glob($this->cacheDir . '/' . $this->prefix . '*');
        $totalSize = 0;
        $totalFiles = 0;
        $expiredFiles = 0;
        $validFiles = 0;
        
        foreach ($files as $file) {
            $totalFiles++;
            $totalSize += filesize($file);
            
            $cacheData = $this->readCacheFile($file);
            if ($cacheData && $cacheData['expires'] < time()) {
                $expiredFiles++;
            } else {
                $validFiles++;
            }
        }
        
        return [
            'total_files' => $totalFiles,
            'valid_files' => $validFiles,
            'expired_files' => $expiredFiles,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'cache_dir' => $this->cacheDir
        ];
    }

    /**
     * Vyčistí expirované cache soubory
     */
    public function cleanupExpired(): int
    {
        $files = glob($this->cacheDir . '/' . $this->prefix . '*');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $cacheData = $this->readCacheFile($file);
            if ($cacheData && $cacheData['expires'] < time()) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }

    /**
     * Získá seznam všech cache klíčů
     */
    public function getKeys(): array
    {
        $files = glob($this->cacheDir . '/' . $this->prefix . '*');
        $keys = [];
        
        foreach ($files as $file) {
            $cacheData = $this->readCacheFile($file);
            if ($cacheData) {
                $keys[] = [
                    'key' => $cacheData['key'],
                    'created' => $cacheData['created'],
                    'expires' => $cacheData['expires'],
                    'size' => filesize($file),
                    'expired' => $cacheData['expires'] < time()
                ];
            }
        }
        
        return $keys;
    }

    /**
     * Získá nebo nastaví hodnotu (cache-aside pattern)
     */
    public function remember(string $key, callable $callback, ?int $ttl = null): mixed
    {
        $value = $this->get($key);
        
        if ($value === null) {
            $value = $callback();
            $this->set($key, $value, $ttl);
        }
        
        return $value;
    }

    /**
     * Inkrementuje hodnotu v cache
     */
    public function increment(string $key, int $value = 1): int
    {
        $current = $this->get($key) ?? 0;
        $new = $current + $value;
        $this->set($key, $new);
        return $new;
    }

    /**
     * Dekrementuje hodnotu v cache
     */
    public function decrement(string $key, int $value = 1): int
    {
        $current = $this->get($key) ?? 0;
        $new = max(0, $current - $value);
        $this->set($key, $new);
        return $new;
    }

    /**
     * Získá cache klíč s prefixem
     */
    private function getCacheKey(string $key): string
    {
        return $this->prefix . md5($key);
    }

    /**
     * Získá cestu k cache souboru
     */
    private function getCacheFilePath(string $cacheKey): string
    {
        return $this->cacheDir . '/' . $cacheKey . '.cache';
    }

    /**
     * Přečte cache soubor
     */
    private function readCacheFile(string $filePath): ?array
    {
        if (!file_exists($filePath)) {
            return null;
        }
        
        try {
            $content = file_get_contents($filePath);
            if ($content === false) {
                return null;
            }
            
            $data = unserialize($content);
            if ($data === false) {
                // Pokud se nepodaří deserializovat, smažeme soubor
                unlink($filePath);
                return null;
            }
            
            return $data;
        } catch (\Exception $e) {
            // Chyba při čtení, smažeme soubor
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            return null;
        }
    }

    /**
     * Zapíše cache soubor
     */
    private function writeCacheFile(string $filePath, array $data): bool
    {
        try {
            $content = serialize($data);
            $tempFile = $filePath . '.tmp';
            
            // Atomické zápis přes dočasný soubor
            if (file_put_contents($tempFile, $content, LOCK_EX) !== false) {
                return rename($tempFile, $filePath);
            }
            
            return false;
        } catch (\Exception $e) {
            // Vyčištění dočasného souboru při chybě
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
            return false;
        }
    }

    /**
     * Získá výchozí TTL pro klíč podle strategie
     */
    private function getDefaultTtlForKey(string $key): int
    {
        foreach ($this->cacheStrategies as $pattern => $ttl) {
            if (strpos($key, $pattern) === 0) {
                return $ttl;
            }
        }

        return $this->defaultTtl;
    }

    /**
     * Zajistí existenci cache adresáře
     */
    private function ensureCacheDirectoryExists(): void
    {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Vytvoření .htaccess pro bezpečnost
        $htaccessPath = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessPath)) {
            file_put_contents($htaccessPath, "Order deny,allow\nDeny from all");
        }
        
        // Vytvoření index.php
        $indexPath = $this->cacheDir . '/index.php';
        if (!file_exists($indexPath)) {
            file_put_contents($indexPath, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Forbidden');\n");
        }
    }

    /**
     * Formátuje velikost v bytech
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Nastaví cache strategii pro konkrétní typ
     */
    public function setCacheStrategy(string $type, int $ttl): void
    {
        // Vytvoříme nový array s aktualizovanou strategií
        $strategies = $this->cacheStrategies;
        $strategies[$type] = $ttl;
        $this->cacheStrategies = $strategies;
    }

    /**
     * Získá cache strategii
     */
    public function getCacheStrategies(): array
    {
        return $this->cacheStrategies;
    }

    /**
     * Vytvoří backup cache
     */
    public function backup(): string
    {
        $backupDir = $this->cacheDir . '/backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $backupFile = $backupDir . '/cache_backup_' . date('Y-m-d_H-i-s') . '.tar.gz';
        
        // Vytvoření tar.gz archivu
        $command = "cd " . escapeshellarg($this->cacheDir) . " && tar -czf " . escapeshellarg($backupFile) . " --exclude=backups *.cache 2>/dev/null";
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($backupFile)) {
            return $backupFile;
        }
        
        throw new DomainException('Nepodařilo se vytvořit backup cache');
    }

    /**
     * Obnoví cache ze zálohy
     */
    public function restore(string $backupFile): bool
    {
        if (!file_exists($backupFile)) {
            throw new DomainException('Backup soubor neexistuje');
        }
        
        // Vyčištění současné cache
        $this->flush();
        
        // Extrakce backup
        $command = "cd " . escapeshellarg($this->cacheDir) . " && tar -xzf " . escapeshellarg($backupFile) . " 2>/dev/null";
        exec($command, $output, $returnCode);
        
        return $returnCode === 0;
    }
}
