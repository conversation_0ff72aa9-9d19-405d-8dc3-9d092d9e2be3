<?php
/**
 * File Manager Service
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Service;

use Symfony\Component\HttpFoundation\File\UploadedFile;

class FileManager
{
    private string $moduleDir;
    private string $uploadDir;
    private string $imageDir;
    private string $fileDir;

    public function __construct(string $moduleDir)
    {
        $this->moduleDir = $moduleDir;
        $this->uploadDir = $moduleDir . '/uploads';
        $this->imageDir = $this->uploadDir . '/images';
        $this->fileDir = $this->uploadDir . '/files';
    }

    /**
     * Upload image file
     */
    public function uploadImage(UploadedFile $file, int $catalogId): string
    {
        $this->ensureDirectoryExists($this->imageDir);
        
        $extension = $file->getClientOriginalExtension();
        $filename = 'catalog_' . $catalogId . '_' . uniqid() . '.' . $extension;
        
        $file->move($this->imageDir, $filename);
        
        return $filename;
    }

    /**
     * Upload catalog file
     */
    public function uploadCatalogFile(UploadedFile $file, int $catalogId): string
    {
        $this->ensureDirectoryExists($this->fileDir);
        
        $extension = $file->getClientOriginalExtension();
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $filename = 'catalog_' . $catalogId . '_' . $this->sanitizeFilename($originalName) . '.' . $extension;
        
        $file->move($this->fileDir, $filename);
        
        return $filename;
    }

    /**
     * Upload general file
     */
    public function uploadFile(UploadedFile $file): string
    {
        $this->ensureDirectoryExists($this->fileDir);
        
        $extension = $file->getClientOriginalExtension();
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $filename = $this->sanitizeFilename($originalName) . '_' . uniqid() . '.' . $extension;
        
        $file->move($this->fileDir, $filename);
        
        return $filename;
    }

    /**
     * Delete file
     */
    public function deleteFile(string $filename): bool
    {
        $imagePath = $this->imageDir . '/' . $filename;
        $filePath = $this->fileDir . '/' . $filename;
        
        if (file_exists($imagePath)) {
            return unlink($imagePath);
        }
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return false;
    }

    /**
     * Get image path
     */
    public function getImagePath(string $filename): string
    {
        return $this->imageDir . '/' . $filename;
    }

    /**
     * Get file path
     */
    public function getFilePath(string $filename): string
    {
        return $this->fileDir . '/' . $filename;
    }

    /**
     * Create thumbnails for image
     */
    public function createThumbnails(string $filename): array
    {
        $imagePath = $this->getImagePath($filename);
        
        if (!file_exists($imagePath)) {
            throw new \Exception('Image file not found');
        }

        $thumbnails = [];
        $sizes = [
            'small' => [150, 100],
            'medium' => [300, 200],
            'large' => [600, 400]
        ];

        foreach ($sizes as $size => $dimensions) {
            $thumbnailPath = $this->createThumbnail($imagePath, $dimensions[0], $dimensions[1], $size);
            $thumbnails[$size] = basename($thumbnailPath);
        }

        return $thumbnails;
    }

    /**
     * Create single thumbnail
     */
    private function createThumbnail(string $imagePath, int $width, int $height, string $suffix): string
    {
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $suffix . '.' . $pathInfo['extension'];

        // Simple thumbnail creation using GD
        $imageInfo = getimagesize($imagePath);
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        $imageType = $imageInfo[2];

        // Calculate new dimensions maintaining aspect ratio
        $ratio = min($width / $originalWidth, $height / $originalHeight);
        $newWidth = (int)($originalWidth * $ratio);
        $newHeight = (int)($originalHeight * $ratio);

        // Create image resource
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($imagePath);
                break;
            default:
                throw new \Exception('Unsupported image type');
        }

        // Create thumbnail
        $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $newWidth, $newHeight, $transparent);
        }

        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

        // Save thumbnail
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                imagejpeg($thumbnail, $thumbnailPath, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($thumbnail, $thumbnailPath);
                break;
            case IMAGETYPE_GIF:
                imagegif($thumbnail, $thumbnailPath);
                break;
        }

        imagedestroy($source);
        imagedestroy($thumbnail);

        return $thumbnailPath;
    }

    /**
     * Ensure directory exists
     */
    private function ensureDirectoryExists(string $directory): void
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    /**
     * Sanitize filename
     */
    private function sanitizeFilename(string $filename): string
    {
        // Remove special characters and spaces
        $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $filename);
        $filename = preg_replace('/_+/', '_', $filename);
        $filename = trim($filename, '_');
        
        return $filename ?: 'file';
    }
}
