<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use CigCatalog\Entity\CatalogOrder;
use CigCatalog\Repository\CatalogOrderRepository;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Service\EmailService;
use CigCatalog\Service\CacheService;
use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;

/**
 * CatalogOrderService - správa objednávek katalogů
 */
class CatalogOrderService
{
    private CatalogOrderRepository $orderRepository;
    private CatalogRepository $catalogRepository;
    private EmailService $emailService;
    private CacheService $cacheService;

    // Rate limiting - max objednávek za hodinu z jedné IP
    private const MAX_ORDERS_PER_HOUR = 5;
    
    // Spam protection - min čas mezi objednávkami ze stejného emailu (minuty)
    private const MIN_TIME_BETWEEN_ORDERS = 10;

    public function __construct(
        CatalogOrderRepository $orderRepository,
        CatalogRepository $catalogRepository,
        EmailService $emailService,
        CacheService $cacheService
    ) {
        $this->orderRepository = $orderRepository;
        $this->catalogRepository = $catalogRepository;
        $this->emailService = $emailService;
        $this->cacheService = $cacheService;
    }

    /**
     * Vytvoří novou objednávku
     */
    public function createOrder(array $orderData): int
    {
        // Validace dat
        $validatedData = $this->validateOrderData($orderData);
        
        // Kontrola rate limiting
        $this->checkRateLimit($validatedData['email']);
        
        // Kontrola spam protection
        $this->checkSpamProtection($validatedData['email']);
        
        // Kontrola existence katalogu
        $catalog = $this->catalogRepository->findById($validatedData['id_catalog']);
        if (!$catalog || !$catalog->active) {
            throw new DomainException('Katalog není dostupný');
        }

        // Vytvoření objednávky
        $order = new CatalogOrder();
        $this->fillOrderData($order, $validatedData);
        
        $orderId = $this->orderRepository->save($order);
        
        // Zpracování objednávky
        $this->processOrder($orderId);
        
        // Vyčištění cache
        $this->clearOrderCache();
        
        return $orderId;
    }

    /**
     * Validuje data objednávky
     */
    public function validateOrderData(array $data): array
    {
        $errors = [];
        $validatedData = [];

        // ID katalogu
        if (empty($data['id_catalog']) || !is_numeric($data['id_catalog'])) {
            $errors[] = 'ID katalogu je povinné';
        } else {
            $validatedData['id_catalog'] = (int)$data['id_catalog'];
        }

        // Jméno
        if (empty($data['first_name']) || strlen(trim($data['first_name'])) < 2) {
            $errors[] = 'Jméno musí mít alespoň 2 znaky';
        } else {
            $validatedData['first_name'] = trim($data['first_name']);
        }

        // Příjmení
        if (empty($data['last_name']) || strlen(trim($data['last_name'])) < 2) {
            $errors[] = 'Příjmení musí mít alespoň 2 znaky';
        } else {
            $validatedData['last_name'] = trim($data['last_name']);
        }

        // Email
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Neplatný email';
        } else {
            $validatedData['email'] = strtolower(trim($data['email']));
        }

        // Telefon (volitelný)
        if (!empty($data['phone'])) {
            $phone = preg_replace('/[^0-9+\-\s]/', '', $data['phone']);
            if (strlen($phone) < 9) {
                $errors[] = 'Neplatné telefonní číslo';
            } else {
                $validatedData['phone'] = $phone;
            }
        }

        // Adresa
        if (empty($data['address']) || strlen(trim($data['address'])) < 10) {
            $errors[] = 'Adresa musí mít alespoň 10 znaků';
        } else {
            $validatedData['address'] = trim($data['address']);
        }

        // Firemní údaje (volitelné)
        if (!empty($data['company_name'])) {
            $validatedData['company_name'] = trim($data['company_name']);
            
            // IČO validace
            if (!empty($data['company_id'])) {
                if (!$this->validateCompanyId($data['company_id'])) {
                    $errors[] = 'Neplatné IČO';
                } else {
                    $validatedData['company_id'] = $data['company_id'];
                }
            }
        }

        // Poznámka (volitelná)
        if (!empty($data['note'])) {
            $validatedData['note'] = trim($data['note']);
        }

        if (!empty($errors)) {
            throw new DomainException('Chyby ve validaci: ' . implode(', ', $errors));
        }

        return $validatedData;
    }

    /**
     * Zpracuje objednávku (odešle emaily)
     */
    public function processOrder(int $orderId): bool
    {
        $order = $this->orderRepository->findById($orderId);
        if (!$order) {
            throw new DomainException('Objednávka nebyla nalezena');
        }

        $catalog = $this->catalogRepository->findById($order->id_catalog);
        if (!$catalog) {
            throw new DomainException('Katalog nebyl nalezen');
        }

        $orderData = $order->toArray();
        $catalogData = $catalog->toArray();

        try {
            // Odeslání potvrzení zákazníkovi
            $this->emailService->sendOrderConfirmation($orderData, $catalogData);
            
            // Odeslání notifikace administrátorovi
            $this->emailService->sendOrderNotification($orderData, $catalogData);
            
            return true;
        } catch (\Exception $e) {
            // Log chyby ale neblokuj objednávku
            error_log('Chyba při odesílání emailů pro objednávku ' . $orderId . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Získá statistiky objednávek
     */
    public function getOrderStatistics(): array
    {
        $cacheKey = 'order_statistics';
        $stats = $this->cacheService->get($cacheKey);
        
        if ($stats === null) {
            $stats = [
                'total_orders' => $this->orderRepository->getTotalCount(),
                'orders_today' => $this->orderRepository->getTodayCount(),
                'orders_this_week' => $this->orderRepository->getThisWeekCount(),
                'orders_this_month' => $this->orderRepository->getThisMonthCount(),
                'top_catalogs' => $this->orderRepository->getTopCatalogs(5),
                'recent_orders' => $this->orderRepository->getRecentOrders(10)
            ];
            
            $this->cacheService->set($cacheKey, $stats, 1800); // 30 minut
        }
        
        return $stats;
    }

    /**
     * Validuje české IČO
     */
    private function validateCompanyId(string $companyId): bool
    {
        // Odstranění mezer a pomlček
        $companyId = preg_replace('/[\s\-]/', '', $companyId);
        
        // Kontrola délky (8 číslic)
        if (!preg_match('/^\d{8}$/', $companyId)) {
            return false;
        }
        
        // Kontrolní součet
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int)$companyId[$i] * (8 - $i);
        }
        
        $remainder = $sum % 11;
        $checkDigit = ($remainder < 2) ? $remainder : 11 - $remainder;
        
        return (int)$companyId[7] === $checkDigit;
    }

    /**
     * Kontrola rate limiting
     */
    private function checkRateLimit(string $email): void
    {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $cacheKey = 'rate_limit_' . md5($ip);
        
        $attempts = $this->cacheService->get($cacheKey) ?? 0;
        
        if ($attempts >= self::MAX_ORDERS_PER_HOUR) {
            throw new DomainException('Překročen limit objednávek za hodinu');
        }
        
        $this->cacheService->set($cacheKey, $attempts + 1, 3600); // 1 hodina
    }

    /**
     * Kontrola spam protection
     */
    private function checkSpamProtection(string $email): void
    {
        $cacheKey = 'last_order_' . md5($email);
        $lastOrderTime = $this->cacheService->get($cacheKey);
        
        if ($lastOrderTime !== null) {
            $timeDiff = time() - $lastOrderTime;
            $minTime = self::MIN_TIME_BETWEEN_ORDERS * 60; // převod na sekundy
            
            if ($timeDiff < $minTime) {
                $remainingTime = ceil(($minTime - $timeDiff) / 60);
                throw new DomainException("Můžete objednat další katalog za {$remainingTime} minut");
            }
        }
        
        $this->cacheService->set($cacheKey, time(), 3600); // 1 hodina
    }

    /**
     * Vyplní data do entity objednávky
     */
    private function fillOrderData(CatalogOrder $order, array $data): void
    {
        $order->id_catalog = $data['id_catalog'];
        $order->first_name = $data['first_name'];
        $order->last_name = $data['last_name'];
        $order->email = $data['email'];
        $order->phone = $data['phone'] ?? '';
        $order->address = $data['address'];
        $order->company_name = $data['company_name'] ?? '';
        $order->company_id = $data['company_id'] ?? '';
        $order->note = $data['note'] ?? '';
    }

    /**
     * Vyčistí cache související s objednávkami
     */
    private function clearOrderCache(): void
    {
        $this->cacheService->delete('order_statistics');
        $this->cacheService->clear('order_list_*');
    }

    /**
     * Získá objednávku podle ID
     */
    public function getOrderById(int $orderId): ?array
    {
        $order = $this->orderRepository->findById($orderId);
        if (!$order) {
            return null;
        }

        // Přidání informací o katalogu
        $catalog = $this->catalogRepository->findById($order->id_catalog);
        $orderData = $order->toArray();

        if ($catalog) {
            $orderData['catalog_name'] = $catalog->title;
            $orderData['catalog_image'] = $catalog->image;
        }

        return $orderData;
    }

    /**
     * Získá objednávky pro konkrétní katalog
     */
    public function getOrdersForCatalog(int $catalogId, int $limit = 50, int $offset = 0): array
    {
        return $this->orderRepository->findByCatalogId($catalogId, $limit, $offset);
    }

    /**
     * Exportuje objednávky do CSV
     */
    public function exportOrdersToCsv(array $filters = []): string
    {
        $orders = $this->orderRepository->findWithFilters($filters);
        
        $csvData = [];
        $csvData[] = [
            'ID',
            'Katalog',
            'Jméno',
            'Příjmení',
            'Email',
            'Telefon',
            'Firma',
            'IČO',
            'Adresa',
            'Poznámka',
            'Datum objednávky'
        ];
        
        foreach ($orders as $order) {
            $catalog = $this->catalogRepository->findById($order->id_catalog);
            $csvData[] = [
                $order->id_order,
                $catalog ? $catalog->title : 'N/A',
                $order->first_name,
                $order->last_name,
                $order->email,
                $order->phone,
                $order->company_name,
                $order->company_id,
                $order->address,
                $order->note,
                $order->date_add
            ];
        }
        
        // Vytvoření CSV
        $output = fopen('php://temp', 'r+');
        foreach ($csvData as $row) {
            fputcsv($output, $row, ';');
        }
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }

    /**
     * Smaže staré objednávky
     */
    public function cleanupOldOrders(int $daysOld = 365): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        $deletedCount = $this->orderRepository->deleteOlderThan($cutoffDate);
        
        // Vyčištění cache
        $this->clearOrderCache();
        
        return $deletedCount;
    }

    /**
     * Získá duplicitní objednávky (stejný email + katalog)
     */
    public function findDuplicateOrders(): array
    {
        return $this->orderRepository->findDuplicates();
    }
}
