<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Entity;

use ObjectModel;
use Db;
use DbQuery;
use Validate;
use Tools;
use Context;

/**
 * CatalogOrder entity class
 * 
 * Represents a catalog order/request
 */
class CatalogOrder extends ObjectModel
{
    /** @var int */
    public $id_order;

    /** @var int */
    public $id_catalog;

    /** @var string */
    public $customer_name;

    /** @var string */
    public $customer_email;

    /** @var string|null */
    public $customer_phone;

    /** @var string|null */
    public $company_name;

    /** @var string|null */
    public $company_ico;

    /** @var string|null */
    public $address;

    /** @var string|null */
    public $city;

    /** @var string|null */
    public $postal_code;

    /** @var string|null */
    public $country;

    /** @var string|null */
    public $note;

    /** @var string|null */
    public $ip_address;

    /** @var string|null */
    public $user_agent;

    /** @var string */
    public $status = 'pending';

    /** @var string|null */
    public $admin_note;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'cig_catalog_order',
        'primary' => 'id_order',
        'fields' => [
            'id_catalog' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedInt',
                'required' => true
            ],
            'customer_name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => true,
                'size' => 255
            ],
            'customer_email' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isEmail',
                'required' => true,
                'size' => 255
            ],
            'customer_phone' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isPhoneNumber',
                'required' => false,
                'size' => 50
            ],
            'company_name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 255
            ],
            'company_ico' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 20
            ],
            'address' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isAddress',
                'required' => false,
                'size' => 500
            ],
            'city' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isCityName',
                'required' => false,
                'size' => 100
            ],
            'postal_code' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isPostCode',
                'required' => false,
                'size' => 20
            ],
            'country' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 100
            ],
            'note' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'ip_address' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isIp2Long',
                'required' => false,
                'size' => 45
            ],
            'user_agent' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 500
            ],
            'status' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => true,
                'values' => ['pending', 'processed', 'sent', 'cancelled']
            ],
            'admin_note' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => false
            ],
            'date_upd' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => false
            ]
        ]
    ];

    /**
     * Constructor
     *
     * @param int|null $id
     */
    public function __construct($id = null)
    {
        parent::__construct($id);
    }

    /**
     * Add order to database
     *
     * @param bool $auto_date
     * @param bool $null_values
     * @return bool
     */
    public function add($auto_date = true, $null_values = false)
    {
        // Set IP address and user agent if not set
        if (empty($this->ip_address)) {
            $this->ip_address = Tools::getRemoteAddr();
        }

        if (empty($this->user_agent)) {
            $this->user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        }

        return parent::add($auto_date, $null_values);
    }

    /**
     * Get orders by status
     *
     * @param string $status
     * @param int $start
     * @param int $limit
     * @return array
     */
    public static function getOrdersByStatus($status, $start = 0, $limit = 0)
    {
        $sql = new DbQuery();
        $sql->select('co.*, c.name as catalog_name');
        $sql->from('cig_catalog_order', 'co');
        $sql->leftJoin('cig_catalog', 'c', 'co.id_catalog = c.id_catalog');
        $sql->leftJoin('cig_catalog_lang', 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . (int) Context::getContext()->language->id);
        $sql->where('co.status = "' . pSQL($status) . '"');
        $sql->orderBy('co.date_add DESC');

        if ($limit > 0) {
            $sql->limit($limit, $start);
        }

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get orders by catalog
     *
     * @param int $id_catalog
     * @param int $start
     * @param int $limit
     * @return array
     */
    public static function getOrdersByCatalog($id_catalog, $start = 0, $limit = 0)
    {
        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('cig_catalog_order');
        $sql->where('id_catalog = ' . (int) $id_catalog);
        $sql->orderBy('date_add DESC');

        if ($limit > 0) {
            $sql->limit($limit, $start);
        }

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get total orders count
     *
     * @param string|null $status
     * @return int
     */
    public static function getTotalOrdersCount($status = null)
    {
        $sql = new DbQuery();
        $sql->select('COUNT(*)');
        $sql->from('cig_catalog_order');

        if ($status !== null) {
            $sql->where('status = "' . pSQL($status) . '"');
        }

        return (int) Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);
    }

    /**
     * Get orders statistics
     *
     * @return array
     */
    public static function getOrdersStatistics()
    {
        $sql = new DbQuery();
        $sql->select('status, COUNT(*) as count');
        $sql->from('cig_catalog_order');
        $sql->groupBy('status');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        $stats = [
            'pending' => 0,
            'processed' => 0,
            'sent' => 0,
            'cancelled' => 0,
            'total' => 0
        ];

        foreach ($results as $result) {
            $stats[$result['status']] = (int) $result['count'];
            $stats['total'] += (int) $result['count'];
        }

        return $stats;
    }

    /**
     * Validate Czech ICO (company ID)
     *
     * @param string $ico
     * @return bool
     */
    public static function validateCzechICO($ico)
    {
        if (empty($ico)) {
            return true; // ICO is optional
        }

        // Remove spaces and non-numeric characters
        $ico = preg_replace('/[^0-9]/', '', $ico);

        // ICO must be 8 digits
        if (strlen($ico) !== 8) {
            return false;
        }

        // Calculate checksum
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int) $ico[$i] * (8 - $i);
        }

        $remainder = $sum % 11;
        $checksum = $remainder < 2 ? $remainder : 11 - $remainder;

        return (int) $ico[7] === $checksum;
    }

    /**
     * Get catalog information
     *
     * @return Catalog|false
     */
    public function getCatalog()
    {
        if ($this->id_catalog) {
            return new Catalog($this->id_catalog);
        }

        return false;
    }

    /**
     * Get formatted customer data
     *
     * @return array
     */
    public function getFormattedCustomerData()
    {
        return [
            'name' => $this->customer_name,
            'email' => $this->customer_email,
            'phone' => $this->customer_phone,
            'company' => $this->company_name,
            'ico' => $this->company_ico,
            'full_address' => $this->getFullAddress(),
            'note' => $this->note
        ];
    }

    /**
     * Get full formatted address
     *
     * @return string
     */
    public function getFullAddress()
    {
        $address_parts = array_filter([
            $this->address,
            $this->city,
            $this->postal_code,
            $this->country
        ]);

        return implode(', ', $address_parts);
    }

    /**
     * Update order status
     *
     * @param string $new_status
     * @param string|null $admin_note
     * @return bool
     */
    public function updateStatus($new_status, $admin_note = null)
    {
        $valid_statuses = ['pending', 'processed', 'sent', 'cancelled'];

        if (!in_array($new_status, $valid_statuses)) {
            return false;
        }

        $this->status = $new_status;

        if ($admin_note !== null) {
            $this->admin_note = $admin_note;
        }

        return $this->update();
    }
}
