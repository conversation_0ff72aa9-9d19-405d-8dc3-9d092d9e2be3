<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Entity;

use ObjectModel;
use Db;
use DbQuery;
use Validate;
use Tools;
use Context;
use Language;

/**
 * Catalog entity class
 * 
 * Represents a catalog item with multilingual support
 */
class Catalog extends ObjectModel
{
    /** @var int */
    public $id_catalog;

    /** @var bool */
    public $active = true;

    /** @var int */
    public $position = 0;

    /** @var bool */
    public $is_new = false;

    /** @var string|null */
    public $image;

    /** @var string|null */
    public $catalog_file;

    /** @var string|null */
    public $catalog_url;

    /** @var int */
    public $download_count = 0;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    // Multilingual fields
    /** @var string */
    public $name;

    /** @var string|null */
    public $description;

    /** @var string|null */
    public $short_description;

    /** @var string|null */
    public $meta_title;

    /** @var string|null */
    public $meta_description;

    /** @var string|null */
    public $slug;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'cig_catalog',
        'primary' => 'id_catalog',
        'multilang' => true,
        'fields' => [
            // Main fields
            'active' => [
                'type' => self::TYPE_BOOL,
                'validate' => 'isBool',
                'required' => false
            ],
            'position' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedInt',
                'required' => false
            ],
            'is_new' => [
                'type' => self::TYPE_BOOL,
                'validate' => 'isBool',
                'required' => false
            ],
            'image' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'size' => 255,
                'required' => false
            ],
            'catalog_file' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'size' => 255,
                'required' => false
            ],
            'catalog_url' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isUrl',
                'size' => 500,
                'required' => false
            ],
            'download_count' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedInt',
                'required' => false
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => false
            ],
            'date_upd' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => false
            ],

            // Multilingual fields
            'name' => [
                'type' => self::TYPE_STRING,
                'lang' => true,
                'validate' => 'isGenericName',
                'required' => true,
                'size' => 255
            ],
            'description' => [
                'type' => self::TYPE_HTML,
                'lang' => true,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'short_description' => [
                'type' => self::TYPE_STRING,
                'lang' => true,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 500
            ],
            'meta_title' => [
                'type' => self::TYPE_STRING,
                'lang' => true,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 255
            ],
            'meta_description' => [
                'type' => self::TYPE_STRING,
                'lang' => true,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 500
            ],
            'slug' => [
                'type' => self::TYPE_STRING,
                'lang' => true,
                'validate' => 'isLinkRewrite',
                'required' => false,
                'size' => 255
            ]
        ]
    ];

    /**
     * Constructor
     *
     * @param int|null $id
     * @param int|null $id_lang
     */
    public function __construct($id = null, $id_lang = null)
    {
        parent::__construct($id, $id_lang);
    }

    /**
     * Add catalog to database
     *
     * @param bool $auto_date
     * @param bool $null_values
     * @return bool
     */
    public function add($auto_date = true, $null_values = false)
    {
        if ($this->position <= 0) {
            $this->position = $this->getHighestPosition() + 1;
        }

        if (empty($this->slug)) {
            $this->generateSlug();
        }

        return parent::add($auto_date, $null_values);
    }

    /**
     * Update catalog in database
     *
     * @param bool $null_values
     * @return bool
     */
    public function update($null_values = false)
    {
        if (empty($this->slug)) {
            $this->generateSlug();
        }

        return parent::update($null_values);
    }

    /**
     * Get highest position
     *
     * @return int
     */
    public function getHighestPosition()
    {
        $sql = new DbQuery();
        $sql->select('MAX(position)');
        $sql->from('cig_catalog');

        return (int) Db::getInstance()->getValue($sql);
    }

    /**
     * Generate slug from name
     */
    public function generateSlug()
    {
        if (is_array($this->name)) {
            foreach ($this->name as $id_lang => $name) {
                if (!empty($name)) {
                    $this->slug[$id_lang] = Tools::str2url($name);
                }
            }
        } elseif (!empty($this->name)) {
            $this->slug = Tools::str2url($this->name);
        }
    }

    /**
     * Get all active catalogs
     *
     * @param int|null $id_lang
     * @param int $start
     * @param int $limit
     * @param string $order_by
     * @param string $order_way
     * @return array
     */
    public static function getActiveCatalogs($id_lang = null, $start = 0, $limit = 0, $order_by = 'position', $order_way = 'ASC')
    {
        if ($id_lang === null) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('c.*, cl.*');
        $sql->from('cig_catalog', 'c');
        $sql->leftJoin('cig_catalog_lang', 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . (int) $id_lang);
        $sql->where('c.active = 1');
        $sql->orderBy('c.' . pSQL($order_by) . ' ' . pSQL($order_way));

        if ($limit > 0) {
            $sql->limit($limit, $start);
        }

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get catalog by slug
     *
     * @param string $slug
     * @param int|null $id_lang
     * @return Catalog|false
     */
    public static function getBySlug($slug, $id_lang = null)
    {
        if ($id_lang === null) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('c.id_catalog');
        $sql->from('cig_catalog', 'c');
        $sql->leftJoin('cig_catalog_lang', 'cl', 'c.id_catalog = cl.id_catalog AND cl.id_lang = ' . (int) $id_lang);
        $sql->where('cl.slug = "' . pSQL($slug) . '"');
        $sql->where('c.active = 1');

        $id_catalog = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if ($id_catalog) {
            return new self($id_catalog, $id_lang);
        }

        return false;
    }

    /**
     * Increment download count
     *
     * @return bool
     */
    public function incrementDownloadCount()
    {
        $this->download_count++;
        return $this->update();
    }

    /**
     * Check if catalog is new based on configuration
     *
     * @return bool
     */
    public function isNew()
    {
        if ($this->is_new) {
            return true;
        }

        // Check if catalog is new based on date
        $new_days = (int) \Configuration::get('CIG_CATALOG_NEW_BADGE_DAYS', 30);
        $date_limit = date('Y-m-d H:i:s', strtotime('-' . $new_days . ' days'));

        return $this->date_add > $date_limit;
    }

    /**
     * Get image URL
     *
     * @return string|null
     */
    public function getImageUrl()
    {
        if (empty($this->image)) {
            return null;
        }

        return _MODULE_DIR_ . 'cig_catalog/uploads/images/' . $this->image;
    }

    /**
     * Get file URL
     *
     * @return string|null
     */
    public function getFileUrl()
    {
        if (!empty($this->catalog_url)) {
            return $this->catalog_url;
        }

        if (!empty($this->catalog_file)) {
            return _MODULE_DIR_ . 'cig_catalog/uploads/files/' . $this->catalog_file;
        }

        return null;
    }
}
