<?php
/**
 * Catalog API Service
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Api;

use CigCatalog\Service\CatalogManager;
use CigCatalog\Service\CatalogOrderService;
use CigCatalog\Security\CsrfTokenManager;
use CigCatalog\Security\RateLimiter;

class CatalogApi
{
    private CatalogManager $catalogManager;
    private CatalogOrderService $orderService;
    private CsrfTokenManager $csrfTokenManager;
    private RateLimiter $rateLimiter;

    public function __construct(
        CatalogManager $catalogManager,
        CatalogOrderService $orderService,
        CsrfTokenManager $csrfTokenManager,
        RateLimiter $rateLimiter
    ) {
        $this->catalogManager = $catalogManager;
        $this->orderService = $orderService;
        $this->csrfTokenManager = $csrfTokenManager;
        $this->rateLimiter = $rateLimiter;
    }

    /**
     * Get catalog list with pagination
     */
    public function getCatalogs(array $params = []): array
    {
        $page = max(1, (int)($params['page'] ?? 1));
        $perPage = min(50, max(1, (int)($params['per_page'] ?? 10)));
        $langId = (int)($params['lang_id'] ?? 1);
        $search = $params['search'] ?? '';

        try {
            if ($search) {
                $catalogs = $this->catalogManager->searchCatalogs($search, $langId, $page, $perPage);
                $total = $this->catalogManager->countSearchResults($search, $langId);
            } else {
                $catalogs = $this->catalogManager->getCatalogs($langId, $page, $perPage);
                $total = $this->catalogManager->getTotalCount();
            }

            return [
                'success' => true,
                'data' => [
                    'catalogs' => $catalogs,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'total_pages' => ceil($total / $perPage)
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Chyba při načítání katalogů: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get single catalog by ID
     */
    public function getCatalog(int $id, int $langId = 1): array
    {
        try {
            $catalog = $this->catalogManager->getCatalogById($id, $langId);
            
            if (!$catalog) {
                return [
                    'success' => false,
                    'message' => 'Katalog nebyl nalezen.'
                ];
            }

            return [
                'success' => true,
                'data' => $catalog
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Chyba při načítání katalogu: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create order
     */
    public function createOrder(array $orderData, string $clientIp): array
    {
        try {
            // Check rate limiting
            if (!$this->rateLimiter->isAllowed($clientIp)) {
                return [
                    'success' => false,
                    'message' => 'Příliš mnoho požadavků. Zkuste to později.'
                ];
            }

            // Validate CSRF token
            if (!$this->csrfTokenManager->isTokenValid('catalog_order', $orderData['_token'] ?? '')) {
                return [
                    'success' => false,
                    'message' => 'Neplatný bezpečnostní token.'
                ];
            }

            // Create order
            $orderId = $this->orderService->createOrder($orderData);

            return [
                'success' => true,
                'message' => 'Objednávka byla úspěšně vytvořena.',
                'data' => [
                    'order_id' => $orderId
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Chyba při vytváření objednávky: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get popular catalogs
     */
    public function getPopularCatalogs(int $limit = 10, int $langId = 1): array
    {
        try {
            $catalogs = $this->catalogManager->getPopularCatalogs($limit, $langId);

            return [
                'success' => true,
                'data' => $catalogs
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Chyba při načítání populárních katalogů: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get new catalogs
     */
    public function getNewCatalogs(int $limit = 10, int $langId = 1): array
    {
        try {
            $catalogs = $this->catalogManager->getNewCatalogs($limit, $langId);

            return [
                'success' => true,
                'data' => $catalogs
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Chyba při načítání nových katalogů: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(string $tokenId = 'catalog_order'): array
    {
        try {
            $token = $this->csrfTokenManager->generateToken($tokenId);

            return [
                'success' => true,
                'data' => [
                    'token' => $token,
                    'token_id' => $tokenId
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Chyba při generování tokenu: ' . $e->getMessage()
            ];
        }
    }
}
