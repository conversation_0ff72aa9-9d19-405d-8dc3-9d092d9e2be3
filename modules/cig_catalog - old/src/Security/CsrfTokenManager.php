<?php
/**
 * CSRF Token Manager
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Security;

use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Csrf\CsrfToken;

class CsrfTokenManager
{
    private CsrfTokenManagerInterface $tokenManager;

    public function __construct(CsrfTokenManagerInterface $tokenManager)
    {
        $this->tokenManager = $tokenManager;
    }

    /**
     * Generate CSRF token
     */
    public function generateToken(string $tokenId): string
    {
        return $this->tokenManager->getToken($tokenId)->getValue();
    }

    /**
     * Validate CSRF token
     */
    public function isTokenValid(string $tokenId, string $token): bool
    {
        return $this->tokenManager->isTokenValid(new CsrfToken($tokenId, $token));
    }

    /**
     * Remove token
     */
    public function removeToken(string $tokenId): ?string
    {
        return $this->tokenManager->removeToken($tokenId);
    }

    /**
     * Refresh token
     */
    public function refreshToken(string $tokenId): string
    {
        $this->removeToken($tokenId);
        return $this->generateToken($tokenId);
    }
}
