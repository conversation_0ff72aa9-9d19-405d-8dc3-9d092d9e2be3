<?php
/**
 * Rate Limiter
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Security;

use Symfony\Contracts\Cache\CacheInterface;
use Psr\Log\LoggerInterface;

class RateLimiter
{
    private CacheInterface $cache;
    private LoggerInterface $logger;
    private int $maxRequests;
    private int $timeWindow;

    public function __construct(
        CacheInterface $cache,
        LoggerInterface $logger,
        int $maxRequests = 10,
        int $timeWindow = 300
    ) {
        $this->cache = $cache;
        $this->logger = $logger;
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow;
    }

    /**
     * Check if request is allowed
     */
    public function isAllowed(string $identifier): bool
    {
        $key = 'rate_limit_' . md5($identifier);
        
        try {
            $requests = $this->cache->get($key, function() {
                return [
                    'count' => 0,
                    'reset_time' => time() + $this->timeWindow
                ];
            });

            // Reset counter if time window has passed
            if (time() >= $requests['reset_time']) {
                $requests = [
                    'count' => 0,
                    'reset_time' => time() + $this->timeWindow
                ];
            }

            // Check if limit exceeded
            if ($requests['count'] >= $this->maxRequests) {
                $this->logger->warning('Rate limit exceeded', [
                    'identifier' => $identifier,
                    'count' => $requests['count'],
                    'max_requests' => $this->maxRequests
                ]);
                return false;
            }

            // Increment counter
            $requests['count']++;
            $this->cache->delete($key);
            $this->cache->get($key, function() use ($requests) {
                return $requests;
            });

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Rate limiter error', [
                'error' => $e->getMessage(),
                'identifier' => $identifier
            ]);
            
            // Allow request if cache fails
            return true;
        }
    }

    /**
     * Get remaining requests
     */
    public function getRemainingRequests(string $identifier): int
    {
        $key = 'rate_limit_' . md5($identifier);
        
        try {
            $requests = $this->cache->get($key, function() {
                return [
                    'count' => 0,
                    'reset_time' => time() + $this->timeWindow
                ];
            });

            if (time() >= $requests['reset_time']) {
                return $this->maxRequests;
            }

            return max(0, $this->maxRequests - $requests['count']);

        } catch (\Exception $e) {
            return $this->maxRequests;
        }
    }

    /**
     * Get reset time
     */
    public function getResetTime(string $identifier): int
    {
        $key = 'rate_limit_' . md5($identifier);
        
        try {
            $requests = $this->cache->get($key, function() {
                return [
                    'count' => 0,
                    'reset_time' => time() + $this->timeWindow
                ];
            });

            return $requests['reset_time'];

        } catch (\Exception $e) {
            return time() + $this->timeWindow;
        }
    }

    /**
     * Reset rate limit for identifier
     */
    public function reset(string $identifier): void
    {
        $key = 'rate_limit_' . md5($identifier);
        $this->cache->delete($key);
    }
}
