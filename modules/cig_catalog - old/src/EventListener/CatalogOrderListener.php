<?php
/**
 * Catalog Order Event Listener
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\EventListener;

use CigCatalog\Service\EmailService;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Event;

class CatalogOrderListener
{
    private EmailService $emailService;
    private LoggerInterface $logger;

    public function __construct(
        EmailService $emailService,
        LoggerInterface $logger
    ) {
        $this->emailService = $emailService;
        $this->logger = $logger;
    }

    /**
     * Handle order created event
     */
    public function onOrderCreated(Event $event): void
    {
        try {
            $orderData = $event->getSubject();
            
            if (!is_array($orderData) || !isset($orderData['id_catalog_order'])) {
                $this->logger->warning('Invalid order data in event', ['data' => $orderData]);
                return;
            }

            // Send notification email to admin
            $this->emailService->sendOrderNotification($orderData);
            
            // Send confirmation email to customer
            $this->emailService->sendOrderConfirmation($orderData);

            $this->logger->info('Order emails sent successfully', [
                'order_id' => $orderData['id_catalog_order']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to send order emails', [
                'error' => $e->getMessage(),
                'order_data' => $event->getSubject()
            ]);
        }
    }

    /**
     * Handle order updated event
     */
    public function onOrderUpdated(Event $event): void
    {
        try {
            $orderData = $event->getSubject();
            
            if (!is_array($orderData) || !isset($orderData['id_catalog_order'])) {
                $this->logger->warning('Invalid order data in event', ['data' => $orderData]);
                return;
            }

            // Send status update email to customer if status changed
            if (isset($orderData['status_changed']) && $orderData['status_changed']) {
                $this->emailService->sendOrderStatusUpdate($orderData);
                
                $this->logger->info('Order status update email sent', [
                    'order_id' => $orderData['id_catalog_order'],
                    'new_status' => $orderData['status']
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('Failed to send order update email', [
                'error' => $e->getMessage(),
                'order_data' => $event->getSubject()
            ]);
        }
    }
}
