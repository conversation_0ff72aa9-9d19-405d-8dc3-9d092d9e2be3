<?php
/**
 * File Helper Utility
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

declare(strict_types=1);

namespace CigCatalog\Util;

use Psr\Log\LoggerInterface;

class FileHelper
{
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * Get file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes) / log(1024));
        $factor = min($factor, count($units) - 1);

        return round($bytes / pow(1024, $factor), 2) . ' ' . $units[$factor];
    }

    /**
     * Get file extension
     */
    public function getExtension(string $filename): string
    {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }

    /**
     * Check if file is image
     */
    public function isImage(string $filename): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
        return in_array($this->getExtension($filename), $imageExtensions);
    }

    /**
     * Check if file is document
     */
    public function isDocument(string $filename): bool
    {
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'];
        return in_array($this->getExtension($filename), $documentExtensions);
    }

    /**
     * Check if file is archive
     */
    public function isArchive(string $filename): bool
    {
        $archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'];
        return in_array($this->getExtension($filename), $archiveExtensions);
    }

    /**
     * Get MIME type for file
     */
    public function getMimeType(string $filename): string
    {
        $extension = $this->getExtension($filename);
        
        $mimeTypes = [
            // Images
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'bmp' => 'image/bmp',
            'svg' => 'image/svg+xml',
            
            // Documents
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'txt' => 'text/plain',
            'rtf' => 'application/rtf',
            
            // Archives
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
            'tar' => 'application/x-tar',
            'gz' => 'application/gzip',
            'bz2' => 'application/x-bzip2'
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * Validate file extension
     */
    public function isAllowedExtension(string $filename, array $allowedExtensions): bool
    {
        $extension = $this->getExtension($filename);
        return in_array($extension, $allowedExtensions);
    }

    /**
     * Sanitize filename
     */
    public function sanitizeFilename(string $filename): string
    {
        // Get file extension
        $extension = $this->getExtension($filename);
        $basename = pathinfo($filename, PATHINFO_FILENAME);
        
        // Remove special characters
        $basename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $basename);
        $basename = preg_replace('/_+/', '_', $basename);
        $basename = trim($basename, '_');
        
        if (empty($basename)) {
            $basename = 'file';
        }
        
        return $basename . ($extension ? '.' . $extension : '');
    }

    /**
     * Create directory if it doesn't exist
     */
    public function ensureDirectoryExists(string $directory): bool
    {
        if (is_dir($directory)) {
            return true;
        }

        try {
            return mkdir($directory, 0755, true);
        } catch (\Exception $e) {
            $this->logger->error('Failed to create directory', [
                'directory' => $directory,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Delete file safely
     */
    public function deleteFile(string $filepath): bool
    {
        if (!file_exists($filepath)) {
            return true;
        }

        try {
            return unlink($filepath);
        } catch (\Exception $e) {
            $this->logger->error('Failed to delete file', [
                'filepath' => $filepath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Copy file safely
     */
    public function copyFile(string $source, string $destination): bool
    {
        if (!file_exists($source)) {
            $this->logger->error('Source file does not exist', ['source' => $source]);
            return false;
        }

        $destinationDir = dirname($destination);
        if (!$this->ensureDirectoryExists($destinationDir)) {
            return false;
        }

        try {
            return copy($source, $destination);
        } catch (\Exception $e) {
            $this->logger->error('Failed to copy file', [
                'source' => $source,
                'destination' => $destination,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get file icon class based on extension
     */
    public function getFileIconClass(string $filename): string
    {
        $extension = $this->getExtension($filename);
        
        if ($this->isImage($filename)) {
            return 'fa-file-image';
        }
        
        if ($this->isDocument($filename)) {
            return 'fa-file-text';
        }
        
        if ($this->isArchive($filename)) {
            return 'fa-file-archive';
        }
        
        switch ($extension) {
            case 'pdf':
                return 'fa-file-pdf';
            case 'doc':
            case 'docx':
                return 'fa-file-word';
            case 'xls':
            case 'xlsx':
                return 'fa-file-excel';
            case 'ppt':
            case 'pptx':
                return 'fa-file-powerpoint';
            default:
                return 'fa-file';
        }
    }
}
