<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Email - CIG Catalog Module</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 300;
        }
        .content {
            padding: 30px 20px;
        }
        .success-icon {
            text-align: center;
            margin-bottom: 20px;
        }
        .success-icon .icon {
            display: inline-block;
            width: 60px;
            height: 60px;
            background-color: #28a745;
            border-radius: 50%;
            line-height: 60px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .message-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
        }
        .info-table td:first-child {
            font-weight: bold;
            color: #666;
            width: 40%;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success {
            background-color: #28a745;
        }
        .status-warning {
            background-color: #ffc107;
        }
        .status-error {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <div class="header">
            <h1>✅ Email Configuration Test</h1>
            <p>CIG Catalog Module</p>
        </div>
        
        <div class="content">
            
            <div class="success-icon">
                <div class="icon">✓</div>
            </div>
            
            <div class="message-box">
                <h3 style="margin-top: 0;">Email Configuration Working!</h3>
                <p><?= htmlspecialchars($test_message) ?></p>
            </div>
            
            <h3>Test Details</h3>
            <table class="info-table">
                <tr>
                    <td>Test Date:</td>
                    <td><?= htmlspecialchars($date) ?></td>
                </tr>
                <tr>
                    <td>Shop Name:</td>
                    <td><?= htmlspecialchars($shop_name) ?></td>
                </tr>
                <tr>
                    <td>Module Version:</td>
                    <td>1.0.0</td>
                </tr>
                <tr>
                    <td>PHP Version:</td>
                    <td><?= PHP_VERSION ?></td>
                </tr>
                <tr>
                    <td>PrestaShop Version:</td>
                    <td><?= _PS_VERSION_ ?></td>
                </tr>
            </table>
            
            <h3>Email System Status</h3>
            <table class="info-table">
                <tr>
                    <td>
                        <span class="status-indicator status-success"></span>
                        Email Delivery
                    </td>
                    <td><strong>Working</strong></td>
                </tr>
                <tr>
                    <td>
                        <span class="status-indicator status-success"></span>
                        Template Rendering
                    </td>
                    <td><strong>Working</strong></td>
                </tr>
                <tr>
                    <td>
                        <span class="status-indicator status-success"></span>
                        Character Encoding
                    </td>
                    <td><strong>UTF-8 ✓</strong></td>
                </tr>
                <tr>
                    <td>
                        <span class="status-indicator status-success"></span>
                        HTML Rendering
                    </td>
                    <td><strong>Working</strong></td>
                </tr>
            </table>
            
            <div style="text-align: center; margin: 30px 0;">
                <p><strong>If you received this email, your email configuration is working correctly!</strong></p>
                <p>You can now safely use the CIG Catalog module's email features.</p>
            </div>
            
            <h3>Next Steps</h3>
            <ul>
                <li>✅ Email configuration is working</li>
                <li>📧 Order notifications will be sent to administrators</li>
                <li>📬 Order confirmations will be sent to customers</li>
                <li>🔔 Admin alerts will be delivered when needed</li>
            </ul>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">Test Email Features</h4>
                <p style="margin-bottom: 0;">This test email demonstrates:</p>
                <ul style="margin-bottom: 0;">
                    <li>HTML email rendering</li>
                    <li>CSS styling support</li>
                    <li>Character encoding (UTF-8)</li>
                    <li>Template variable substitution</li>
                    <li>Responsive design elements</li>
                </ul>
            </div>
            
        </div>
        
        <div class="footer">
            <p>
                <strong>CIG Catalog Module</strong><br>
                Test email sent from <?= htmlspecialchars($shop_name) ?>
            </p>
            <p style="margin-top: 10px; font-size: 10px; color: #999;">
                This is an automated test email. Please do not reply to this message.
            </p>
        </div>
        
    </div>
</body>
</html>
