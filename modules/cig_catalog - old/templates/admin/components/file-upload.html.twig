{#
    File Upload Component
    Reusable drag&drop upload component
    
    Parameters:
    - id: unique identifier for the upload container
    - name: form field name
    - label: field label
    - accept: accepted file types (image|file|both)
    - multiple: allow multiple files (default: false)
    - required: field is required (default: false)
    - current_file: current file path (for edit forms)
    - help_text: additional help text
#}

{% set upload_id = id|default('file-upload-' ~ random()) %}
{% set field_name = name|default('file') %}
{% set field_label = label|default('Soubor') %}
{% set accept_types = accept|default('both') %}
{% set is_multiple = multiple|default(false) %}
{% set is_required = required|default(false) %}
{% set help = help_text|default('') %}

<div class="form-group">
    <label for="{{ upload_id }}" class="form-label">
        {{ field_label }}
        {% if is_required %}<span class="text-danger">*</span>{% endif %}
    </label>
    
    {% if help %}
        <small class="form-text text-muted">{{ help }}</small>
    {% endif %}

    <!-- Current file display -->
    {% if current_file %}
        <div class="current-file mb-3">
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <i class="material-icons mr-2">
                        {% if current_file matches '/\\.(jpg|jpeg|png|gif|webp)$/i' %}
                            image
                        {% else %}
                            description
                        {% endif %}
                    </i>
                    <div class="flex-grow-1">
                        <strong>Aktuální soubor:</strong>
                        <a href="{{ asset('modules/cig_catalog/uploads/' ~ current_file) }}" 
                           target="_blank" class="ml-2">
                            {{ current_file|split('/')|last }}
                        </a>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-current-file"
                            data-file-path="{{ current_file }}"
                            data-csrf-token="{{ csrf_token('delete_current_file') }}">
                        <i class="material-icons">delete</i>
                        Smazat
                    </button>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Upload container -->
    <div id="{{ upload_id }}" class="file-upload-container">
        <div class="drop-zone">
            <i class="material-icons">cloud_upload</i>
            <div class="drop-zone-text">
                Přetáhněte soubory sem nebo klikněte pro výběr
            </div>
            <div class="drop-zone-subtext">
                {% if accept_types == 'image' %}
                    Podporované formáty: JPG, PNG, WebP, GIF (max. 50MB)
                {% elseif accept_types == 'file' %}
                    Podporované formáty: PDF, ZIP, DOC, DOCX, XLS, XLSX (max. 50MB)
                {% else %}
                    Podporované formáty: Obrázky a dokumenty (max. 50MB)
                {% endif %}
            </div>
            
            <input type="file" 
                   name="{{ field_name }}" 
                   id="{{ upload_id }}_input"
                   {% if is_multiple %}multiple{% endif %}
                   {% if is_required %}required{% endif %}
                   {% if accept_types == 'image' %}
                       accept="image/jpeg,image/png,image/webp,image/gif"
                   {% elseif accept_types == 'file' %}
                       accept=".pdf,.zip,.doc,.docx,.xls,.xlsx"
                   {% else %}
                       accept="image/jpeg,image/png,image/webp,image/gif,.pdf,.zip,.doc,.docx,.xls,.xlsx"
                   {% endif %}>
            
            <button type="button" class="browse-btn">
                <i class="material-icons">folder_open</i>
                Vybrat soubory
            </button>
        </div>
        
        <!-- Progress container -->
        <div class="upload-progress"></div>
        
        <!-- Upload controls -->
        <div class="upload-controls" style="display: none;">
            <button type="button" class="clear-btn">
                <i class="material-icons">clear_all</i>
                Vymazat vše
            </button>
        </div>
    </div>
    
    <!-- Hidden field for storing uploaded file path -->
    <input type="hidden" name="{{ field_name }}_path" id="{{ upload_id }}_path" value="{{ current_file|default('') }}">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize upload manager for this component
    const uploadManager = new FileUploadManager('{{ upload_id }}', {
        {% if accept_types == 'image' %}
        allowedTypes: {
            image: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
        },
        {% elseif accept_types == 'file' %}
        allowedTypes: {
            file: ['application/pdf', 'application/zip', 'application/msword', 
                   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                   'application/vnd.ms-excel',
                   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
        },
        {% else %}
        allowedTypes: {
            image: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
            file: ['application/pdf', 'application/zip', 'application/msword', 
                   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                   'application/vnd.ms-excel',
                   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
        },
        {% endif %}
        multiple: {{ is_multiple ? 'true' : 'false' }},
        uploadUrl: '{{ path('admin_catalog_upload') }}',
        onSuccess: function(uploadItem, response) {
            // Store uploaded file path in hidden field
            const pathField = document.getElementById('{{ upload_id }}_path');
            if (pathField) {
                pathField.value = response.file_path;
            }
            
            // Show upload controls
            const controls = document.querySelector('#{{ upload_id }} .upload-controls');
            if (controls) {
                controls.style.display = 'flex';
            }
            
            // Trigger custom event
            const event = new CustomEvent('fileUploaded', {
                detail: { uploadItem, response, fieldName: '{{ field_name }}' }
            });
            document.dispatchEvent(event);
        },
        onError: function(uploadItem, message) {
            console.error('Upload error:', message);
            
            // Trigger custom event
            const event = new CustomEvent('fileUploadError', {
                detail: { uploadItem, message, fieldName: '{{ field_name }}' }
            });
            document.dispatchEvent(event);
        }
    });
    
    // Handle delete current file
    const deleteBtn = document.querySelector('#{{ upload_id }} .delete-current-file');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            const filePath = this.dataset.filePath;
            const csrfToken = this.dataset.csrfToken;
            
            if (confirm('Opravdu chcete smazat tento soubor?')) {
                fetch('{{ path('admin_catalog_delete_file', {id: 0, type: 'file'}) }}'.replace('/0/', '/' + getCurrentCatalogId() + '/'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        _token: csrfToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove current file display
                        const currentFileDiv = document.querySelector('#{{ upload_id }} .current-file');
                        if (currentFileDiv) {
                            currentFileDiv.remove();
                        }
                        
                        // Clear hidden field
                        const pathField = document.getElementById('{{ upload_id }}_path');
                        if (pathField) {
                            pathField.value = '';
                        }
                        
                        FileUploadManager.showNotification('Soubor byl úspěšně smazán.', 'success');
                    } else {
                        FileUploadManager.showNotification(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    FileUploadManager.showNotification('Chyba při mazání souboru.', 'error');
                });
            }
        });
    }
    
    function getCurrentCatalogId() {
        // Try to get catalog ID from URL or form
        const urlParts = window.location.pathname.split('/');
        const editIndex = urlParts.indexOf('edit');
        if (editIndex !== -1 && urlParts[editIndex - 1]) {
            return urlParts[editIndex - 1];
        }
        
        // Fallback to form field
        const idField = document.querySelector('input[name="id"], input[name="catalog_id"]');
        return idField ? idField.value : '0';
    }
});
</script>

<style>
/* Component-specific styles */
#{{ upload_id }} .current-file {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

#{{ upload_id }} .delete-current-file {
    transition: all 0.2s ease;
}

#{{ upload_id }} .delete-current-file:hover {
    transform: scale(1.05);
}
</style>
