{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
<div class="content-div">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="card-title">
                                <i class="material-icons">{{ isEdit ? 'edit' : 'add' }}</i>
                                {{ pageTitle }}
                            </h3>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_catalog_index') }}" class="btn btn-secondary">
                                <i class="material-icons">arrow_back</i>
                                Zpět na seznam
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    {{ form_start(form, {'attr': {'enctype': 'multipart/form-data', 'class': 'catalog-form'}}) }}
                    
                    <div class="row">
                        <!-- <PERSON><PERSON> sloupec - Základní informace -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Základní informace</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Základní pole pro název a popis -->
                                    <div class="form-group">
                                        <label for="catalog_name_1">Název (CS)</label>
                                        {{ form_widget(form.name_1, {'attr': {'class': 'form-control', 'required': true}}) }}
                                        {{ form_errors(form.name_1) }}
                                    </div>

                                    <div class="form-group">
                                        <label for="catalog_description_1">Popis (CS)</label>
                                        {{ form_widget(form.description_1, {'attr': {'class': 'form-control', 'rows': 4}}) }}
                                        {{ form_errors(form.description_1) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Soubory -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5>Soubory</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                {{ form_label(form.image_file) }}
                                                {{ form_widget(form.image_file) }}
                                                {{ form_errors(form.image_file) }}
                                                <small class="form-text text-muted">
                                                    Podporované formáty: JPEG, PNG, GIF, WebP. Max. velikost: 5MB.
                                                </small>
                                                {% if isEdit and catalog.image %}
                                                    <div class="current-image mt-2">
                                                        <strong>Aktuální obrázek:</strong><br>
                                                        <img src="{{ asset('modules/cig_catalog/uploads/images/' ~ catalog.image) }}" 
                                                             alt="Aktuální obrázek" class="img-thumbnail" 
                                                             style="max-width: 150px; max-height: 150px;">
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                {{ form_label(form.catalog_file) }}
                                                {{ form_widget(form.catalog_file) }}
                                                {{ form_errors(form.catalog_file) }}
                                                <small class="form-text text-muted">
                                                    Podporované formáty: PDF, ZIP. Max. velikost: 50MB.
                                                </small>
                                                {% if isEdit and catalog.file_path %}
                                                    <div class="current-file mt-2">
                                                        <strong>Aktuální soubor:</strong><br>
                                                        <a href="{{ asset('modules/cig_catalog/uploads/files/' ~ catalog.file_path) }}" 
                                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="material-icons">download</i>
                                                            {{ catalog.file_path }}
                                                        </a>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pravý sloupec - Nastavení -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Nastavení</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <div class="form-check">
                                            {{ form_widget(form.active) }}
                                            {{ form_label(form.active) }}
                                        </div>
                                        {{ form_errors(form.active) }}
                                    </div>

                                    <div class="form-group">
                                        <div class="form-check">
                                            {{ form_widget(form.is_new) }}
                                            {{ form_label(form.is_new) }}
                                        </div>
                                        {{ form_errors(form.is_new) }}
                                        <small class="form-text text-muted">
                                            Katalog bude označen jako "Nový" na frontendu.
                                        </small>
                                    </div>

                                    <div class="form-group">
                                        {{ form_label(form.position) }}
                                        {{ form_widget(form.position) }}
                                        {{ form_errors(form.position) }}
                                        <small class="form-text text-muted">
                                            Pořadí zobrazení (nižší číslo = vyšší pozice).
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Akce -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5>Akce</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        {{ form_widget(form.save, {'attr': {'class': 'btn btn-primary btn-block mb-2'}}) }}
                                        {{ form_widget(form.save_and_continue, {'attr': {'class': 'btn btn-secondary btn-block'}}) }}
                                    </div>
                                </div>
                            </div>

                            {% if isEdit %}
                                <!-- Informace o katalogu -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5>Informace</h5>
                                    </div>
                                    <div class="card-body">
                                        <small class="text-muted">
                                            <strong>ID:</strong> {{ catalog.id_catalog }}<br>
                                            <strong>Vytvořeno:</strong> {{ catalog.date_add|date('d.m.Y H:i') }}<br>
                                            {% if catalog.date_upd %}
                                                <strong>Upraveno:</strong> {{ catalog.date_upd|date('d.m.Y H:i') }}<br>
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Validace formuláře
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.catalog-form');
            
            form.addEventListener('submit', function(e) {
                let isValid = true;
                const requiredFields = form.querySelectorAll('input[required], textarea[required]');
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    alert('Prosím vyplňte všechna povinná pole.');
                }
            });

            // Náhled obrázku
            const imageInput = document.querySelector('#catalog_image_file');
            if (imageInput) {
                imageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            let preview = document.querySelector('.image-preview');
                            if (!preview) {
                                preview = document.createElement('div');
                                preview.className = 'image-preview mt-2';
                                imageInput.parentNode.appendChild(preview);
                            }
                            preview.innerHTML = '<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">';
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>
{% endblock %}
