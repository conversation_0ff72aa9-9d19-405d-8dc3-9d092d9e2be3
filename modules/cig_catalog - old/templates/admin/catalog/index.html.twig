{% extends '@PrestaShop/Admin/layout.html.twig' %}

{% block content %}
<div class="content-div">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="card-title">
                                <i class="material-icons">library_books</i>
                                Správa katalogů
                            </h3>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_catalog_new') }}" class="btn btn-primary">
                                <i class="material-icons">add</i>
                                Nový katalog
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Vyhledávání -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" action="{{ path('admin_catalog_index') }}" class="form-inline">
                                <div class="input-group">
                                    <input type="text" name="search" value="{{ search }}" 
                                           class="form-control" placeholder="Hledat katalogy...">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-outline-secondary">
                                            <i class="material-icons">search</i>
                                        </button>
                                        {% if search %}
                                            <a href="{{ path('admin_catalog_index') }}" class="btn btn-outline-secondary">
                                                <i class="material-icons">clear</i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <span class="text-muted">
                                Celkem: {{ totalCatalogs }} katalogů
                            </span>
                        </div>
                    </div>

                    <!-- Hromadné akce -->
                    {% if catalogs|length > 0 %}
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="bulk-actions" style="display: none;">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-success bulk-action" data-action="activate">
                                            <i class="material-icons">visibility</i>
                                            Aktivovat
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning bulk-action" data-action="deactivate">
                                            <i class="material-icons">visibility_off</i>
                                            Deaktivovat
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger bulk-action" data-action="delete">
                                            <i class="material-icons">delete</i>
                                            Smazat
                                        </button>
                                    </div>
                                    <span class="ml-2 selected-count">0 vybraných</span>
                                </div>
                            </div>
                        </div>

                        <!-- Tabulka katalogů -->
                        <div class="table-responsive">
                            <table id="sortable-catalogs" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                            <span class="ml-2">Řazení</span>
                                        </th>
                                        <th width="60">Obrázek</th>
                                        <th>Název</th>
                                        <th width="100">Pozice</th>
                                        <th width="100">Stav</th>
                                        <th width="80">Nový</th>
                                        <th width="150">Vytvořeno</th>
                                        <th width="200">Akce</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for catalog in catalogs %}
                                        <tr data-catalog-id="{{ catalog.id_catalog }}">
                                            <td>
                                                <input type="checkbox" class="catalog-checkbox form-check-input"
                                                       value="{{ catalog.id_catalog }}">
                                            </td>
                                            <td>
                                                {% if catalog.image %}
                                                    <img src="{{ asset('modules/cig_catalog/uploads/images/' ~ catalog.image) }}" 
                                                         alt="{{ catalog.name }}" class="img-thumbnail" 
                                                         style="max-width: 50px; max-height: 50px;">
                                                {% else %}
                                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="material-icons text-muted">image</i>
                                                    </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <strong>{{ catalog.name }}</strong>
                                                {% if catalog.description %}
                                                    <br><small class="text-muted">{{ catalog.description|slice(0, 100) }}{% if catalog.description|length > 100 %}...{% endif %}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary position-number">{{ catalog.position }}</span>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ catalog.active ? 'success' : 'danger' }}">
                                                    {{ catalog.active ? 'Aktivní' : 'Neaktivní' }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if catalog.is_new %}
                                                    <span class="badge badge-info">Nový</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ catalog.date_add|date('d.m.Y H:i') }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ path('admin_catalog_edit', {id: catalog.id_catalog}) }}" 
                                                       class="btn btn-sm btn-outline-primary" title="Editovat">
                                                        <i class="material-icons">edit</i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-secondary toggle-active" 
                                                            data-id="{{ catalog.id_catalog }}" 
                                                            title="{{ catalog.active ? 'Deaktivovat' : 'Aktivovat' }}">
                                                        <i class="material-icons">
                                                            {{ catalog.active ? 'visibility_off' : 'visibility' }}
                                                        </i>
                                                    </button>
                                                    <a href="{{ path('admin_catalog_duplicate', {id: catalog.id_catalog}) }}" 
                                                       class="btn btn-sm btn-outline-info" title="Duplikovat">
                                                        <i class="material-icons">content_copy</i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger delete-catalog" 
                                                            data-id="{{ catalog.id_catalog }}" title="Smazat">
                                                        <i class="material-icons">delete</i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Stránkování -->
                        {% if totalPages > 1 %}
                            <nav aria-label="Stránkování katalogů">
                                <ul class="pagination justify-content-center">
                                    {% if currentPage > 1 %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ path('admin_catalog_index', {page: currentPage - 1, search: search}) }}">
                                                Předchozí
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for page in 1..totalPages %}
                                        {% if page == currentPage %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page }}</span>
                                            </li>
                                        {% elseif page <= currentPage + 2 and page >= currentPage - 2 %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ path('admin_catalog_index', {page: page, search: search}) }}">
                                                    {{ page }}
                                                </a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if currentPage < totalPages %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ path('admin_catalog_index', {page: currentPage + 1, search: search}) }}">
                                                Další
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="material-icons" style="font-size: 48px;">library_books</i>
                            <h4>Zatím nebyly vytvořeny žádné katalogy</h4>
                            <p>Začněte vytvořením prvního katalogu.</p>
                            <a href="{{ path('admin_catalog_new') }}" class="btn btn-primary">
                                <i class="material-icons">add</i>
                                Vytvořit první katalog
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF tokeny pro AJAX -->
<div id="csrf-tokens" style="display: none;">
    {% for catalog in catalogs %}
        <input type="hidden" id="delete-token-{{ catalog.id_catalog }}"
               value="{{ csrf_token('delete_catalog_' ~ catalog.id_catalog) }}">
        <input type="hidden" id="toggle-token-{{ catalog.id_catalog }}"
               value="{{ csrf_token('toggle_active_' ~ catalog.id_catalog) }}">
    {% endfor %}
    <input type="hidden" id="bulk-token" value="{{ csrf_token('bulk_action') }}">
    <input type="hidden" id="reorder-token" value="{{ csrf_token('reorder_catalogs') }}">
</div>

<!-- JavaScript proměnné -->
<script>
    window.catalogAdminUrls = {
        reorder: '{{ path('admin_catalog_reorder') }}',
        upload: '{{ path('admin_catalog_upload') }}',
        bulk: '{{ path('admin_catalog_bulk_action') }}'
    };
</script>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('modules/cig_catalog/views/js/admin-catalog.js') }}"></script>
{% endblock %}
