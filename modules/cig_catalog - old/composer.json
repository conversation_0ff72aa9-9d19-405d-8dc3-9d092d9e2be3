{"name": "cig/catalog-module", "description": "Advanced catalog management module for PrestaShop 8.2.0 with ordering system and email notifications", "type": "prestashop-module", "keywords": ["prestashop", "module", "catalog", "e-commerce", "cms", "ordering", "email"], "homepage": "https://github.com/cig-development/prestashop-catalog-module", "license": "MIT", "authors": [{"name": "CIG Development Team", "email": "<EMAIL>", "homepage": "https://cig.cz", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/cig-development/prestashop-catalog-module/issues", "docs": "https://docs.cig.cz/prestashop-catalog-module"}, "require": {"php": ">=7.4", "ext-gd": "*", "ext-curl": "*", "ext-mbstring": "*", "ext-zip": "*", "ext-openssl": "*"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.6", "phpstan/phpstan": "^1.8", "phpdocumentor/phpdocumentor": "^3.3", "friendsofphp/php-cs-fixer": "^3.8"}, "autoload": {"psr-4": {"CigCatalog\\": "src/"}}, "autoload-dev": {"psr-4": {"CigCatalog\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit --configuration phpunit.xml", "test-unit": "phpunit --configuration phpunit.xml --testsuite 'Unit Tests'", "test-integration": "phpunit --configuration phpunit.xml --testsuite 'Integration Tests'", "test-performance": "phpunit --configuration phpunit.xml --testsuite 'Performance Tests'", "test-security": "phpunit --configuration phpunit.xml --testsuite 'Security Tests'", "test-coverage": "phpunit --configuration phpunit.xml --coverage-html coverage-html --coverage-text", "lint": "phpcs --standard=PSR12 src/ classes/ controllers/", "lint-fix": "phpcbf --standard=PSR12 src/ classes/ controllers/", "analyze": "phpstan analyse src/ classes/ controllers/ --level=5", "docs": "phpdoc -d src/,classes/,controllers/ -t docs/api", "validate": ["@lint", "@analyze", "@test"], "post-install-cmd": ["@php -r \"if (!file_exists('uploads/images')) mkdir('uploads/images', 0755, true);\"", "@php -r \"if (!file_exists('uploads/files')) mkdir('uploads/files', 0755, true);\""]}, "scripts-descriptions": {"test": "Run all tests", "test-unit": "Run unit tests only", "test-integration": "Run integration tests only", "test-performance": "Run performance tests only", "test-security": "Run security tests only", "test-coverage": "Run tests with coverage report", "lint": "Run code style checks", "lint-fix": "Fix code style issues", "analyze": "Run static analysis", "docs": "Generate API documentation", "validate": "Run all validation checks"}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "extra": {"prestashop": {"name": "CIG Catalog", "displayName": "CIG Catalog - Advanced Catalog Management", "version": "1.0.0", "author": "CIG Development Team", "tab": "administration", "is_configurable": 1, "need_instance": 0, "limited_countries": [], "parent_class_name": "AdminParentModulesSf", "class_name": "AdminCatalog"}, "branch-alias": {"dev-main": "1.0.x-dev"}}, "minimum-stability": "stable", "prefer-stable": true}