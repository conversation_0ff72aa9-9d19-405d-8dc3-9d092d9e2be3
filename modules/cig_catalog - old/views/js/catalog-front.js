/**
 * CIG Catalog Frontend JavaScript
 * 
 * Handles frontend interactions for catalog display and ordering
 */

(function() {
    'use strict';

    // Configuration from template
    const config = window.catalogConfig || {};
    
    // DOM elements
    const elements = {
        searchForm: document.getElementById('catalog-search-form'),
        searchInput: document.getElementById('search-input'),
        catalogsContainer: document.getElementById('catalogs-container'),
        catalogsGrid: document.getElementById('catalogs-grid'),
        loadingIndicator: document.getElementById('loading-indicator'),
        orderModal: document.getElementById('orderModal'),
        orderForm: document.getElementById('catalog-order-form'),
        selectedCatalogInfo: document.getElementById('selected-catalog-info'),
        selectedCatalogName: document.getElementById('selected-catalog-name'),
        formMessages: document.getElementById('form-messages')
    };

    // State
    let currentRequest = null;
    let isLoading = false;

    /**
     * Initialize the catalog frontend
     */
    function init() {
        bindEvents();
        initializeOrderForm();
        setupLazyLoading();
    }

    /**
     * Bind event listeners
     */
    function bindEvents() {
        // Search form
        if (elements.searchForm) {
            elements.searchForm.addEventListener('submit', handleSearch);
        }

        // Download buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-download')) {
                e.preventDefault();
                handleDownload(e.target.closest('.btn-download'));
            }
        });

        // Order buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-order')) {
                e.preventDefault();
                handleOrderClick(e.target.closest('.btn-order'));
            }
        });

        // Details buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-details')) {
                e.preventDefault();
                handleDetailsClick(e.target.closest('.btn-details'));
            }
        });

        // Order form submission
        if (elements.orderForm) {
            elements.orderForm.addEventListener('submit', handleOrderSubmit);
        }

        // Real-time form validation
        if (elements.orderForm) {
            const inputs = elements.orderForm.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => validateField(input));
                input.addEventListener('input', () => clearFieldError(input));
            });
        }

        // Modal events
        if (elements.orderModal) {
            elements.orderModal.addEventListener('hidden.bs.modal', resetOrderForm);
        }
    }

    /**
     * Handle search form submission
     */
    function handleSearch(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        const query = elements.searchInput.value.trim();
        const url = new URL(window.location);
        
        if (query) {
            url.searchParams.set('search', query);
        } else {
            url.searchParams.delete('search');
        }
        
        url.searchParams.delete('page'); // Reset to first page
        
        window.location.href = url.toString();
    }

    /**
     * Handle catalog download
     */
    function handleDownload(button) {
        const catalogId = button.dataset.catalogId;
        const catalogName = button.dataset.catalogName;
        
        if (!catalogId) {
            showNotification('error', config.translations.error);
            return;
        }

        // Confirm download
        if (!confirm(config.translations.confirmDownload)) {
            return;
        }

        // Show loading state
        setButtonLoading(button, true);
        
        // Make AJAX request
        fetch(config.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'download',
                catalog_id: catalogId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Start download
                const link = document.createElement('a');
                link.href = data.download_url;
                link.download = data.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showNotification('success', config.translations.downloadSuccess);
            } else {
                showNotification('error', data.error || config.translations.error);
            }
        })
        .catch(error => {
            console.error('Download error:', error);
            showNotification('error', config.translations.error);
        })
        .finally(() => {
            setButtonLoading(button, false);
        });
    }

    /**
     * Handle order button click
     */
    function handleOrderClick(button) {
        const catalogId = button.dataset.catalogId;
        const catalogName = button.dataset.catalogName;
        
        if (!catalogId || !catalogName) {
            showNotification('error', config.translations.error);
            return;
        }

        // Set catalog info in modal
        document.getElementById('catalog_id').value = catalogId;
        elements.selectedCatalogName.textContent = catalogName;
        elements.selectedCatalogInfo.style.display = 'block';
        
        // Reset form
        resetOrderForm();
    }

    /**
     * Handle details button click
     */
    function handleDetailsClick(button) {
        const catalogId = button.dataset.catalogId;
        const detailsModal = document.getElementById(`detailsModal-${catalogId}`);
        
        if (detailsModal) {
            const modal = new bootstrap.Modal(detailsModal);
            modal.show();
        }
    }

    /**
     * Handle order form submission
     */
    function handleOrderSubmit(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        // Validate form
        if (!validateForm()) {
            return;
        }

        const formData = new FormData(elements.orderForm);
        const submitButton = elements.orderForm.querySelector('#submit-order-btn');
        
        // Show loading state
        setButtonLoading(submitButton, true);
        isLoading = true;
        hideFormMessages();
        
        // Make AJAX request
        fetch(config.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFormMessage('success', data.message || config.translations.orderSuccess);
                
                // Close modal after delay
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(elements.orderModal);
                    modal.hide();
                }, 2000);
                
            } else {
                showFormMessage('error', data.error || config.translations.error);
            }
        })
        .catch(error => {
            console.error('Order error:', error);
            showFormMessage('error', config.translations.error);
        })
        .finally(() => {
            setButtonLoading(submitButton, false);
            isLoading = false;
        });
    }

    /**
     * Validate entire form
     */
    function validateForm() {
        const inputs = elements.orderForm.querySelectorAll('input[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    /**
     * Validate individual field
     */
    function validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const name = field.name;
        let isValid = true;
        let message = '';
        
        // Required validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = window.orderFormValidation.messages.required;
        }
        
        // Email validation
        else if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = window.orderFormValidation.messages.email;
            }
        }
        
        // Company ID validation (Czech IČO)
        else if (name === 'company_id' && value) {
            if (!validateCompanyId(value)) {
                isValid = false;
                message = window.orderFormValidation.messages.companyId;
            }
        }
        
        // Phone validation
        else if (name === 'phone' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{9,}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = window.orderFormValidation.messages.phone;
            }
        }
        
        // Length validation
        else if (value) {
            const minLength = field.getAttribute('minlength');
            const maxLength = field.getAttribute('maxlength');
            
            if (minLength && value.length < parseInt(minLength)) {
                isValid = false;
                message = window.orderFormValidation.messages.minLength;
            } else if (maxLength && value.length > parseInt(maxLength)) {
                isValid = false;
                message = window.orderFormValidation.messages.maxLength;
            }
        }
        
        // Update field state
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }
        
        // Show/hide error message
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
        }
        
        return isValid;
    }

    /**
     * Validate Czech company ID (IČO)
     */
    function validateCompanyId(ico) {
        // Remove spaces and non-digits
        ico = ico.replace(/\D/g, '');
        
        // Must be 8 digits
        if (ico.length !== 8) {
            return false;
        }
        
        // Modulo 11 check
        let sum = 0;
        for (let i = 0; i < 7; i++) {
            sum += parseInt(ico[i]) * (8 - i);
        }
        
        const remainder = sum % 11;
        const checkDigit = remainder < 2 ? remainder : 11 - remainder;
        
        return parseInt(ico[7]) === checkDigit;
    }

    /**
     * Clear field error state
     */
    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    }

    /**
     * Reset order form
     */
    function resetOrderForm() {
        if (!elements.orderForm) return;
        
        elements.orderForm.reset();
        elements.selectedCatalogInfo.style.display = 'none';
        
        // Clear validation states
        const fields = elements.orderForm.querySelectorAll('.form-control, .form-check-input');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        hideFormMessages();
    }

    /**
     * Show form message
     */
    function showFormMessage(type, message) {
        if (!elements.formMessages) return;
        
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alert = elements.formMessages.querySelector('.alert');
        
        alert.className = `alert ${alertClass}`;
        alert.textContent = message;
        elements.formMessages.style.display = 'block';
    }

    /**
     * Hide form messages
     */
    function hideFormMessages() {
        if (elements.formMessages) {
            elements.formMessages.style.display = 'none';
        }
    }

    /**
     * Set button loading state
     */
    function setButtonLoading(button, loading) {
        if (!button) return;

        const textSpan = button.querySelector('.btn-text');
        const loadingSpan = button.querySelector('.btn-loading');

        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            if (textSpan) textSpan.style.display = 'none';
            if (loadingSpan) loadingSpan.style.display = 'inline-block';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            if (textSpan) textSpan.style.display = 'inline-block';
            if (loadingSpan) loadingSpan.style.display = 'none';
        }
    }

    /**
     * Show notification
     */
    function showNotification(type, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Initialize order form
     */
    function initializeOrderForm() {
        // Set up form validation messages
        if (typeof window.orderFormValidation === 'undefined') {
            window.orderFormValidation = {
                messages: {
                    required: 'This field is required',
                    email: 'Please enter a valid email address',
                    minLength: 'This field is too short',
                    maxLength: 'This field is too long',
                    companyId: 'Please enter a valid company ID',
                    phone: 'Please enter a valid phone number'
                }
            };
        }
    }

    /**
     * Setup lazy loading for images
     */
    function setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            const lazyImages = document.querySelectorAll('img.lazy');
            lazyImages.forEach(img => imageObserver.observe(img));
        }
    }

    /**
     * Handle AJAX search (for future implementation)
     */
    function performAjaxSearch(query, page = 1) {
        if (currentRequest) {
            currentRequest.abort();
        }

        showLoading(true);

        currentRequest = fetch(config.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'search',
                q: query,
                page: page
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCatalogsGrid(data.catalogs);
                updatePagination(data.page, data.total_pages);
            } else {
                showNotification('error', data.error || config.translations.error);
            }
        })
        .catch(error => {
            if (error.name !== 'AbortError') {
                console.error('Search error:', error);
                showNotification('error', config.translations.error);
            }
        })
        .finally(() => {
            showLoading(false);
            currentRequest = null;
        });
    }

    /**
     * Update catalogs grid with new data
     */
    function updateCatalogsGrid(catalogs) {
        if (!elements.catalogsGrid) return;

        // This would be implemented for AJAX search
        // For now, we use page reload for simplicity
    }

    /**
     * Update pagination
     */
    function updatePagination(currentPage, totalPages) {
        // This would be implemented for AJAX search
        // For now, we use page reload for simplicity
    }

    /**
     * Show/hide loading indicator
     */
    function showLoading(show) {
        if (elements.loadingIndicator) {
            elements.loadingIndicator.style.display = show ? 'block' : 'none';
        }

        if (elements.catalogsContainer) {
            elements.catalogsContainer.style.opacity = show ? '0.5' : '1';
        }

        isLoading = show;
    }

    /**
     * Debounce function for search input
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Handle keyboard navigation
     */
    function handleKeyboardNavigation(e) {
        // ESC key closes modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }

        // Enter key in search submits form
        if (e.key === 'Enter' && e.target === elements.searchInput) {
            e.preventDefault();
            elements.searchForm.dispatchEvent(new Event('submit'));
        }
    }

    // Bind keyboard events
    document.addEventListener('keydown', handleKeyboardNavigation);

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
