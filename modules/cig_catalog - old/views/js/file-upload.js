/**
 * File Upload Manager
 * Pokročilý drag&drop upload systém s progress bary a validací
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license MIT
 */

class FileUploadManager {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error('Upload container not found:', containerId);
            return;
        }

        this.options = {
            maxFileSize: 50 * 1024 * 1024, // 50MB
            allowedTypes: {
                image: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
                file: ['application/pdf', 'application/zip', 'application/msword', 
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                       'application/vnd.ms-excel',
                       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
            },
            uploadUrl: '/admin/catalog/upload',
            multiple: false,
            autoUpload: true,
            ...options
        };

        this.uploadQueue = [];
        this.activeUploads = 0;
        this.maxConcurrentUploads = 3;

        this.init();
    }

    init() {
        this.setupDropZone();
        this.setupFileInput();
        this.setupProgressContainer();
        this.bindEvents();
    }

    setupDropZone() {
        const dropZone = this.container.querySelector('.drop-zone');
        if (!dropZone) {
            console.error('Drop zone not found in container');
            return;
        }

        this.dropZone = dropZone;

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => this.highlight(), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => this.unhighlight(), false);
        });

        // Handle dropped files
        dropZone.addEventListener('drop', this.handleDrop.bind(this), false);
    }

    setupFileInput() {
        const fileInput = this.container.querySelector('input[type="file"]');
        if (fileInput) {
            this.fileInput = fileInput;
            fileInput.addEventListener('change', (e) => {
                this.handleFiles(e.target.files);
            });
        }
    }

    setupProgressContainer() {
        let progressContainer = this.container.querySelector('.upload-progress');
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.className = 'upload-progress';
            this.container.appendChild(progressContainer);
        }
        this.progressContainer = progressContainer;
    }

    bindEvents() {
        // Browse button
        const browseBtn = this.container.querySelector('.browse-btn');
        if (browseBtn && this.fileInput) {
            browseBtn.addEventListener('click', () => {
                this.fileInput.click();
            });
        }

        // Clear button
        const clearBtn = this.container.querySelector('.clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearUploads();
            });
        }
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight() {
        this.dropZone.classList.add('drag-over');
    }

    unhighlight() {
        this.dropZone.classList.remove('drag-over');
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        this.handleFiles(files);
    }

    handleFiles(files) {
        const fileArray = Array.from(files);
        
        if (!this.options.multiple && fileArray.length > 1) {
            this.showError('Lze nahrát pouze jeden soubor najednou.');
            return;
        }

        fileArray.forEach(file => {
            if (this.validateFile(file)) {
                this.addToQueue(file);
            }
        });

        if (this.options.autoUpload) {
            this.processQueue();
        }
    }

    validateFile(file) {
        // Check file size
        if (file.size > this.options.maxFileSize) {
            this.showError(`Soubor "${file.name}" je příliš velký. Maximum je ${this.formatFileSize(this.options.maxFileSize)}.`);
            return false;
        }

        // Check file type
        const isValidType = Object.values(this.options.allowedTypes).flat().includes(file.type);
        if (!isValidType) {
            this.showError(`Soubor "${file.name}" má nepodporovaný formát.`);
            return false;
        }

        return true;
    }

    addToQueue(file) {
        const uploadItem = {
            id: this.generateId(),
            file: file,
            status: 'queued',
            progress: 0,
            xhr: null
        };

        this.uploadQueue.push(uploadItem);
        this.createProgressItem(uploadItem);
    }

    createProgressItem(uploadItem) {
        const progressItem = document.createElement('div');
        progressItem.className = 'upload-item';
        progressItem.dataset.uploadId = uploadItem.id;

        progressItem.innerHTML = `
            <div class="upload-info">
                <div class="file-name">${uploadItem.file.name}</div>
                <div class="file-size">${this.formatFileSize(uploadItem.file.size)}</div>
            </div>
            <div class="upload-progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="upload-status">Ve frontě...</div>
            <button class="upload-cancel" type="button">
                <i class="material-icons">close</i>
            </button>
        `;

        // Cancel button
        const cancelBtn = progressItem.querySelector('.upload-cancel');
        cancelBtn.addEventListener('click', () => {
            this.cancelUpload(uploadItem.id);
        });

        this.progressContainer.appendChild(progressItem);
    }

    processQueue() {
        while (this.activeUploads < this.maxConcurrentUploads && this.uploadQueue.length > 0) {
            const uploadItem = this.uploadQueue.find(item => item.status === 'queued');
            if (uploadItem) {
                this.uploadFile(uploadItem);
            } else {
                break;
            }
        }
    }

    uploadFile(uploadItem) {
        uploadItem.status = 'uploading';
        this.activeUploads++;

        const formData = new FormData();
        formData.append('file', uploadItem.file);
        formData.append('_token', this.getCsrfToken());

        const xhr = new XMLHttpRequest();
        uploadItem.xhr = xhr;

        // Progress tracking
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                this.updateProgress(uploadItem.id, percentComplete);
            }
        });

        // Upload complete
        xhr.addEventListener('load', () => {
            this.activeUploads--;
            
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        this.handleUploadSuccess(uploadItem, response);
                    } else {
                        this.handleUploadError(uploadItem, response.message || 'Chyba při nahrávání');
                    }
                } catch (e) {
                    this.handleUploadError(uploadItem, 'Chyba při zpracování odpovědi serveru');
                }
            } else {
                this.handleUploadError(uploadItem, `HTTP chyba: ${xhr.status}`);
            }

            this.processQueue(); // Continue with next uploads
        });

        // Upload error
        xhr.addEventListener('error', () => {
            this.activeUploads--;
            this.handleUploadError(uploadItem, 'Chyba při komunikaci se serverem');
            this.processQueue();
        });

        // Upload abort
        xhr.addEventListener('abort', () => {
            this.activeUploads--;
            this.handleUploadCancel(uploadItem);
            this.processQueue();
        });

        // Start upload
        this.updateStatus(uploadItem.id, 'Nahrávání...');
        xhr.open('POST', this.options.uploadUrl);
        xhr.send(formData);
    }

    updateProgress(uploadId, percent) {
        const progressItem = this.progressContainer.querySelector(`[data-upload-id="${uploadId}"]`);
        if (progressItem) {
            const progressFill = progressItem.querySelector('.progress-fill');
            const status = progressItem.querySelector('.upload-status');
            
            progressFill.style.width = `${percent}%`;
            status.textContent = `${Math.round(percent)}%`;
        }
    }

    updateStatus(uploadId, status) {
        const progressItem = this.progressContainer.querySelector(`[data-upload-id="${uploadId}"]`);
        if (progressItem) {
            const statusElement = progressItem.querySelector('.upload-status');
            statusElement.textContent = status;
        }
    }

    handleUploadSuccess(uploadItem, response) {
        uploadItem.status = 'completed';
        uploadItem.response = response;

        this.updateStatus(uploadItem.id, 'Dokončeno');
        this.updateProgress(uploadItem.id, 100);

        // Hide cancel button
        const progressItem = this.progressContainer.querySelector(`[data-upload-id="${uploadItem.id}"]`);
        if (progressItem) {
            const cancelBtn = progressItem.querySelector('.upload-cancel');
            cancelBtn.style.display = 'none';
            progressItem.classList.add('upload-success');
        }

        // Trigger success callback
        if (this.options.onSuccess) {
            this.options.onSuccess(uploadItem, response);
        }

        // Auto-remove after delay
        setTimeout(() => {
            this.removeProgressItem(uploadItem.id);
        }, 3000);
    }

    handleUploadError(uploadItem, message) {
        uploadItem.status = 'error';
        uploadItem.error = message;

        this.updateStatus(uploadItem.id, `Chyba: ${message}`);

        const progressItem = this.progressContainer.querySelector(`[data-upload-id="${uploadItem.id}"]`);
        if (progressItem) {
            progressItem.classList.add('upload-error');
        }

        // Trigger error callback
        if (this.options.onError) {
            this.options.onError(uploadItem, message);
        }
    }

    handleUploadCancel(uploadItem) {
        uploadItem.status = 'cancelled';
        this.updateStatus(uploadItem.id, 'Zrušeno');
        this.removeProgressItem(uploadItem.id);
    }

    cancelUpload(uploadId) {
        const uploadItem = this.uploadQueue.find(item => item.id === uploadId);
        if (uploadItem) {
            if (uploadItem.xhr) {
                uploadItem.xhr.abort();
            } else {
                // Remove from queue
                const index = this.uploadQueue.indexOf(uploadItem);
                this.uploadQueue.splice(index, 1);
                this.removeProgressItem(uploadId);
            }
        }
    }

    removeProgressItem(uploadId) {
        const progressItem = this.progressContainer.querySelector(`[data-upload-id="${uploadId}"]`);
        if (progressItem) {
            progressItem.remove();
        }

        // Remove from queue
        const index = this.uploadQueue.findIndex(item => item.id === uploadId);
        if (index !== -1) {
            this.uploadQueue.splice(index, 1);
        }
    }

    clearUploads() {
        // Cancel all active uploads
        this.uploadQueue.forEach(item => {
            if (item.xhr) {
                item.xhr.abort();
            }
        });

        // Clear queue and UI
        this.uploadQueue = [];
        this.activeUploads = 0;
        this.progressContainer.innerHTML = '';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    generateId() {
        return 'upload_' + Math.random().toString(36).substr(2, 9);
    }

    getCsrfToken() {
        const tokenElement = document.querySelector('meta[name="csrf-token"]') || 
                           document.getElementById('upload-token');
        return tokenElement ? tokenElement.content || tokenElement.value : '';
    }

    showError(message) {
        // Use existing notification system or fallback to alert
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else if (window.FileUploadManager && window.FileUploadManager.showNotification) {
            window.FileUploadManager.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

// Global notification function for file uploads
window.FileUploadManager = {
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show upload-notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
            min-width: 300px;
            max-width: 500px;
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" onclick="this.parentElement.remove()">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
};
