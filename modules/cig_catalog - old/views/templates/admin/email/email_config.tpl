{**
 * Email Configuration Template
 * 
 * Admin template for email configuration
 *}

<div class="panel">
    <div class="panel-heading">
        <i class="icon-envelope"></i>
        {l s='Email Configuration' mod='cig_catalog'}
    </div>
    
    <div class="panel-body">
        
        {* Configuration Form *}
        <form id="email-config-form" class="form-horizontal" method="post">
            
            {* Basic Email Settings *}
            <div class="form-group">
                <h4 class="col-lg-12">{l s='Basic Email Settings' mod='cig_catalog'}</h4>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3 required">
                    {l s='Admin Email' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <input type="email" 
                           name="admin_email" 
                           id="admin_email"
                           class="form-control" 
                           value="{$config.admin_email|escape:'html':'UTF-8'}"
                           required>
                    <p class="help-block">{l s='Email address for receiving order notifications' mod='cig_catalog'}</p>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3 required">
                    {l s='From Name' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <input type="text" 
                           name="from_name" 
                           id="from_name"
                           class="form-control" 
                           value="{$config.from_name|escape:'html':'UTF-8'}"
                           required>
                    <p class="help-block">{l s='Name displayed as sender in emails' mod='cig_catalog'}</p>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3 required">
                    {l s='From Email' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <input type="email" 
                           name="from_email" 
                           id="from_email"
                           class="form-control" 
                           value="{$config.from_email|escape:'html':'UTF-8'}"
                           required>
                    <p class="help-block">{l s='Email address used as sender' mod='cig_catalog'}</p>
                </div>
            </div>
            
            {* SMTP Settings *}
            <div class="form-group">
                <h4 class="col-lg-12">{l s='SMTP Settings' mod='cig_catalog'}</h4>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Enable SMTP' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" 
                               name="smtp_enabled" 
                               id="smtp_enabled_on" 
                               value="1" 
                               {if $config.smtp_enabled}checked="checked"{/if}>
                        <label for="smtp_enabled_on">{l s='Yes' mod='cig_catalog'}</label>
                        <input type="radio" 
                               name="smtp_enabled" 
                               id="smtp_enabled_off" 
                               value="0" 
                               {if !$config.smtp_enabled}checked="checked"{/if}>
                        <label for="smtp_enabled_off">{l s='No' mod='cig_catalog'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">{l s='Use SMTP server for sending emails' mod='cig_catalog'}</p>
                </div>
            </div>
            
            <div id="smtp-settings" style="display: {if $config.smtp_enabled}block{else}none{/if};">
                
                <div class="form-group">
                    <label class="control-label col-lg-3">
                        {l s='SMTP Host' mod='cig_catalog'}
                    </label>
                    <div class="col-lg-6">
                        <input type="text" 
                               name="smtp_host" 
                               id="smtp_host"
                               class="form-control" 
                               value="{$config.smtp_host|escape:'html':'UTF-8'}">
                        <p class="help-block">{l s='SMTP server hostname' mod='cig_catalog'}</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="control-label col-lg-3">
                        {l s='SMTP Port' mod='cig_catalog'}
                    </label>
                    <div class="col-lg-6">
                        <input type="number" 
                               name="smtp_port" 
                               id="smtp_port"
                               class="form-control" 
                               value="{$config.smtp_port}"
                               min="1" 
                               max="65535">
                        <p class="help-block">{l s='SMTP server port (usually 587 for TLS, 465 for SSL)' mod='cig_catalog'}</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="control-label col-lg-3">
                        {l s='SMTP Username' mod='cig_catalog'}
                    </label>
                    <div class="col-lg-6">
                        <input type="text" 
                               name="smtp_username" 
                               id="smtp_username"
                               class="form-control" 
                               value="{$config.smtp_username|escape:'html':'UTF-8'}">
                        <p class="help-block">{l s='SMTP authentication username' mod='cig_catalog'}</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="control-label col-lg-3">
                        {l s='SMTP Password' mod='cig_catalog'}
                    </label>
                    <div class="col-lg-6">
                        <input type="password" 
                               name="smtp_password" 
                               id="smtp_password"
                               class="form-control" 
                               value="{$config.smtp_password|escape:'html':'UTF-8'}">
                        <p class="help-block">{l s='SMTP authentication password' mod='cig_catalog'}</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="control-label col-lg-3">
                        {l s='SMTP Encryption' mod='cig_catalog'}
                    </label>
                    <div class="col-lg-6">
                        <select name="smtp_encryption" id="smtp_encryption" class="form-control">
                            <option value="none" {if $config.smtp_encryption == 'none'}selected{/if}>
                                {l s='None' mod='cig_catalog'}
                            </option>
                            <option value="ssl" {if $config.smtp_encryption == 'ssl'}selected{/if}>
                                {l s='SSL' mod='cig_catalog'}
                            </option>
                            <option value="tls" {if $config.smtp_encryption == 'tls'}selected{/if}>
                                {l s='TLS' mod='cig_catalog'}
                            </option>
                        </select>
                        <p class="help-block">{l s='SMTP encryption method' mod='cig_catalog'}</p>
                    </div>
                </div>
                
            </div>
            
            {* Notification Settings *}
            <div class="form-group">
                <h4 class="col-lg-12">{l s='Notification Settings' mod='cig_catalog'}</h4>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Order Notifications' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" 
                               name="enable_order_notifications" 
                               id="order_notifications_on" 
                               value="1" 
                               {if $config.enable_order_notifications}checked="checked"{/if}>
                        <label for="order_notifications_on">{l s='Yes' mod='cig_catalog'}</label>
                        <input type="radio" 
                               name="enable_order_notifications" 
                               id="order_notifications_off" 
                               value="0" 
                               {if !$config.enable_order_notifications}checked="checked"{/if}>
                        <label for="order_notifications_off">{l s='No' mod='cig_catalog'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">{l s='Send email notifications to admin when new orders are received' mod='cig_catalog'}</p>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Order Confirmations' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" 
                               name="enable_order_confirmations" 
                               id="order_confirmations_on" 
                               value="1" 
                               {if $config.enable_order_confirmations}checked="checked"{/if}>
                        <label for="order_confirmations_on">{l s='Yes' mod='cig_catalog'}</label>
                        <input type="radio" 
                               name="enable_order_confirmations" 
                               id="order_confirmations_off" 
                               value="0" 
                               {if !$config.enable_order_confirmations}checked="checked"{/if}>
                        <label for="order_confirmations_off">{l s='No' mod='cig_catalog'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">{l s='Send confirmation emails to customers' mod='cig_catalog'}</p>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Admin Alerts' mod='cig_catalog'}
                </label>
                <div class="col-lg-6">
                    <span class="switch prestashop-switch fixed-width-lg">
                        <input type="radio" 
                               name="enable_admin_alerts" 
                               id="admin_alerts_on" 
                               value="1" 
                               {if $config.enable_admin_alerts}checked="checked"{/if}>
                        <label for="admin_alerts_on">{l s='Yes' mod='cig_catalog'}</label>
                        <input type="radio" 
                               name="enable_admin_alerts" 
                               id="admin_alerts_off" 
                               value="0" 
                               {if !$config.enable_admin_alerts}checked="checked"{/if}>
                        <label for="admin_alerts_off">{l s='No' mod='cig_catalog'}</label>
                        <a class="slide-button btn"></a>
                    </span>
                    <p class="help-block">{l s='Send admin alerts for errors and warnings' mod='cig_catalog'}</p>
                </div>
            </div>
            
            {* Form Actions *}
            <div class="panel-footer">
                <button type="submit" name="submitEmailConfig" class="btn btn-default pull-right">
                    <i class="process-icon-save"></i> {l s='Save Configuration' mod='cig_catalog'}
                </button>
            </div>
            
        </form>

    </div>
</div>

{* Email Testing Panel *}
<div class="panel">
    <div class="panel-heading">
        <i class="icon-cog"></i>
        {l s='Email Testing' mod='cig_catalog'}
    </div>

    <div class="panel-body">

        <div class="row">
            <div class="col-lg-6">
                <h4>{l s='Test Email Configuration' mod='cig_catalog'}</h4>
                <p>{l s='Send a test email to verify your configuration is working correctly.' mod='cig_catalog'}</p>

                <div class="form-group">
                    <label for="test_email">{l s='Test Email Address' mod='cig_catalog'}</label>
                    <input type="email"
                           id="test_email"
                           class="form-control"
                           placeholder="{l s='Enter email address...' mod='cig_catalog'}"
                           value="{$config.admin_email|escape:'html':'UTF-8'}">
                </div>

                <button type="button" id="send-test-email" class="btn btn-primary">
                    <i class="icon-envelope"></i> {l s='Send Test Email' mod='cig_catalog'}
                </button>

                <div id="test-result" class="alert" style="display: none; margin-top: 15px;"></div>
            </div>

            <div class="col-lg-6">
                <h4>{l s='Email Statistics' mod='cig_catalog'}</h4>
                <table class="table table-striped">
                    <tr>
                        <td>{l s='Total Emails Sent' mod='cig_catalog'}</td>
                        <td><strong>{$stats.total_sent}</strong></td>
                    </tr>
                    <tr>
                        <td>{l s='Order Notifications' mod='cig_catalog'}</td>
                        <td><strong>{$stats.order_notifications}</strong></td>
                    </tr>
                    <tr>
                        <td>{l s='Order Confirmations' mod='cig_catalog'}</td>
                        <td><strong>{$stats.order_confirmations}</strong></td>
                    </tr>
                    <tr>
                        <td>{l s='Admin Alerts' mod='cig_catalog'}</td>
                        <td><strong>{$stats.admin_alerts}</strong></td>
                    </tr>
                    <tr>
                        <td>{l s='Failed Attempts' mod='cig_catalog'}</td>
                        <td><strong class="text-danger">{$stats.failed_attempts}</strong></td>
                    </tr>
                    {if $stats.last_sent}
                    <tr>
                        <td>{l s='Last Email Sent' mod='cig_catalog'}</td>
                        <td><strong>{$stats.last_sent}</strong></td>
                    </tr>
                    {/if}
                </table>
            </div>
        </div>

    </div>
</div>

{* Email Templates Panel *}
<div class="panel">
    <div class="panel-heading">
        <i class="icon-file-text"></i>
        {l s='Email Templates' mod='cig_catalog'}
    </div>

    <div class="panel-body">

        <p>{l s='Preview and manage email templates used by the module.' mod='cig_catalog'}</p>

        <div class="row">
            {foreach from=$templates key=template_key item=template_name}
            <div class="col-lg-6 col-md-12 template-item" style="margin-bottom: 15px;">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <h5>{$template_name|escape:'html':'UTF-8'}</h5>
                        <p class="text-muted">{l s='Template:' mod='cig_catalog'} {$template_key}</p>
                        <button type="button"
                                class="btn btn-sm btn-default preview-template"
                                data-template="{$template_key}">
                            <i class="icon-eye"></i> {l s='Preview' mod='cig_catalog'}
                        </button>
                    </div>
                </div>
            </div>
            {/foreach}
        </div>

    </div>
</div>

{* Template Preview Modal *}
<div class="modal fade" id="template-preview-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
                <h4 class="modal-title">{l s='Template Preview' mod='cig_catalog'}</h4>
            </div>
            <div class="modal-body">
                <div id="template-preview-content">
                    <div class="text-center">
                        <i class="icon-spinner icon-spin"></i> {l s='Loading...' mod='cig_catalog'}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    {l s='Close' mod='cig_catalog'}
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {

    // SMTP settings toggle
    $('input[name="smtp_enabled"]').change(function() {
        if ($(this).val() == '1') {
            $('#smtp-settings').show();
        } else {
            $('#smtp-settings').hide();
        }
    });

    // Test email functionality
    $('#send-test-email').click(function() {
        var testEmail = $('#test_email').val();
        var button = $(this);
        var resultDiv = $('#test-result');

        if (!testEmail || !isValidEmail(testEmail)) {
            showTestResult('error', '{l s='Please enter a valid email address' mod='cig_catalog' js=1}');
            return;
        }

        button.prop('disabled', true).html('<i class="icon-spinner icon-spin"></i> {l s='Sending...' mod='cig_catalog' js=1}');

        $.ajax({
            url: '{$test_email_url}',
            type: 'POST',
            data: {
                test_email: testEmail,
                ajax: true
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showTestResult('success', response.message);
                } else {
                    showTestResult('error', response.error || '{l s='Test email failed' mod='cig_catalog' js=1}');
                }
            },
            error: function() {
                showTestResult('error', '{l s='An error occurred while sending test email' mod='cig_catalog' js=1}');
            },
            complete: function() {
                button.prop('disabled', false).html('<i class="icon-envelope"></i> {l s='Send Test Email' mod='cig_catalog' js=1}');
            }
        });
    });

    // Template preview functionality
    $('.preview-template').click(function() {
        var template = $(this).data('template');
        var modal = $('#template-preview-modal');
        var content = $('#template-preview-content');

        content.html('<div class="text-center"><i class="icon-spinner icon-spin"></i> {l s='Loading...' mod='cig_catalog' js=1}</div>');
        modal.modal('show');

        $.ajax({
            url: '{$template_preview_url}',
            type: 'POST',
            data: {
                template: template,
                ajax: true
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    content.html('<iframe srcdoc="' + response.content.replace(/"/g, '&quot;') + '" style="width: 100%; height: 500px; border: 1px solid #ddd;"></iframe>');
                } else {
                    content.html('<div class="alert alert-danger">' + (response.error || '{l s='Failed to load template' mod='cig_catalog' js=1}') + '</div>');
                }
            },
            error: function() {
                content.html('<div class="alert alert-danger">{l s='An error occurred while loading template' mod='cig_catalog' js=1}</div>');
            }
        });
    });

    function showTestResult(type, message) {
        var resultDiv = $('#test-result');
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';

        resultDiv.removeClass('alert-success alert-danger')
                 .addClass(alertClass)
                 .html(message)
                 .show();
    }

    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

});
</script>
