{**
 * CIG Catalog Frontend Template
 * 
 * Main template for displaying catalogs
 *}

{extends file='page.tpl'}

{block name='page_title'}
    <h1 class="h1">{$page_title}</h1>
{/block}

{block name='page_content_container'}
    <section id="catalog-page" class="page-content card card-block">
        <div class="card-block">
            
            {* Page description *}
            {if $page_description}
                <div class="page-description">
                    <p class="text-muted">{$page_description}</p>
                </div>
            {/if}

            {* Search form *}
            <div class="catalog-search mb-4">
                <form id="catalog-search-form" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" 
                               id="search-input" 
                               name="search" 
                               class="form-control" 
                               placeholder="{l s='Search catalogs...' mod='cig_catalog'}"
                               value="{$search_query|escape:'html':'UTF-8'}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> {l s='Search' mod='cig_catalog'}
                        </button>
                    </div>
                </form>
            </div>

            {* Results info *}
            <div class="catalog-results-info mb-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0 text-muted">
                            {if $search_query}
                                {l s='Found %d catalogs for "%s"' sprintf=[$total_catalogs, $search_query] mod='cig_catalog'}
                            {else}
                                {l s='Showing %d catalogs' sprintf=[$total_catalogs] mod='cig_catalog'}
                            {/if}
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        {if $search_query}
                            <a href="{$link->getModuleLink('cig_catalog', 'catalog')}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-times"></i> {l s='Clear search' mod='cig_catalog'}
                            </a>
                        {/if}
                    </div>
                </div>
            </div>

            {* Catalogs grid *}
            <div id="catalogs-container">
                {if $catalogs && count($catalogs) > 0}
                    <div class="row" id="catalogs-grid">
                        {foreach from=$catalogs item=catalog}
                            <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
                                {include file='module:cig_catalog/views/templates/front/catalog/_catalog-card.tpl' catalog=$catalog}
                            </div>
                        {/foreach}
                    </div>

                    {* Pagination *}
                    {if $total_pages > 1}
                        <nav aria-label="{l s='Catalog pagination' mod='cig_catalog'}" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {* Previous page *}
                                {if $current_page > 1}
                                    <li class="page-item">
                                        <a class="page-link" href="{$link->getModuleLink('cig_catalog', 'catalog', ['page' => $current_page-1, 'search' => $search_query])}" aria-label="{l s='Previous' mod='cig_catalog'}">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {/if}

                                {* Page numbers *}
                                {for $page=1 to $total_pages}
                                    {if $page == $current_page}
                                        <li class="page-item active">
                                            <span class="page-link">{$page}</span>
                                        </li>
                                    {elseif $page <= 3 || $page > $total_pages-3 || ($page >= $current_page-2 && $page <= $current_page+2)}
                                        <li class="page-item">
                                            <a class="page-link" href="{$link->getModuleLink('cig_catalog', 'catalog', ['page' => $page, 'search' => $search_query])}">{$page}</a>
                                        </li>
                                    {elseif $page == 4 && $current_page > 6}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {elseif $page == $total_pages-3 && $current_page < $total_pages-5}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {/if}
                                {/for}

                                {* Next page *}
                                {if $current_page < $total_pages}
                                    <li class="page-item">
                                        <a class="page-link" href="{$link->getModuleLink('cig_catalog', 'catalog', ['page' => $current_page+1, 'search' => $search_query])}" aria-label="{l s='Next' mod='cig_catalog'}">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                {/if}
                            </ul>
                        </nav>
                    {/if}

                {else}
                    {* No catalogs found *}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <h4>{l s='No catalogs found' mod='cig_catalog'}</h4>
                        {if $search_query}
                            <p>{l s='No catalogs match your search criteria. Try different keywords.' mod='cig_catalog'}</p>
                            <a href="{$link->getModuleLink('cig_catalog', 'catalog')}" class="btn btn-primary">
                                {l s='View all catalogs' mod='cig_catalog'}
                            </a>
                        {else}
                            <p>{l s='There are currently no catalogs available.' mod='cig_catalog'}</p>
                        {/if}
                    </div>
                {/if}
            </div>

            {* Loading indicator *}
            <div id="loading-indicator" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">{l s='Loading...' mod='cig_catalog'}</span>
                </div>
            </div>

        </div>
    </section>

    {* Order Modal *}
    {if $enable_ordering}
        <div class="modal fade" id="orderModal" tabindex="-1" aria-labelledby="orderModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="orderModalLabel">
                            <i class="fas fa-shopping-cart"></i> {l s='Order Catalog' mod='cig_catalog'}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{l s='Close' mod='cig_catalog'}"></button>
                    </div>
                    <div class="modal-body">
                        {include file='module:cig_catalog/views/templates/front/catalog/_order-form.tpl'}
                    </div>
                </div>
            </div>
        </div>
    {/if}

{/block}

{block name='page_footer'}
    {* JavaScript variables *}
    <script type="text/javascript">
        var catalogConfig = {
            ajaxUrl: '{$ajax_url}',
            moduleDir: '{$module_dir}',
            enableOrdering: {if $enable_ordering}true{else}false{/if},
            currentPage: {$current_page},
            totalPages: {$total_pages},
            searchQuery: '{$search_query|escape:'javascript':'UTF-8'}',
            translations: {
                loading: '{l s='Loading...' mod='cig_catalog' js=1}',
                error: '{l s='An error occurred' mod='cig_catalog' js=1}',
                downloadSuccess: '{l s='Download started' mod='cig_catalog' js=1}',
                orderSuccess: '{l s='Order submitted successfully' mod='cig_catalog' js=1}',
                confirmDownload: '{l s='Do you want to download this catalog?' mod='cig_catalog' js=1}',
                confirmOrder: '{l s='Do you want to order this catalog?' mod='cig_catalog' js=1}'
            }
        };
    </script>
{/block}

{block name='stylesheets'}
    {$parent_stylesheets}
    <link rel="stylesheet" href="{$module_dir}views/css/catalog-front.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{/block}

{block name='javascript_bottom'}
    {$parent_javascript_bottom}
    <script src="{$module_dir}views/js/catalog-front.js"></script>
{/block}
