{**
 * Order Error Template
 * 
 * Template for order error page
 *}

{extends file='page.tpl'}

{block name='page_title'}
    <h1 class="h1 text-danger">
        <i class="fas fa-exclamation-triangle"></i> {$page_title}
    </h1>
{/block}

{block name='page_content_container'}
    <section id="order-error-page" class="page-content card card-block">
        <div class="card-block text-center">
            
            {* Error Icon *}
            <div class="error-icon mb-4">
                <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
            </div>
            
            {* Error Message *}
            <div class="error-message mb-4">
                <h2 class="h3 text-danger mb-3">{l s='Order Submission Failed' mod='cig_catalog'}</h2>
                <p class="lead text-muted">
                    {l s='We encountered an error while processing your catalog order.' mod='cig_catalog'}
                </p>
                
                {if $error_message}
                    <div class="alert alert-danger mt-3">
                        <strong>{l s='Error Details:' mod='cig_catalog'}</strong> {$error_message|escape:'html':'UTF-8'}
                    </div>
                {/if}
            </div>
            
            {* Troubleshooting Steps *}
            <div class="troubleshooting mb-4">
                <h3 class="h4 mb-3">{l s='What can you do?' mod='cig_catalog'}</h3>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="step-item">
                            <div class="step-icon mb-2">
                                <i class="fas fa-redo text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <h5 class="step-title">{l s='Try Again' mod='cig_catalog'}</h5>
                            <p class="step-description text-muted">
                                {l s='Go back and try submitting your order again.' mod='cig_catalog'}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="step-item">
                            <div class="step-icon mb-2">
                                <i class="fas fa-check-double text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <h5 class="step-title">{l s='Check Information' mod='cig_catalog'}</h5>
                            <p class="step-description text-muted">
                                {l s='Verify that all required fields are filled correctly.' mod='cig_catalog'}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="step-item">
                            <div class="step-icon mb-2">
                                <i class="fas fa-headset text-success" style="font-size: 2rem;"></i>
                            </div>
                            <h5 class="step-title">{l s='Contact Support' mod='cig_catalog'}</h5>
                            <p class="step-description text-muted">
                                {l s='If the problem persists, contact our customer support.' mod='cig_catalog'}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            {* Common Issues *}
            <div class="common-issues mb-4">
                <h4 class="h5 mb-3">{l s='Common Issues and Solutions' mod='cig_catalog'}</h4>
                <div class="accordion" id="troubleshootingAccordion">
                    
                    {* Required Fields *}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                {l s='Missing Required Information' mod='cig_catalog'}
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body text-start">
                                <p>{l s='Make sure you have filled in all required fields:' mod='cig_catalog'}</p>
                                <ul>
                                    <li>{l s='Company name' mod='cig_catalog'}</li>
                                    <li>{l s='First and last name' mod='cig_catalog'}</li>
                                    <li>{l s='Valid email address' mod='cig_catalog'}</li>
                                    <li>{l s='Complete delivery address' mod='cig_catalog'}</li>
                                    <li>{l s='GDPR consent checkbox' mod='cig_catalog'}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    {* Email Format *}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                {l s='Invalid Email Format' mod='cig_catalog'}
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body text-start">
                                <p>{l s='Please ensure your email address is in the correct format:' mod='cig_catalog'}</p>
                                <ul>
                                    <li>{l s='Example: <EMAIL>' mod='cig_catalog'}</li>
                                    <li>{l s='Must contain @ symbol' mod='cig_catalog'}</li>
                                    <li>{l s='Must have a valid domain' mod='cig_catalog'}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    {* Company ID *}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                {l s='Invalid Company ID (IČO)' mod='cig_catalog'}
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body text-start">
                                <p>{l s='If you entered a Company ID (IČO), please check:' mod='cig_catalog'}</p>
                                <ul>
                                    <li>{l s='Must be exactly 8 digits' mod='cig_catalog'}</li>
                                    <li>{l s='Must be a valid Czech company ID' mod='cig_catalog'}</li>
                                    <li>{l s='You can leave this field empty if not applicable' mod='cig_catalog'}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    {* Rate Limiting *}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingFour">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour">
                                {l s='Too Many Requests' mod='cig_catalog'}
                            </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body text-start">
                                <p>{l s='If you see a "too many requests" error:' mod='cig_catalog'}</p>
                                <ul>
                                    <li>{l s='Wait a few minutes before trying again' mod='cig_catalog'}</li>
                                    <li>{l s='This is a security measure to prevent spam' mod='cig_catalog'}</li>
                                    <li>{l s='Contact support if you need immediate assistance' mod='cig_catalog'}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            {* Action Buttons *}
            <div class="action-buttons mb-4">
                <button onclick="history.back()" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-arrow-left"></i> {l s='Go Back and Try Again' mod='cig_catalog'}
                </button>
                <a href="{$link->getModuleLink('cig_catalog', 'catalog')}" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-book"></i> {l s='Browse Catalogs' mod='cig_catalog'}
                </a>
            </div>
            
            {* Contact Information *}
            <div class="contact-info mt-5 pt-4 border-top">
                <h4 class="h5 mb-3">{l s='Still Need Help?' mod='cig_catalog'}</h4>
                <p class="text-muted">
                    {l s='Our customer support team is here to help you with your catalog order:' mod='cig_catalog'}
                </p>
                <div class="contact-details">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="contact-method mb-3">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <strong>{l s='Email:' mod='cig_catalog'}</strong>
                                <a href="mailto:{Configuration::get('PS_SHOP_EMAIL')}" class="ms-2">
                                    {Configuration::get('PS_SHOP_EMAIL')}
                                </a>
                            </div>
                            <div class="contact-method mb-3">
                                <i class="fas fa-phone text-primary me-2"></i>
                                <strong>{l s='Phone:' mod='cig_catalog'}</strong>
                                <a href="tel:{Configuration::get('PS_SHOP_PHONE')}" class="ms-2">
                                    {Configuration::get('PS_SHOP_PHONE')}
                                </a>
                            </div>
                            <div class="contact-method">
                                <i class="fas fa-clock text-primary me-2"></i>
                                <strong>{l s='Business Hours:' mod='cig_catalog'}</strong>
                                <span class="ms-2">{l s='Mon-Fri 9:00-17:00' mod='cig_catalog'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </section>
{/block}

{block name='stylesheets'}
    {$parent_stylesheets}
    <style>
        .error-icon {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .step-item {
            text-align: center;
            padding: 1rem;
        }
        
        .step-icon {
            transition: transform 0.3s ease;
        }
        
        .step-item:hover .step-icon {
            transform: scale(1.1);
        }
        
        .contact-details a {
            color: inherit;
            text-decoration: none;
        }
        
        .contact-details a:hover {
            color: #0d6efd;
            text-decoration: underline;
        }
        
        .accordion-button:not(.collapsed) {
            background-color: #f8f9fa;
            color: #0d6efd;
        }
        
        .contact-method {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media (max-width: 768px) {
            .contact-method {
                justify-content: flex-start;
            }
        }
    </style>
{/block}
