# Admin Routes
admin_catalog_index:
  path: /admin/catalog
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::indexAction'
  requirements:
    _legacy_link: 'AdminCatalog'

admin_catalog_create:
  path: /admin/catalog/create
  methods: [GET, POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::createAction'
  requirements:
    _legacy_link: 'AdminCatalog:create'

admin_catalog_edit:
  path: /admin/catalog/{id}/edit
  methods: [GET, POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::editAction'
  requirements:
    id: '\d+'
    _legacy_link: 'AdminCatalog:edit'

admin_catalog_delete:
  path: /admin/catalog/{id}/delete
  methods: [POST, DELETE]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::deleteAction'
  requirements:
    id: '\d+'
    _legacy_link: 'AdminCatalog:delete'

admin_catalog_toggle_status:
  path: /admin/catalog/{id}/toggle-status
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::toggleStatusAction'
  requirements:
    id: '\d+'

admin_catalog_bulk_action:
  path: /admin/catalog/bulk
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::bulkAction'

# AJAX Admin Routes
admin_catalog_reorder:
  path: /admin/catalog/reorder
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::reorderAction'
  options:
    expose: true

admin_catalog_upload:
  path: /admin/catalog/upload
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::uploadAction'
  options:
    expose: true

admin_catalog_delete_file:
  path: /admin/catalog/{id}/delete-file/{type}
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminCatalogController::deleteFileAction'
  requirements:
    id: '\d+'
    type: 'image|file'
  options:
    expose: true

# Email Configuration Routes
admin_catalog_email_config:
  path: /admin/catalog/email-config
  methods: [GET, POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminEmailConfigController::indexAction'
  requirements:
    _legacy_link: 'AdminCatalogEmailConfig'

admin_catalog_email_test:
  path: /admin/catalog/email-test
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminEmailConfigController::testAction'
  options:
    expose: true

# Statistics Routes
admin_catalog_statistics:
  path: /admin/catalog/statistics
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminStatisticsController::indexAction'
  requirements:
    _legacy_link: 'AdminCatalogStatistics'

admin_catalog_statistics_data:
  path: /admin/catalog/statistics/data
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Admin\AdminStatisticsController::dataAction'
  options:
    expose: true

# Frontend Routes
catalog_index:
  path: /{_locale}/katalogy
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Front\CatalogController::indexAction'
  requirements:
    _locale: 'cs|en|sk'

catalog_detail:
  path: /{_locale}/katalogy/{slug}
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Front\CatalogController::detailAction'
  requirements:
    _locale: 'cs|en|sk'
    slug: '[a-z0-9\-]+'

catalog_download:
  path: /module/cig_catalog/download/{id}
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Front\CatalogController::downloadAction'
  requirements:
    id: '\d+'
  options:
    expose: true

# Order System Routes
catalog_order_create:
  path: /module/cig_catalog/order
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Front\OrderController::createAction'
  options:
    expose: true

catalog_order_success:
  path: /{_locale}/katalogy/objednavka/uspech
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Front\OrderController::successAction'
  requirements:
    _locale: 'cs|en|sk'

catalog_order_error:
  path: /{_locale}/katalogy/objednavka/chyba
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Front\OrderController::errorAction'
  requirements:
    _locale: 'cs|en|sk'

# API Routes
api_catalog_list:
  path: /api/catalog/list
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Api\CatalogApiController::listAction'
  options:
    expose: true

api_catalog_search:
  path: /api/catalog/search
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Api\CatalogApiController::searchAction'
  options:
    expose: true

api_catalog_order_validate:
  path: /api/catalog/order/validate
  methods: [POST]
  defaults:
    _controller: 'CigCatalog\Controller\Api\OrderApiController::validateAction'
  options:
    expose: true

# Utility Routes
catalog_sitemap:
  path: /module/cig_catalog/sitemap.xml
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Utility\SitemapController::indexAction'

catalog_robots:
  path: /module/cig_catalog/robots.txt
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Utility\RobotsController::indexAction'

# Health Check Routes
catalog_health_check:
  path: /module/cig_catalog/health
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Utility\HealthController::checkAction'

# File Serving Routes (for development)
catalog_serve_image:
  path: /module/cig_catalog/images/{filename}
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Utility\FileController::serveImageAction'
  requirements:
    filename: '[a-zA-Z0-9\-_\.]+\.(jpg|jpeg|png|gif|webp)'

catalog_serve_file:
  path: /module/cig_catalog/files/{filename}
  methods: [GET]
  defaults:
    _controller: 'CigCatalog\Controller\Utility\FileController::serveFileAction'
  requirements:
    filename: '[a-zA-Z0-9\-_\.]+\.(pdf|zip|doc|docx|xls|xlsx)'
