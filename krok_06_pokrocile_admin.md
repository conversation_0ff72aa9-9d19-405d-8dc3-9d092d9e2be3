# Krok 6: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> admin funkce - drag&drop a upload

## <PERSON><PERSON><PERSON> kroku
Implementace pokročil<PERSON>ch funkcí administrace: drag&drop řazen<PERSON>, p<PERSON><PERSON><PERSON>il<PERSON> upload management a konfigurace emailů.

## Co se bude realizovat

### 6.1 Drag & Drop řazení

#### JavaScript implementace
```javascript
// admin/js/catalog-sortable.js
$(document).ready(function() {
    $('#sortable-catalogs').sortable({
        handle: '.drag-handle',
        placeholder: 'sortable-placeholder',
        helper: 'clone',
        opacity: 0.8,
        cursor: 'move',
        tolerance: 'pointer',
        update: function(event, ui) {
            var positions = {};
            $('#sortable-catalogs tr').each(function(index) {
                var catalogId = $(this).data('id');
                positions[catalogId] = index + 1;
            });
            
            updatePositions(positions);
        }
    });
    
    function updatePositions(positions) {
        $.ajax({
            url: catalogAdminUrls.reorder,
            method: 'POST',
            data: {
                positions: positions,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                showLoadingOverlay();
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Pořadí bylo úspěšně aktualizováno', 'success');
                    updatePositionNumbers();
                } else {
                    showNotification('Chyba při aktualizaci pořadí', 'error');
                    location.reload(); // Fallback
                }
            },
            error: function() {
                showNotification('Chyba při komunikaci se serverem', 'error');
                location.reload();
            },
            complete: function() {
                hideLoadingOverlay();
            }
        });
    }
    
    function updatePositionNumbers() {
        $('#sortable-catalogs tr').each(function(index) {
            $(this).find('.drag-handle').text(index + 1);
        });
    }
});
```

#### AJAX endpoint pro řazení
```php
// AdminCatalogController.php
public function reorderAction(Request $request): JsonResponse
{
    if (!$request->isXmlHttpRequest()) {
        throw new BadRequestHttpException('Only AJAX requests allowed');
    }
    
    $positions = $request->request->get('positions', []);
    
    if (empty($positions)) {
        return new JsonResponse(['success' => false, 'message' => 'No positions provided']);
    }
    
    try {
        $success = $this->catalogManager->reorderCatalogs($positions);
        
        return new JsonResponse([
            'success' => $success,
            'message' => $success ? 'Pořadí aktualizováno' : 'Chyba při aktualizaci'
        ]);
    } catch (\Exception $e) {
        $this->logger->error('Reorder failed', ['error' => $e->getMessage()]);
        
        return new JsonResponse([
            'success' => false,
            'message' => 'Chyba při aktualizaci pořadí'
        ], 500);
    }
}
```

### 6.2 Pokročilý upload management

#### Drag & Drop upload
```javascript
// admin/js/file-upload.js
class FileUploadManager {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            maxFileSize: 50 * 1024 * 1024, // 50MB
            allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf', 'application/zip'],
            uploadUrl: '/admin/catalog/upload',
            ...options
        };
        
        this.init();
    }
    
    init() {
        this.setupDropZone();
        this.setupFileInput();
        this.setupProgressBar();
    }
    
    setupDropZone() {
        const dropZone = this.container.querySelector('.drop-zone');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => dropZone.classList.add('drag-over'), false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => dropZone.classList.remove('drag-over'), false);
        });
        
        dropZone.addEventListener('drop', this.handleDrop.bind(this), false);
    }
    
    handleDrop(e) {
        const files = e.dataTransfer.files;
        this.handleFiles(files);
    }
    
    handleFiles(files) {
        Array.from(files).forEach(file => {
            if (this.validateFile(file)) {
                this.uploadFile(file);
            }
        });
    }
    
    validateFile(file) {
        if (file.size > this.options.maxFileSize) {
            this.showError(`Soubor ${file.name} je příliš velký. Maximum je ${this.formatFileSize(this.options.maxFileSize)}.`);
            return false;
        }
        
        if (!this.options.allowedTypes.includes(file.type)) {
            this.showError(`Soubor ${file.name} má nepodporovaný formát.`);
            return false;
        }
        
        return true;
    }
    
    uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
        
        const xhr = new XMLHttpRequest();
        const progressBar = this.createProgressBar(file.name);
        
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                this.updateProgressBar(progressBar, percentComplete);
            }
        });
        
        xhr.addEventListener('load', () => {
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    this.handleUploadSuccess(response, progressBar);
                } else {
                    this.handleUploadError(response.message, progressBar);
                }
            } else {
                this.handleUploadError('Chyba při nahrávání souboru', progressBar);
            }
        });
        
        xhr.addEventListener('error', () => {
            this.handleUploadError('Chyba při komunikaci se serverem', progressBar);
        });
        
        xhr.open('POST', this.options.uploadUrl);
        xhr.send(formData);
    }
}
```

#### Upload endpoint
```php
// AdminCatalogController.php
public function uploadAction(Request $request): JsonResponse
{
    if (!$request->isXmlHttpRequest()) {
        throw new BadRequestHttpException('Only AJAX requests allowed');
    }
    
    $file = $request->files->get('file');
    
    if (!$file) {
        return new JsonResponse(['success' => false, 'message' => 'No file uploaded']);
    }
    
    try {
        $uploadedPath = $this->fileUploadService->uploadFile($file);
        
        // Pro obrázky vytvoříme náhledy
        if (strpos($file->getMimeType(), 'image/') === 0) {
            $thumbnails = $this->fileUploadService->createThumbnails($uploadedPath);
        }
        
        return new JsonResponse([
            'success' => true,
            'file_path' => $uploadedPath,
            'thumbnails' => $thumbnails ?? null,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType()
        ]);
    } catch (\Exception $e) {
        $this->logger->error('Upload failed', [
            'file' => $file->getClientOriginalName(),
            'error' => $e->getMessage()
        ]);
        
        return new JsonResponse([
            'success' => false,
            'message' => 'Chyba při nahrávání souboru: ' . $e->getMessage()
        ], 500);
    }
}
```

### 6.3 Konfigurace emailů

#### Email konfigurace formulář
```php
// Form/EmailConfigurationType.php
class EmailConfigurationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('email_recipients', TextType::class, [
                'label' => 'Email příjemci',
                'help' => 'Zadejte email adresy oddělené čárkami',
                'required' => true,
                'constraints' => [
                    new NotBlank(),
                    new Callback([
                        'callback' => [$this, 'validateEmails']
                    ])
                ]
            ])
            ->add('email_subject_order', TextType::class, [
                'label' => 'Předmět - nová objednávka',
                'data' => 'Nová objednávka katalogu',
                'required' => true
            ])
            ->add('email_subject_confirmation', TextType::class, [
                'label' => 'Předmět - potvrzení zákazníkovi',
                'data' => 'Potvrzení objednávky katalogu',
                'required' => true
            ])
            ->add('email_template_order', TextareaType::class, [
                'label' => 'Šablona - notifikace admina',
                'attr' => ['rows' => 10],
                'help' => 'Dostupné proměnné: {{catalog_title}}, {{company_name}}, {{first_name}}, {{last_name}}, {{email}}, {{phone}}, {{address}}, {{note}}, {{date_add}}',
                'required' => true
            ])
            ->add('email_template_confirmation', TextareaType::class, [
                'label' => 'Šablona - potvrzení zákazníkovi',
                'attr' => ['rows' => 10],
                'help' => 'Dostupné proměnné: {{catalog_title}}, {{company_name}}, {{first_name}}, {{last_name}}',
                'required' => true
            ])
            ->add('smtp_enabled', CheckboxType::class, [
                'label' => 'Použít SMTP',
                'required' => false
            ])
            ->add('smtp_host', TextType::class, [
                'label' => 'SMTP server',
                'required' => false
            ])
            ->add('smtp_port', IntegerType::class, [
                'label' => 'SMTP port',
                'data' => 587,
                'required' => false
            ])
            ->add('smtp_username', TextType::class, [
                'label' => 'SMTP uživatel',
                'required' => false
            ])
            ->add('smtp_password', PasswordType::class, [
                'label' => 'SMTP heslo',
                'required' => false,
                'always_empty' => false
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Uložit konfiguraci'
            ]);
    }
    
    public function validateEmails($value, ExecutionContextInterface $context)
    {
        $emails = array_map('trim', explode(',', $value));
        
        foreach ($emails as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $context->buildViolation('Email "{{ email }}" není platný')
                    ->setParameter('{{ email }}', $email)
                    ->addViolation();
            }
        }
    }
}
```

### 6.4 Bulk operace

#### JavaScript pro hromadné akce
```javascript
// admin/js/bulk-actions.js
class BulkActionsManager {
    constructor() {
        this.selectedItems = new Set();
        this.init();
    }
    
    init() {
        this.setupSelectAll();
        this.setupItemSelection();
        this.setupBulkActions();
    }
    
    setupSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all');
        selectAllCheckbox.addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                this.updateSelection(checkbox.value, e.target.checked);
            });
            this.updateBulkActionsVisibility();
        });
    }
    
    setupItemSelection() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.updateSelection(e.target.value, e.target.checked);
                this.updateBulkActionsVisibility();
            });
        });
    }
    
    updateSelection(itemId, selected) {
        if (selected) {
            this.selectedItems.add(itemId);
        } else {
            this.selectedItems.delete(itemId);
        }
    }
    
    updateBulkActionsVisibility() {
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');
        
        if (this.selectedItems.size > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = this.selectedItems.size;
        } else {
            bulkActions.style.display = 'none';
        }
    }
    
    executeBulkAction(action) {
        if (this.selectedItems.size === 0) {
            alert('Nejsou vybrány žádné položky');
            return;
        }
        
        const confirmMessage = this.getConfirmMessage(action);
        if (!confirm(confirmMessage)) {
            return;
        }
        
        const formData = new FormData();
        formData.append('action', action);
        formData.append('items', JSON.stringify(Array.from(this.selectedItems)));
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
        
        fetch('/admin/catalog/bulk', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Chyba: ' + data.message);
            }
        })
        .catch(error => {
            alert('Chyba při komunikaci se serverem');
        });
    }
}
```

## Technické detaily

### Performance optimalizace
- Chunked upload pro velké soubory
- Progress tracking
- Background processing
- Memory management

### Bezpečnost
- File type validace
- Size limity
- CSRF protection
- Path traversal prevence

### UX/UI
- Drag & drop indikátory
- Progress bary
- Loading states
- Error handling

## Výstupy kroku
1. Drag & drop řazení
2. Pokročilý upload systém
3. Email konfigurace
4. Bulk operace
5. AJAX endpointy

## Závislosti
- Krok 1-5: Předchozí kroky

## Následující krok
Krok 7: Frontend stránka katalogů
