# Krok 1: Základní struktura modulu a konfigurace

## C<PERSON>l kroku
Vytvoření základní struktury PrestaShop modulu s konfigurací pro dependency injection a routing.

## Co se bude realizovat

### 1.1 Struktura adresářů
```
modules/cig_catalog/
├── cig_catalog.php                 # Hlavní soubor modulu
├── config/
│   ├── services.yml               # Dependency injection
│   └── routes.yml                 # Routing definice
├── controllers/
│   ├── admin/                     # Admin controllery
│   └── front/                     # Frontend controllery
├── src/
│   ├── Entity/                    # Entity třídy
│   ├── Repository/                # Repository pattern
│   ├── Form/                      # Symfony formuláře
│   ├── Service/                   # Business logika
│   └── Validator/                 # Validační třídy
├── templates/
│   ├── admin/                     # Admin šablony
│   └── front/                     # Frontend šablony
├── translations/
│   ├── cs.php                     # České překlady
│   └── en.php                     # Anglické překlady
├── sql/
│   ├── install.sql                # Instalační SQL
│   └── uninstall.sql              # Deinstalační SQL
├── views/
│   ├── css/                       # Styly
│   ├── js/                        # JavaScript
│   └── img/                       # Obrázky
└── uploads/                       # Upload složka pro katalogy
    ├── images/                    # Náhledové obrázky
    └── files/                     # PDF/ZIP soubory
```

### 1.2 Hlavní soubor modulu (cig_catalog.php)
- Definice základních vlastností modulu
- Konstruktor s nastavením kompatibility PS 8.2.0
- Metody install() a uninstall()
- Registrace hooků
- Autoloader pro PSR-4

### 1.3 Konfigurace services.yml
- Definice služeb pro dependency injection
- Repository registrace
- Service layer konfigurace
- Form type registrace

### 1.4 Routing (routes.yml)
- Admin routes pro správu katalogů
- Frontend routes pro zobrazení katalogů
- AJAX endpointy
- Download endpointy

### 1.5 Základní překlady
- České překlady pro admin rozhraní
- Anglické překlady jako fallback
- Klíče pro všechny texty v modulu

## Technické detaily

### Kompatibilita
- PrestaShop 8.2.0+
- PHP 8.1+
- Symfony 6.x komponenty
- PSR-4 autoloading

### Bezpečnost
- CSRF token integrace
- Validace oprávnění
- Sanitizace vstupů

### Performance
- Lazy loading služeb
- Optimalizované autoloading
- Cache-friendly struktura

## Výstupy kroku
1. Kompletní adresářová struktura
2. Funkční hlavní soubor modulu
3. Konfigurace DI a routingu
4. Základní překlady
5. Instalovatelný modul (bez funkcionalit)

## Závislosti
- Žádné (první krok)

## Následující krok
Krok 2: Databázová struktura a entity
