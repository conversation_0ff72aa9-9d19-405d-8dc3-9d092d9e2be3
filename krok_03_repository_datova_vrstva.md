# Krok 3: Repository pattern a datová vrstva

## <PERSON><PERSON><PERSON> kroku
Implementace Repository pattern pro č<PERSON><PERSON> separaci datové vrstvy s podporou multijazyčnosti.

## Co se bude realizovat

### 3.1 CatalogRepository.php

#### Základní CRUD operace
- `findById(int $id, int $langId): ?array`
- `findAll(int $langId): array`
- `findAllActive(int $langId): array`
- `create(array $data): int`
- `update(int $id, array $data): bool`
- `delete(int $id): bool`

#### Pokročilé dotazy
- `findByPosition(int $langId): array` - řazení podle pozice
- `findNewCatalogs(int $langId): array` - pouze nové katalogy
- `findWithPagination(int $page, int $limit, int $langId): array`
- `search(string $query, int $langId): array`

#### Pozice management
- `updatePositions(array $positions): bool`
- `getMaxPosition(): int`
- `reorderAfterDelete(int $deletedPosition): bool`

### 3.2 CatalogOrderRepository.php

#### Objednávky management
- `create(array $orderData): int`
- `findByCatalogId(int $catalogId): array`
- `findByEmail(string $email): array`
- `findRecent(int $days = 30): array`
- `getStatistics(): array`

#### Export a reporting
- `exportToCSV(array $filters): string`
- `getOrdersByDateRange(DateTime $from, DateTime $to): array`

### 3.3 CatalogConfigRepository.php

#### Konfigurace management
- `get(string $name, mixed $default = null): mixed`
- `set(string $name, mixed $value): bool`
- `getAll(): array`
- `delete(string $name): bool`

#### Cache mechanismus
- `getWithCache(string $name): mixed`
- `clearCache(): void`
- `warmupCache(): void`

### 3.4 Abstraktní BaseRepository

#### Společná funkcionalita
- Database connection management
- Error handling
- Logging mechanismus
- Transaction support

```php
abstract class BaseRepository
{
    protected Connection $connection;
    protected LoggerInterface $logger;
    
    public function __construct(Connection $connection, LoggerInterface $logger)
    {
        $this->connection = $connection;
        $this->logger = $logger;
    }
    
    protected function executeQuery(string $sql, array $params = []): array
    {
        try {
            return $this->connection->fetchAllAssociative($sql, $params);
        } catch (\Exception $e) {
            $this->logger->error('Database query failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
```

### 3.5 Query Builder integrace

#### Složité dotazy
- Využití Doctrine DBAL Query Builder
- Type-safe parametry
- Optimalizované JOINy
- Subquery podpora

#### Příklad implementace
```php
public function findActiveWithFilters(array $filters, int $langId): array
{
    $qb = $this->connection->createQueryBuilder();
    
    $qb->select('c.*', 'cl.title', 'cl.description')
       ->from(_DB_PREFIX_ . 'cig_catalog', 'c')
       ->leftJoin('c', _DB_PREFIX_ . 'cig_catalog_lang', 'cl', 
                  'c.id_catalog = cl.id_catalog AND cl.id_lang = :lang_id')
       ->where('c.active = 1')
       ->setParameter('lang_id', $langId);
    
    if (isset($filters['is_new'])) {
        $qb->andWhere('c.is_new = :is_new')
           ->setParameter('is_new', $filters['is_new']);
    }
    
    if (isset($filters['search'])) {
        $qb->andWhere('cl.title LIKE :search OR cl.description LIKE :search')
           ->setParameter('search', '%' . $filters['search'] . '%');
    }
    
    $qb->orderBy('c.is_new', 'DESC')
       ->addOrderBy('c.position', 'ASC');
    
    return $qb->execute()->fetchAllAssociative();
}
```

## Technické detaily

### Performance optimalizace
- Prepared statements
- Index využití
- Lazy loading
- Connection pooling

### Error handling
- Structured logging
- Exception wrapping
- Graceful degradation
- Retry mechanismus

### Testing podpora
- Mock-friendly interface
- Test data fixtures
- Transaction rollback v testech

## Výstupy kroku
1. Kompletní repository třídy
2. Abstraktní base repository
3. Query builder integrace
4. Error handling systém
5. Unit testy pro repository

## Závislosti
- Krok 1: Základní struktura
- Krok 2: Databázová struktura a entity

## Následující krok
Krok 4: Service vrstva a business logika
