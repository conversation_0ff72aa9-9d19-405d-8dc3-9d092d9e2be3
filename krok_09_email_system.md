# Krok 9: <PERSON><PERSON> sys<PERSON> a notifikace

## <PERSON><PERSON><PERSON> email systému s šablonami pro admin notifikace a potvrzení <PERSON>ů<PERSON>, konfigurovatelné příjemce a testování odesílání.

## Co se bude realizovat

### 9.1 Email <PERSON>ab<PERSON>

#### admin-order-notification.html.twig
```twig
{# templates/email/admin-order-notification.html.twig #}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nová objednávka katalogu</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f8f9fa; }
        .order-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .detail-row { display: flex; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-label { font-weight: bold; width: 150px; }
        .detail-value { flex: 1; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .btn { display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Nová objednávka katalogu</h1>
        </div>
        
        <div class="content">
            <p>Byla přijata nová objednávka katalogu prostřednictvím webových stránek.</p>
            
            <div class="order-details">
                <h3>📁 Objednaný katalog</h3>
                <div class="detail-row">
                    <div class="detail-label">Název:</div>
                    <div class="detail-value"><strong>{{ catalog.title }}</strong></div>
                </div>
                {% if catalog.description %}
                <div class="detail-row">
                    <div class="detail-label">Popis:</div>
                    <div class="detail-value">{{ catalog.description }}</div>
                </div>
                {% endif %}
            </div>
            
            <div class="order-details">
                <h3>🏢 Údaje o firmě</h3>
                <div class="detail-row">
                    <div class="detail-label">Název firmy:</div>
                    <div class="detail-value">{{ order.company_name }}</div>
                </div>
                {% if order.company_id %}
                <div class="detail-row">
                    <div class="detail-label">IČO:</div>
                    <div class="detail-value">{{ order.company_id }}</div>
                </div>
                {% endif %}
            </div>
            
            <div class="order-details">
                <h3>👤 Kontaktní osoba</h3>
                <div class="detail-row">
                    <div class="detail-label">Jméno:</div>
                    <div class="detail-value">{{ order.first_name }} {{ order.last_name }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Email:</div>
                    <div class="detail-value"><a href="mailto:{{ order.email }}">{{ order.email }}</a></div>
                </div>
                {% if order.phone %}
                <div class="detail-row">
                    <div class="detail-label">Telefon:</div>
                    <div class="detail-value"><a href="tel:{{ order.phone }}">{{ order.phone }}</a></div>
                </div>
                {% endif %}
            </div>
            
            <div class="order-details">
                <h3>📍 Adresa pro doručení</h3>
                <div class="detail-row">
                    <div class="detail-label">Adresa:</div>
                    <div class="detail-value">{{ order.address|nl2br }}</div>
                </div>
            </div>
            
            {% if order.note %}
            <div class="order-details">
                <h3>📝 Poznámka</h3>
                <div class="detail-row">
                    <div class="detail-label">Poznámka:</div>
                    <div class="detail-value">{{ order.note|nl2br }}</div>
                </div>
            </div>
            {% endif %}
            
            <div class="order-details">
                <h3>ℹ️ Technické informace</h3>
                <div class="detail-row">
                    <div class="detail-label">Datum objednávky:</div>
                    <div class="detail-value">{{ order.date_add|date('d.m.Y H:i:s') }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">IP adresa:</div>
                    <div class="detail-value">{{ order.ip_address }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">ID objednávky:</div>
                    <div class="detail-value">#{{ order.id_order }}</div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ admin_url }}" class="btn">Zobrazit v administraci</a>
            </div>
        </div>
        
        <div class="footer">
            <p>Tento email byl automaticky vygenerován systémem {{ shop_name }}.</p>
            <p>Pro odpověď použijte email: {{ order.email }}</p>
        </div>
    </div>
</body>
</html>
```

#### customer-order-confirmation.html.twig
```twig
{# templates/email/customer-order-confirmation.html.twig #}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potvrzení objednávky katalogu</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f8f9fa; }
        .order-summary { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .detail-row { display: flex; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-label { font-weight: bold; width: 150px; }
        .detail-value { flex: 1; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .success-icon { font-size: 48px; text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Potvrzení objednávky</h1>
        </div>
        
        <div class="content">
            <div class="success-icon">🎉</div>
            
            <p>Vážený zákazníku,</p>
            
            <p>děkujeme za Váš zájem o náš katalog <strong>{{ catalog.title }}</strong>.</p>
            
            <p>Vaše objednávka byla úspěšně přijata a bude zpracována v nejbližší době. Katalog vám zašleme poštou na uvedenou adresu <strong>zdarma</strong>.</p>
            
            <div class="order-summary">
                <h3>📋 Shrnutí objednávky</h3>
                <div class="detail-row">
                    <div class="detail-label">Katalog:</div>
                    <div class="detail-value">{{ catalog.title }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Firma:</div>
                    <div class="detail-value">{{ order.company_name }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Kontakt:</div>
                    <div class="detail-value">{{ order.first_name }} {{ order.last_name }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Email:</div>
                    <div class="detail-value">{{ order.email }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Adresa:</div>
                    <div class="detail-value">{{ order.address|nl2br }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Datum:</div>
                    <div class="detail-value">{{ order.date_add|date('d.m.Y H:i') }}</div>
                </div>
            </div>
            
            <p><strong>Co bude následovat:</strong></p>
            <ul>
                <li>Vaše objednávka bude zpracována do 2 pracovních dnů</li>
                <li>Katalog vám zašleme poštou na uvedenou adresu</li>
                <li>Doručení obvykle trvá 3-5 pracovních dnů</li>
                <li>V případě dotazů nás kontaktujte na {{ contact_email }}</li>
            </ul>
            
            <p>Mezitím si můžete prohlédnout naše další katalogy na našich webových stránkách.</p>
            
            <p>Děkujeme za Vaši důvěru!</p>
            
            <p>S pozdravem,<br>
            <strong>{{ shop_name }}</strong></p>
        </div>
        
        <div class="footer">
            <p>Tento email byl automaticky vygenerován.</p>
            <p>{{ shop_name }} | {{ shop_address }} | {{ shop_phone }}</p>
            <p><a href="{{ shop_url }}">{{ shop_url }}</a></p>
        </div>
    </div>
</body>
</html>
```

### 9.2 EmailService rozšíření

#### EmailService.php (kompletní implementace)
```php
<?php

namespace CigCatalog\Service;

use Swift_Mailer;
use Swift_Message;
use Swift_SmtpTransport;
use Twig\Environment;
use Psr\Log\LoggerInterface;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Repository\CatalogConfigRepository;

class EmailService
{
    private Environment $twig;
    private LoggerInterface $logger;
    private CatalogRepository $catalogRepository;
    private CatalogConfigRepository $configRepository;
    private Swift_Mailer $mailer;
    
    public function __construct(
        Environment $twig,
        LoggerInterface $logger,
        CatalogRepository $catalogRepository,
        CatalogConfigRepository $configRepository
    ) {
        $this->twig = $twig;
        $this->logger = $logger;
        $this->catalogRepository = $catalogRepository;
        $this->configRepository = $configRepository;
        $this->initializeMailer();
    }
    
    private function initializeMailer(): void
    {
        $smtpEnabled = $this->configRepository->get('smtp_enabled', false);
        
        if ($smtpEnabled) {
            $transport = (new Swift_SmtpTransport(
                $this->configRepository->get('smtp_host'),
                $this->configRepository->get('smtp_port', 587),
                $this->configRepository->get('smtp_encryption', 'tls')
            ))
            ->setUsername($this->configRepository->get('smtp_username'))
            ->setPassword($this->configRepository->get('smtp_password'));
        } else {
            $transport = new \Swift_SendmailTransport('/usr/sbin/sendmail -bs');
        }
        
        $this->mailer = new Swift_Mailer($transport);
    }
    
    public function sendOrderNotification(array $orderData): bool
    {
        try {
            $catalog = $this->catalogRepository->findById($orderData['id_catalog'], 1);
            
            if (!$catalog) {
                throw new \Exception('Katalog nebyl nalezen');
            }
            
            $recipients = $this->getEmailRecipients();
            $subject = $this->configRepository->get('email_subject_order', 'Nová objednávka katalogu');
            
            $htmlBody = $this->twig->render('@CigCatalog/email/admin-order-notification.html.twig', [
                'order' => $orderData,
                'catalog' => $catalog,
                'admin_url' => $this->getAdminUrl(),
                'shop_name' => $this->getShopName()
            ]);
            
            $message = (new Swift_Message($subject))
                ->setFrom([$this->getFromEmail() => $this->getShopName()])
                ->setTo($recipients)
                ->setBody($htmlBody, 'text/html')
                ->setReplyTo($orderData['email'], $orderData['first_name'] . ' ' . $orderData['last_name']);
            
            $result = $this->mailer->send($message);
            
            $this->logger->info('Admin notification sent', [
                'order_id' => $orderData['id_order'] ?? null,
                'recipients' => $recipients,
                'result' => $result
            ]);
            
            return $result > 0;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to send admin notification', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            return false;
        }
    }
    
    public function sendOrderConfirmation(array $orderData): bool
    {
        try {
            $catalog = $this->catalogRepository->findById($orderData['id_catalog'], 1);
            
            if (!$catalog) {
                throw new \Exception('Katalog nebyl nalezen');
            }
            
            $subject = $this->configRepository->get('email_subject_confirmation', 'Potvrzení objednávky katalogu');
            
            $htmlBody = $this->twig->render('@CigCatalog/email/customer-order-confirmation.html.twig', [
                'order' => $orderData,
                'catalog' => $catalog,
                'shop_name' => $this->getShopName(),
                'shop_url' => $this->getShopUrl(),
                'shop_address' => $this->getShopAddress(),
                'shop_phone' => $this->getShopPhone(),
                'contact_email' => $this->getContactEmail()
            ]);
            
            $message = (new Swift_Message($subject))
                ->setFrom([$this->getFromEmail() => $this->getShopName()])
                ->setTo([$orderData['email'] => $orderData['first_name'] . ' ' . $orderData['last_name']])
                ->setBody($htmlBody, 'text/html');
            
            $result = $this->mailer->send($message);
            
            $this->logger->info('Customer confirmation sent', [
                'order_id' => $orderData['id_order'] ?? null,
                'email' => $orderData['email'],
                'result' => $result
            ]);
            
            return $result > 0;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to send customer confirmation', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            return false;
        }
    }
    
    public function testEmailConfiguration(): array
    {
        $results = [];
        
        // Test SMTP připojení
        try {
            $transport = $this->mailer->getTransport();
            if ($transport instanceof Swift_SmtpTransport) {
                $transport->start();
                $results['smtp_connection'] = ['success' => true, 'message' => 'SMTP připojení úspěšné'];
                $transport->stop();
            } else {
                $results['smtp_connection'] = ['success' => true, 'message' => 'Používá se sendmail'];
            }
        } catch (\Exception $e) {
            $results['smtp_connection'] = ['success' => false, 'message' => $e->getMessage()];
        }
        
        // Test odeslání testovacího emailu
        try {
            $testEmail = $this->getContactEmail();
            $message = (new Swift_Message('Test email - Katalog modul'))
                ->setFrom([$this->getFromEmail() => $this->getShopName()])
                ->setTo([$testEmail])
                ->setBody('Toto je testovací email z katalog modulu. Pokud tento email vidíte, konfigurace je správná.');
            
            $result = $this->mailer->send($message);
            $results['test_email'] = [
                'success' => $result > 0,
                'message' => $result > 0 ? 'Testovací email odeslán' : 'Chyba při odesílání'
            ];
        } catch (\Exception $e) {
            $results['test_email'] = ['success' => false, 'message' => $e->getMessage()];
        }
        
        return $results;
    }
    
    private function getEmailRecipients(): array
    {
        $recipients = $this->configRepository->get('email_recipients', '');
        $emails = array_map('trim', explode(',', $recipients));
        
        return array_filter($emails, function($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });
    }
    
    private function getFromEmail(): string
    {
        return $this->configRepository->get('from_email', 'noreply@' . $_SERVER['HTTP_HOST']);
    }
    
    private function getShopName(): string
    {
        return \Configuration::get('PS_SHOP_NAME');
    }
    
    private function getShopUrl(): string
    {
        return \Tools::getShopDomainSsl(true);
    }
    
    private function getShopAddress(): string
    {
        return \Configuration::get('PS_SHOP_ADDR1') . ', ' . \Configuration::get('PS_SHOP_CITY');
    }
    
    private function getShopPhone(): string
    {
        return \Configuration::get('PS_SHOP_PHONE');
    }
    
    private function getContactEmail(): string
    {
        return \Configuration::get('PS_SHOP_EMAIL');
    }
    
    private function getAdminUrl(): string
    {
        return \Tools::getShopDomainSsl(true) . '/' . \Configuration::get('PS_ADMIN_DIR');
    }
}
```

### 9.3 Email konfigurace v admin rozhraní

#### EmailConfigurationController.php
```php
<?php

namespace CigCatalog\Controller\Admin;

use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use CigCatalog\Form\EmailConfigurationType;
use CigCatalog\Service\EmailService;

class EmailConfigurationController extends FrameworkBundleAdminController
{
    private EmailService $emailService;
    
    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }
    
    public function indexAction(Request $request): Response
    {
        $form = $this->createForm(EmailConfigurationType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            
            // Uložení konfigurace
            foreach ($data as $key => $value) {
                $this->configRepository->set($key, $value);
            }
            
            $this->addFlash('success', 'Email konfigurace byla uložena');
            
            return $this->redirectToRoute('admin_catalog_email_config');
        }
        
        return $this->render('@CigCatalog/admin/email-configuration.html.twig', [
            'form' => $form->createView()
        ]);
    }
    
    public function testAction(): Response
    {
        $results = $this->emailService->testEmailConfiguration();
        
        return $this->json([
            'success' => true,
            'results' => $results
        ]);
    }
}
```

## Technické detaily

### Email delivery
- SMTP konfigurace
- Fallback na sendmail
- Queue systém pro velké objemy
- Retry mechanismus

### Template systém
- Twig šablony
- Responsive design
- Customizovatelné texty
- Multi-language podpora

### Monitoring
- Delivery tracking
- Error logging
- Bounce handling
- Statistics

### Performance
- Async odesílání
- Batch processing
- Connection pooling
- Memory optimalizace

## Výstupy kroku
1. Kompletní email šablony
2. Rozšířený EmailService
3. Admin konfigurace emailů
4. Test funkcionalita
5. Monitoring a logging

## Závislosti
- Krok 1-8: Předchozí kroky

## Následující krok
Krok 10: Finalizace, testování a dokumentace
