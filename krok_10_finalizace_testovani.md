# Krok 10: Finaliza<PERSON>, testování a dokumentace

## <PERSON><PERSON><PERSON> kroku
<PERSON>tn<PERSON> testování modulu, optimalizace výkonu, bezpečnostní kontroly a vytvoření dokumentace.

## Co se bude realizovat

### 10.1 Kompletní testování

#### Unit testy
```php
<?php
// tests/Unit/Service/CatalogManagerTest.php

namespace CigCatalog\Tests\Unit\Service;

use PHPUnit\Framework\TestCase;
use CigCatalog\Service\CatalogManager;
use CigCatalog\Repository\CatalogRepository;

class CatalogManagerTest extends TestCase
{
    private CatalogManager $catalogManager;
    private CatalogRepository $catalogRepository;
    
    protected function setUp(): void
    {
        $this->catalogRepository = $this->createMock(CatalogRepository::class);
        $this->catalogManager = new CatalogManager($this->catalogRepository);
    }
    
    public function testCreateCatalog(): void
    {
        $data = [
            'title' => 'Test katalog',
            'description' => 'Test popis',
            'active' => true
        ];
        
        $this->catalogRepository
            ->expects($this->once())
            ->method('create')
            ->with($data)
            ->willReturn(1);
        
        $result = $this->catalogManager->createCatalog($data, []);
        
        $this->assertEquals(1, $result);
    }
    
    public function testValidateRequiredFields(): void
    {
        $data = ['description' => 'Pouze popis']; // Chybí title
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Název je povinný');
        
        $this->catalogManager->createCatalog($data, []);
    }
}
```

#### Integration testy
```php
<?php
// tests/Integration/Controller/CatalogControllerTest.php

namespace CigCatalog\Tests\Integration\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class CatalogControllerTest extends WebTestCase
{
    public function testCatalogPageLoads(): void
    {
        $client = static::createClient();
        $crawler = $client->request('GET', '/cs/katalogy');
        
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $this->assertSelectorTextContains('h1', 'Naše katalogy');
    }
    
    public function testOrderFormSubmission(): void
    {
        $client = static::createClient();
        
        $orderData = [
            'catalog_id' => 1,
            'company_name' => 'Test s.r.o.',
            'first_name' => 'Jan',
            'last_name' => 'Novák',
            'email' => '<EMAIL>',
            'address' => 'Testovací 123, Praha',
            'gdpr_consent' => true,
            '_token' => $this->generateCsrfToken('catalog_order')
        ];
        
        $client->request('POST', '/module/cig_catalog/order', $orderData);
        
        $response = json_decode($client->getResponse()->getContent(), true);
        
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $this->assertTrue($response['success']);
    }
}
```

#### Frontend testy (JavaScript)
```javascript
// tests/frontend/catalog-order.test.js

describe('Catalog Order Modal', () => {
    let modal;
    
    beforeEach(() => {
        document.body.innerHTML = `
            <div id="orderModal" class="modal">
                <form id="catalog-order-form">
                    <input type="text" id="company_name" name="company_name" required>
                    <input type="email" id="email" name="email" required>
                    <input type="text" id="company_id" name="company_id">
                </form>
            </div>
        `;
        
        modal = new CatalogOrderModal();
    });
    
    test('validates required fields', () => {
        const companyNameField = document.getElementById('company_name');
        companyNameField.value = '';
        
        const isValid = modal.validateField(companyNameField);
        
        expect(isValid).toBe(false);
        expect(companyNameField.classList.contains('is-invalid')).toBe(true);
    });
    
    test('validates email format', () => {
        const emailField = document.getElementById('email');
        emailField.value = 'invalid-email';
        
        const isValid = modal.validateField(emailField);
        
        expect(isValid).toBe(false);
    });
    
    test('validates IČO format', () => {
        const companyIdField = document.getElementById('company_id');
        companyIdField.value = '12345'; // Neplatné IČO
        
        const isValid = modal.validateField(companyIdField);
        
        expect(isValid).toBe(false);
    });
});
```

### 10.2 Performance optimalizace

#### Database optimalizace
```sql
-- Přidání indexů pro lepší výkon
ALTER TABLE `cig_catalog` ADD INDEX `idx_active_position` (`active`, `position`);
ALTER TABLE `cig_catalog` ADD INDEX `idx_new_active` (`is_new`, `active`);
ALTER TABLE `cig_catalog_order` ADD INDEX `idx_catalog_date` (`id_catalog`, `date_add`);
ALTER TABLE `cig_catalog_order` ADD INDEX `idx_email_date` (`email`, `date_add`);

-- Optimalizace pro fulltextové vyhledávání
ALTER TABLE `cig_catalog_lang` ADD FULLTEXT(`title`, `description`);
```

#### Cache optimalizace
```php
<?php
// src/Service/CacheOptimizer.php

namespace CigCatalog\Service;

class CacheOptimizer
{
    private const CACHE_KEYS = [
        'catalog_list' => 3600,        // 1 hodina
        'catalog_config' => 86400,     // 24 hodin
        'catalog_stats' => 1800,       // 30 minut
    ];
    
    public function warmupCache(): void
    {
        // Přednahřátí cache pro často používané dotazy
        $this->catalogRepository->findAllActive(1); // Čeština
        $this->catalogRepository->findAllActive(2); // Angličtina
        
        // Cache konfigurace
        $this->configRepository->getAll();
        
        // Cache statistik
        $this->getStatistics();
    }
    
    public function clearExpiredCache(): void
    {
        foreach (self::CACHE_KEYS as $key => $ttl) {
            $cacheKey = 'cig_catalog_' . $key;
            if ($this->cache->isExpired($cacheKey)) {
                $this->cache->delete($cacheKey);
            }
        }
    }
}
```

### 10.3 Bezpečnostní kontroly

#### Security audit checklist
```php
<?php
// src/Security/SecurityAuditor.php

namespace CigCatalog\Security;

class SecurityAuditor
{
    public function auditModule(): array
    {
        $issues = [];
        
        // Kontrola file permissions
        $uploadDir = _PS_MODULE_DIR_ . 'cig_catalog/uploads/';
        if (is_writable($uploadDir) && !file_exists($uploadDir . '.htaccess')) {
            $issues[] = 'Upload directory missing .htaccess protection';
        }
        
        // Kontrola SQL injection prevence
        $this->auditSqlQueries();
        
        // Kontrola XSS prevence
        $this->auditTemplates();
        
        // Kontrola CSRF protection
        $this->auditForms();
        
        // Kontrola file upload security
        $this->auditFileUploads();
        
        return $issues;
    }
    
    private function auditSqlQueries(): void
    {
        // Kontrola, že všechny dotazy používají prepared statements
        // Scan všech PHP souborů pro potenciální SQL injection
    }
    
    private function auditTemplates(): void
    {
        // Kontrola, že všechny výstupy jsou escapované
        // Scan Twig šablon pro |raw filtry bez validace
    }
}
```

#### .htaccess pro upload složku
```apache
# uploads/.htaccess
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|zip)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Zakázání spouštění PHP
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>
```

### 10.4 Dokumentace

#### README.md
```markdown
# CIG Catalog Module pro PrestaShop 8.2.0

Pokročilý modul pro správu a prezentaci katalogů s možností objednávání.

## Funkce

- ✅ Správa katalogů v admin rozhraní
- ✅ Drag & drop řazení
- ✅ Upload obrázků a souborů
- ✅ Responzivní frontend
- ✅ Modal formulář pro objednávky
- ✅ Email notifikace
- ✅ Multijazyčnost
- ✅ SEO optimalizace

## Požadavky

- PrestaShop 8.2.0+
- PHP 8.1+
- MySQL 5.7+ / MariaDB 10.3+
- GD extension pro zpracování obrázků
- cURL extension pro HTTP požadavky

## Instalace

1. Nahrajte modul do složky `/modules/cig_catalog/`
2. V admin rozhraní přejděte na Moduly > Správce modulů
3. Najděte "CIG Catalog" a klikněte na "Instalovat"
4. Nakonfigurujte email nastavení v Moduly > CIG Catalog > Konfigurace

## Konfigurace

### Email nastavení
- Příjemci notifikací
- SMTP konfigurace
- Email šablony

### Stránka katalogů
- Název a popis stránky
- Počet katalogů na stránku
- SEO nastavení

## Použití

### Přidání katalogu
1. Přejděte na Moduly > CIG Catalog > Katalogy
2. Klikněte na "Přidat katalog"
3. Vyplňte název, popis a nahrajte soubory
4. Uložte

### Správa objednávek
Objednávky jsou automaticky ukládány a odesílány emailem.
Přehled objednávek najdete v Moduly > CIG Catalog > Objednávky.

## Troubleshooting

### Nefunguje upload souborů
- Zkontrolujte oprávnění složky `uploads/`
- Ověřte PHP limity (upload_max_filesize, post_max_size)

### Neodesílají se emaily
- Zkontrolujte SMTP nastavení
- Použijte test email funkci
- Zkontrolujte logy v `/var/logs/`

## Podpora

Pro technickou podporu kontaktujte: <EMAIL>
```

#### CHANGELOG.md
```markdown
# Changelog

## [1.0.0] - 2024-01-XX

### Přidáno
- Základní správa katalogů
- Frontend stránka katalogů
- Objednávkový systém
- Email notifikace
- Drag & drop řazení
- Upload management
- Multijazyčnost
- SEO optimalizace

### Bezpečnost
- CSRF protection
- XSS prevence
- File upload security
- Rate limiting
```

### 10.5 Deployment checklist

#### Pre-deployment kontroly
```bash
#!/bin/bash
# scripts/pre-deployment-check.sh

echo "🔍 Spouštím pre-deployment kontroly..."

# Kontrola syntaxe PHP
find . -name "*.php" -exec php -l {} \; | grep -v "No syntax errors"

# Spuštění testů
./vendor/bin/phpunit tests/

# Kontrola bezpečnosti
./vendor/bin/security-checker security:check

# Kontrola kódování
./vendor/bin/phpcs --standard=PSR12 src/

# Kontrola závislostí
composer validate

echo "✅ Pre-deployment kontroly dokončeny"
```

#### Post-deployment ověření
```php
<?php
// scripts/post-deployment-verify.php

// Ověření, že modul je správně nainstalován
$module = Module::getInstanceByName('cig_catalog');
if (!$module || !$module->isInstalled()) {
    die('❌ Modul není nainstalován');
}

// Ověření databázových tabulek
$tables = ['cig_catalog', 'cig_catalog_lang', 'cig_catalog_order', 'cig_catalog_config'];
foreach ($tables as $table) {
    $exists = Db::getInstance()->executeS("SHOW TABLES LIKE '" . _DB_PREFIX_ . $table . "'");
    if (empty($exists)) {
        die("❌ Tabulka {$table} neexistuje");
    }
}

// Ověření upload složek
$uploadDirs = [
    _PS_MODULE_DIR_ . 'cig_catalog/uploads/images/',
    _PS_MODULE_DIR_ . 'cig_catalog/uploads/files/'
];

foreach ($uploadDirs as $dir) {
    if (!is_dir($dir) || !is_writable($dir)) {
        die("❌ Upload složka {$dir} není dostupná");
    }
}

echo "✅ Post-deployment ověření úspěšné\n";
```

## Technické detaily

### Monitoring
- Error tracking
- Performance metriky
- Usage statistics
- Health checks

### Maintenance
- Automatické čištění cache
- Log rotation
- Database optimalizace
- Backup strategie

### Scaling
- CDN integrace
- Database sharding připravenost
- Microservices architektura
- API endpoints

## Výstupy kroku
1. Kompletní test suite
2. Performance optimalizace
3. Bezpečnostní audit
4. Dokumentace
5. Deployment skripty
6. Monitoring setup

## Závislosti
- Krok 1-9: Všechny předchozí kroky

## Výsledek
Plně funkční, testovaný a dokumentovaný modul připravený k produkčnímu nasazení.
