# Krok 2: Databázová struktura a entity

## <PERSON><PERSON><PERSON> kroku
Vytvoření databázov<PERSON>ch tabulek a implementace entity tříd s multijazyčnou podporou.

## Co se bude realizovat

### 2.1 SQL skripty pro instalaci

#### Tabulka cig_catalog (hlavn<PERSON> katalogy)
```sql
CREATE TABLE `cig_catalog` (
    `id_catalog` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `description` text,
    `image_path` varchar(500),
    `catalog_url` varchar(500),
    `catalog_file` varchar(500),
    `is_new` tinyint(1) DEFAULT 0,
    `position` int(11) DEFAULT 0,
    `active` tinyint(1) DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_catalog`),
    KEY `position` (`position`),
    KEY `active` (`active`),
    KEY `is_new` (`is_new`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Tabulka cig_catalog_lang (multijazyčnost)
```sql
CREATE TABLE `cig_catalog_lang` (
    `id_catalog` int(11) NOT NULL,
    `id_lang` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `description` text,
    PRIMARY KEY (`id_catalog`, `id_lang`),
    KEY `id_lang` (`id_lang`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Tabulka cig_catalog_order (objednávky)
```sql
CREATE TABLE `cig_catalog_order` (
    `id_order` int(11) NOT NULL AUTO_INCREMENT,
    `id_catalog` int(11) NOT NULL,
    `company_name` varchar(255),
    `company_id` varchar(50),
    `first_name` varchar(100) NOT NULL,
    `last_name` varchar(100) NOT NULL,
    `email` varchar(255) NOT NULL,
    `phone` varchar(50),
    `address` text NOT NULL,
    `note` text,
    `date_add` datetime NOT NULL,
    PRIMARY KEY (`id_order`),
    KEY `id_catalog` (`id_catalog`),
    KEY `email` (`email`),
    KEY `date_add` (`date_add`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Tabulka cig_catalog_config (konfigurace)
```sql
CREATE TABLE `cig_catalog_config` (
    `id_config` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `value` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_config`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2.2 Entity třídy

#### Catalog.php (hlavní entita)
- Rozšíření ObjectModel pro PS kompatibilitu
- Definice všech polí s validací
- Multijazyčná podpora
- Metody pro práci s pozicemi
- Gettery a settery
- Validační pravidla

#### CatalogOrder.php (objednávky)
- Entita pro ukládání objednávek
- Validace emailu, IČO
- Formátování dat
- Metody pro export

#### CatalogConfig.php (konfigurace)
- Správa konfiguračních hodnot
- Statické metody pro rychlý přístup
- Cache mechanismus

### 2.3 Instalační a deinstalační skripty

#### install.sql
- Vytvoření všech tabulek
- Základní indexy pro výkon
- Výchozí konfigurační hodnoty
- Ukázkový katalog

#### uninstall.sql
- Bezpečné odstranění tabulek
- Kontrola závislostí
- Backup možnosti

### 2.4 Migrace a aktualizace
- Verze kontrola
- Postupné migrace
- Rollback mechanismus
- Data integrity kontroly

## Technické detaily

### Databázové optimalizace
- Správné indexy pro rychlé dotazy
- UTF8MB4 pro emoji podporu
- InnoDB engine pro transakce
- Optimalizované datové typy

### Validace
- PrestaShop validátory
- Custom validace pro IČO
- Email format kontrola
- File path validace

### Multijazyčnost
- Automatická detekce jazyků
- Fallback na výchozí jazyk
- Lazy loading překladů

## Výstupy kroku
1. ✅ Kompletní SQL skripty (install, uninstall, upgrade, backup, restore, integrity_check)
2. ✅ Funkční entity třídy (Catalog, CatalogOrder, CatalogConfig)
3. ✅ Instalační mechanismus s foreign key constraints
4. ✅ Migrace systém s verze kontrolou
5. ✅ Testovací data a ukázkový katalog
6. ✅ Backup a restore mechanismus
7. ✅ Data integrity kontroly
8. ✅ Kompletní dokumentace SQL skriptů
9. ✅ Pokročilý migrační systém s PHP třídami
10. ✅ Automatické migrace mezi verzemi (1.0.0 → 1.3.0)
11. ✅ Rollback mechanismus a error handling
12. ✅ Kompletní test suite pro migrace

## Realizace

### 7. Entity třídy - DOKONČENO ✅

Implementovány všechny entity třídy:

#### Catalog.php - Hlavní entita katalogu
- Rozšíření ObjectModel pro PS kompatibilitu
- Multijazyčná podpora (title, description)
- Validace všech polí (title, image_path, catalog_url, atd.)
- Metody pro práci s pozicemi (updatePosition, cleanPositions)
- Statické metody pro získávání dat (getActiveCatalogs, getNewCatalogs)
- Správa souborů (getImageUrl, getFileUrl, hasFile, hasImage)
- Automatické nastavení pozice při vytváření

#### CatalogOrder.php - Entita objednávek
- Kompletní validace objednávkových dat
- Validace českého IČO s kontrolním součtem
- Formátování IČO (XX XXX XXX)
- Statické metody pro správu objednávek (getAllOrders, getOrdersByCatalog)
- Vyhledávání a filtrování objednávek
- Export do CSV
- Statistiky objednávek

#### CatalogConfig.php - Konfigurace modulu
- Statické metody pro rychlý přístup (get, set, getBool, getInt, getArray)
- Cache mechanismus pro výkon
- Inicializace výchozích hodnot
- Skupinování konfigurací pro admin rozhraní
- Validace konfiguračních hodnot

#### Testování
- Vytvořeny kompletní testy pro všechny entity
- Testy prošly úspěšně (36 úspěšných testů, 0 chyb)
- Ověřena funkčnost všech metod a validací
- Mock objekty pro nezávislé testování

Všechny entity jsou plně kompatibilní s PrestaShop 8.2.0 a obsahují:
- ✅ Správnou validaci dat
- ✅ Multijazyčnou podporu
- ✅ Optimalizované databázové operace
- ✅ Bezpečnostní kontroly
- ✅ Kompletní dokumentaci
- ✅ Funkční testy

### 8. Instalační a deinstalační skripty - DOKONČENO ✅

Vytvořeny kompletní SQL skripty pro správu databáze modulu:

#### install.sql - Instalační skript
- ✅ Vytvoření všech databázových tabulek s správnými datovými typy
- ✅ Nastavení optimálních indexů pro výkon (position, active, date_add)
- ✅ Foreign key constraints pro referenční integritu
- ✅ UTF8MB4 charset pro emoji podporu
- ✅ Vložení 20 výchozích konfiguračních hodnot
- ✅ Vytvoření ukázkového katalogu s multilingual podporou
- ✅ Automatická detekce existujících dat

#### uninstall.sql - Deinstalační skript
- ✅ Bezpečné odstranění všech tabulek v správném pořadí
- ✅ Odstranění foreign key constraints před drop tabulek
- ✅ Vyčištění konfiguračních hodnot z PS tabulek
- ✅ Odstranění hook registrací
- ✅ Poznámky pro manuální vyčištění souborů

#### upgrade.sql - Migrace systém
- ✅ Verze kontrola pro postupné aktualizace
- ✅ Dynamické přidávání nových indexů a sloupců
- ✅ Data integrity kontroly a opravy
- ✅ Rollback mechanismus
- ✅ Logging upgrade operací

#### backup.sql - Zálohovací systém
- ✅ Automatické vytváření backup tabulek s časovým razítkem
- ✅ Backup všech tabulek modulu
- ✅ Logging backup operací
- ✅ Informace o vytvořených backup tabulkách

#### restore.sql - Obnovení dat
- ✅ Template pro obnovení dat ze zálohy
- ✅ Bezpečnostní kontroly před přepsáním dat
- ✅ Detailní instrukce pro manuální použití
- ✅ Logging restore operací

#### integrity_check.sql - Diagnostika
- ✅ Kontrola orphaned záznamů
- ✅ Validace foreign key vztahů
- ✅ Kontrola duplicitních pozic
- ✅ Validace email adres v objednávkách
- ✅ Kontrola neplatných cest k souborům
- ✅ Ověření kompletnosti konfigurace
- ✅ Souhrnný report integrity

#### README.md - Dokumentace
- ✅ Kompletní dokumentace všech SQL skriptů
- ✅ Návody na použití pro různé scénáře
- ✅ Troubleshooting guide
- ✅ Bezpečnostní doporučení
- ✅ Popis databázové struktury

Všechny SQL skripty jsou:
- ✅ Kompatibilní s PrestaShop 8.2.0
- ✅ Optimalizované pro výkon
- ✅ Bezpečné s proper error handling
- ✅ Dokumentované s komentáři
- ✅ Testovatelné a rollback schopné

## Závislosti
- Krok 1: Základní struktura

### 9. Migrace a aktualizace systém - DOKONČENO ✅

Implementován kompletní migrační systém pro správu verzí a aktualizací modulu:

#### MigrationManager.php - Správce migrací
- ✅ Detekce aktuální verze modulu z databáze
- ✅ Automatické spouštění potřebných migrací v správném pořadí
- ✅ Rollback mechanismus pro neúspěšné migrace
- ✅ Logging všech migračních operací do databáze
- ✅ Automatický backup před každou migrací
- ✅ Validace integrity dat po migraci
- ✅ Výpočet migračních cest mezi verzemi
- ✅ Čištění starých logů (zachování posledních 50)

#### AbstractMigration.php - Základní třída
- ✅ Helper metody pro databázové operace
- ✅ Bezpečné přidávání/odebírání sloupců a indexů
- ✅ Správa konfiguračních hodnot
- ✅ Validace existence tabulek, sloupců a indexů
- ✅ Error handling s detailním reportingem
- ✅ Debug mode pro vývojáře

#### Konkrétní migrace
- ✅ **Migration_1_0_0_to_1_1_0.php** - Cache a SEO optimalizace
  - Přidání cache konfigurací (enabled, lifetime, key prefix)
  - SEO sloupce (meta_title, meta_description, slug)
  - Performance indexy (title+active, date_add, position+active)
  - Automatické generování slugů z existujících dat
  - Unique index pro slugy s řešením duplicit

- ✅ **Migration_1_1_0_to_1_2_0.php** - Podpora kategorií
  - Nové tabulky cig_catalog_category a cig_catalog_category_lang
  - Foreign key constraints s CASCADE operacemi
  - 5 výchozích kategorií s multijazyčnou podporou
  - Konfigurace pro kategorie (tree depth, colors, filtering)
  - Indexy pro optimální výkon kategorizace

- ✅ **Migration_1_2_0_to_1_3_0.php** - Pokročilé SEO a analytics
  - Nová tabulka cig_catalog_analytics pro tracking
  - Open Graph meta tagy (og_title, og_description, og_image)
  - Twitter Cards (twitter_title, twitter_description, twitter_image)
  - Schema.org structured data s JSON validací
  - Analytics konfigurace (Google Analytics, GTM, Facebook Pixel)
  - Tracking views, downloads, orders s device detection

#### Testování migračního systému
- ✅ Kompletní test suite pro všechny komponenty
- ✅ Test instantiation všech migračních tříd
- ✅ Validace verzí a povinných metod
- ✅ Test detekce migračních souborů
- ✅ Test výpočtu migračních cest
- ✅ Rollback funkčnost testy
- ✅ Error handling a integrity testy
- ✅ Mock databáze pro nezávislé testování

#### Dokumentace a návody
- ✅ Kompletní README.md s návody na použití
- ✅ Příklady vytváření nových migrací
- ✅ Troubleshooting guide pro časté problémy
- ✅ Bezpečnostní doporučení a best practices
- ✅ Performance optimalizace a monitoring

Migrační systém je plně funkční a připravený pro produkční použití s:
- ✅ Automatickými zálohami před každou migrací
- ✅ Rollback mechanismem při chybách
- ✅ Kompletním loggingem všech operací
- ✅ Data integrity kontrolami
- ✅ Debug módem pro vývojáře
- ✅ Comprehensive error handling

## Následující krok
Krok 3: Repository pattern a datová vrstva
